package com.kuaidizs.jxc.ai.xxljob.ai;

import com.alibaba.fastjson.JSONObject;
import com.kuaidizs.jxc.common.diamond.DiamondConfigConstant;
import com.kuaidizs.jxc.common.util.Constant;
import com.kuaidizs.jxc.common.util.DateUtil;
import com.kuaidizs.jxc.common.util.LogHelper;
import com.kuaidizs.jxc.dao.UserDAO;
import com.kuaidizs.jxc.dao.ai.AIPrintRuleDAO;
import com.kuaidizs.jxc.dao.user.UserGroupConnectionDAO;
import com.kuaidizs.jxc.domain.User;
import com.kuaidizs.jxc.domain.ai.AIItem;
import com.kuaidizs.jxc.domain.trade.PrintVersionRecord;
import com.kuaidizs.jxc.domain.user.UserGroupConnection;
import com.kuaidizs.jxc.query.trade.TradeQuery;
import com.kuaidizs.jxc.query.user.UserGroupConnectionQuery;
import com.kuaidizs.jxc.request.ai.ReqSkuSimilaritySearchDTO;
import com.kuaidizs.jxc.response.ai.RspSkuSimilaritySearchDTO;
import com.kuaidizs.jxc.service.ai.AIItemService;
import com.kuaidizs.jxc.service.ai.AISkuSimilarService;
import com.kuaidizs.jxc.service.trade.ApiBean.SkuBean;
import com.kuaidizs.jxc.service.trade.PrintVersionRecordService;
import com.kuaidizs.jxc.service.trade.SyncTradeService;
import com.raycloud.bizlogger.Logger;
import com.taobao.api.domain.Order;
import com.taobao.api.domain.Trade;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 商品相似度统计
 */
@Component
@JobHandler("aIItemSimilarJobHandler")
public class AIItemSimilarJobHandler extends IJobHandler {

    private static final Logger logger = Logger.getLogger(AIItemSimilarJobHandler.class);

    @Resource
    private UserDAO userDAO;
    @Resource
    private AIPrintRuleDAO aiPrintRuleDAO;
    @Resource
    private AIItemService aiItemService;
    @Resource
    private SyncTradeService syncTradeService;
    @Resource
    private AISkuSimilarService aiSkuSimilarService;
    @Resource
    private UserGroupConnectionDAO userGroupConnectionDAO;
    @Resource
    private PrintVersionRecordService printVersionRecordService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Date now = new Date();

        List<Long> groupIdList = new ArrayList<>(); // 获取所有关联店铺分组id

        if (StringUtils.isNotBlank(s)) {
            groupIdList = Stream.of(s.split(",")).mapToLong(Long::parseLong).boxed().collect(Collectors.toList());
        } else {
            List<Long> userIdList = aiPrintRuleDAO.getSimilarItemUser();

            for (Long userId : userIdList) {
                List<UserGroupConnection> userGroupConnectionList = userGroupConnectionDAO.getUserGroupConnectionByGroupId(null, userId);

                if (CollectionUtils.isNotEmpty(userGroupConnectionList)) { // 有关联组的情况
                    Long groupId = userGroupConnectionList.get(0).getGroupId();

                    if (!groupIdList.contains(groupId)) {
                        groupIdList.add(groupId);
                    }
                } else { // 没有关联组的情况
                    groupIdList.add(userId);
                }
            }
        }

        for (Long groupId : groupIdList) {
            List<AIItem> allAIItemList = new ArrayList<>(); // 所有有效关联店铺的商品集合
            List<Long> allUserList = new ArrayList<>(); // 当前关联组下所有有效店铺集合
            Map<Long, User> userMap = new HashMap<>();

            logger.biz("分组：{} 统计商品开始", groupId);

            // 查询当前关联组下所有有效店铺 & 同步商品 & 获取所有有效店铺的商品
            UserGroupConnectionQuery userGroupConnectionQuery = new UserGroupConnectionQuery();
            userGroupConnectionQuery.setGroupId(groupId);

            List<UserGroupConnection> userGroupConnectionList = userGroupConnectionDAO.getUserGroupConnectionList(userGroupConnectionQuery);

            if (CollectionUtils.isNotEmpty(userGroupConnectionList)) {
                for (UserGroupConnection userGroupConnection : userGroupConnectionList) {
                    syncItemByUser(userGroupConnection.getTaobaoId(), groupId, now, allUserList, userMap, allAIItemList);
                }
            } else {
                // 没有关联组的情况下，groupId 其实就是 taobaoId
                syncItemByUser(groupId, groupId, now, allUserList, userMap, allAIItemList);
            }

            processAndSaveAIItems(allAIItemList, userMap, groupId); // 处理商品相似度 & 存储到数据库

            logger.biz("分组：{} 统计商品结束", groupId);
        }

        return ReturnT.SUCCESS;
    }

    private void syncItemByUser(Long taobaoId, Long groupId, Date now, List<Long> allUserList, Map<Long, User> userMap, List<AIItem> allAIItemList) throws Exception {
        User user = userDAO.getUserByTaobaoId(taobaoId);

        PrintVersionRecord printVersionRecord = printVersionRecordService.getPrintVersionRecordByTaobaoId(user);

        if (user != null && printVersionRecord != null && printVersionRecord.getTopExpired() != null && printVersionRecord.getTopExpired().after(now)) {
            allUserList.add(user.getTaobaoId());
            userMap.put(user.getTaobaoId(), user);

            processUserItemSync(user, groupId); // 从订单中获取商品信息 & 转换成向量 & 插入商品信息到数据库中

            logger.biz(LogHelper.buildLogHead(user).append("开始统计用户商品"));

            List<AIItem> aiItemList = aiItemService.getAIItemListByTaobaoId(user);

            for (AIItem aiItem : aiItemList) {
                if (aiItem != null && aiItem.getNoGroup() == 0) { // 参与分组，1不参与分组
                    allAIItemList.add(aiItem);
                }
            }
        } else {
            logger.biz(LogHelper.buildLogHead(user).append("未参与统计"));
        }
    }


    /**
     * 从订单中获取商品信息 & 转换成向量 & 插入商品信息到数据库中
     */
    private void processUserItemSync(User user, Long groupId) {
        try {
            logger.biz(LogHelper.buildLogHead(user).append("统计订单商品信息开始"));

            Map<String, Integer> skuCountMap = new HashMap<>();
            this.deleteAIItem(user); // 同步前删除所有商品
            Map<String, SkuBean> skuBeanMap = this.getItemFromTrade(user, skuCountMap); // 从订单中 获取商品信息
            boolean isVec = this.sku2Vec(user, skuBeanMap, groupId); // 商品转换成向量
            this.insertAIItem(user, skuBeanMap, isVec); // 插入商品信息到数据库中
            this.tradeCount(skuCountMap, user); // 插入商品订单数量
        } catch (Exception e) {
            logger.error("统计订单商品信息失败：{},{},{}", user.getTaobaoId(), user.getTaobaoNick(), e.getMessage(), e);
        }

        logger.biz(LogHelper.buildLogHead(user).append("统计订单商品信息结束"));
    }

    /**
     * 同步前删除所有商品，产品需求
     */
    private void deleteAIItem(User user) {
        aiItemService.deleteAIItemList(user);
    }

    /**
     * 从订单中 获取商品信息
     */
    private Map<String, SkuBean> getItemFromTrade(User user, Map<String, Integer> skuCountMap) throws Exception {
        Map<String, SkuBean> snsMap = new HashMap<>();

        Date startDate = DateUtil.getDayStart(new Date(), -7);
        Date endDate = DateUtil.getDayStart(new Date(), 1);

        TradeQuery tradeQuery = new TradeQuery();
        tradeQuery.setTimeType(1);
        tradeQuery.setStartTime(startDate);
        tradeQuery.setEndTime(endDate);
        tradeQuery.setStatus(Constant.DEFAULT_TRADE_STATUS);
        tradeQuery.setLogQueryType(1);
        tradeQuery.setRefundStatus(0);

        int pageNo = 1;

        Integer maxTradeNum = 50000;

        // 查询最近七天没有退款的订单
        List<Trade> tradeList = syncTradeService.queryTradeForAI(user, tradeQuery, pageNo, startDate, endDate, maxTradeNum);

        for (Trade trade : tradeList) {
            List<Order> orders = trade.getOrders();
            for (Order order : orders) {
                order.setSkuId(StringUtils.isNotBlank(order.getSkuId()) ? order.getSkuId() : "0");

                String snsKey = user.getTaobaoId() + order.getNumIid() + order.getSkuId();
                if (snsMap.get(snsKey) == null) {
                    SkuBean skuBean = new SkuBean();
                    skuBean.setTaobaoId(user.getTaobaoId());
                    skuBean.setNumIid(order.getNumIid() + "");
                    skuBean.setTitle(order.getTitle());
                    skuBean.setPicUrl(order.getPicPath());
                    skuBean.setSkuOuterId(order.getOuterSkuId());
                    skuBean.setSkuId(order.getSkuId());
                    skuBean.setSkuName(order.getSkuPropertiesName());

                    snsMap.put(snsKey, skuBean);
                }

                // 统计 SKU 出现次数
                String skuKey = user.getTaobaoId() + "_" + order.getNumIid() + "_" + order.getSkuId();
                skuCountMap.put(skuKey, skuCountMap.getOrDefault(skuKey, 0) + 1);
            }
        }

        return snsMap;
    }

    private void insertAIItem(User user, Map<String, SkuBean> skuBeanMap, boolean isVec) {
        if (skuBeanMap == null || skuBeanMap.isEmpty()) {
            return; // 空数据直接返回
        }

        Date now = new Date();
        List<AIItem> aiItemList = new ArrayList<>();

        for (String skuKey : skuBeanMap.keySet()) {
            SkuBean skuBean = skuBeanMap.get(skuKey);

            AIItem aiItem = aiItemService.getAIItemByNumIIdAndSkuId(user, skuBean.getNumIid(), skuBean.getSkuId()); // 判断数据库中是否存在该商品

            if (aiItem == null) {
                AIItem addAiItem = new AIItem();
                addAiItem.setTaobaoId(skuBean.getTaobaoId());
                addAiItem.setNumIid(skuBean.getNumIid());
                addAiItem.setTitle(skuBean.getTitle());
                addAiItem.setPicUrl(skuBean.getPicUrl());
                addAiItem.setSkuId(skuBean.getSkuId());
                addAiItem.setSkuOuterId(skuBean.getSkuOuterId());
                addAiItem.setSkuName(skuBean.getSkuName());
                addAiItem.setIsVec(isVec ?  1 : 0);
                addAiItem.setCreated(now);
                addAiItem.setModified(now);

                aiItemList.add(addAiItem);
            }
        }

        if (!aiItemList.isEmpty()) {
            try {
                aiItemService.addAIItemAll(user, aiItemList); // 批量插入商品
            } catch (Exception e) {
                logger.error("批量插入 aiItem 失败，" + e.getMessage(), e);
            }
        }
    }

    /**
     * 商品 sku 转成向量
     */
    private boolean sku2Vec(User user, Map<String, SkuBean> skuBeanMap, Long groupId) {
        boolean flag = false;

        List<SkuBean> skuBeanS = new ArrayList<>(skuBeanMap.values());

        try {
            aiSkuSimilarService.sku2Vec(skuBeanS, user.getTaobaoId(), groupId);
            flag = true;
        } catch (Exception e) {
            logger.error("商品 sku 转成向量失败：{},{},{}", user.getTaobaoId(), user.getTaobaoNick(), e.getMessage(), e);
        }

        return flag;
    }


    /**
     * 商品订单数量统计
     */
    private void tradeCount(Map<String, Integer> skuCountMap, User user) {
        for (Map.Entry<String, Integer> entry : skuCountMap.entrySet()) {
            String taobaoId = entry.getKey().split("_")[0];
            String numIid = entry.getKey().split("_")[1];
            String skuId = entry.getKey().split("_")[2];

            AIItem aiItem = new AIItem();
            aiItem.setNumIid(numIid);
            aiItem.setSkuId(skuId);
            aiItem.setTaobaoId(Long.valueOf(taobaoId));
            aiItem.setTradeCount(entry.getValue());

            aiItemService.updateAiItem(user, aiItem);
        }
    }

    /**
     * 处理商品相似度 & 存储到数据库
     */
    private void processAndSaveAIItems(List<AIItem> allAIItemList, Map<Long, User> userMap, Long groupId) {
        int similarity = DiamondConfigConstant.GLOBAL_LIMIT_CONFIG.getIntValue("similarity");
        float titleWeight = DiamondConfigConstant.GLOBAL_LIMIT_CONFIG.getFloatValue("titleWeight");
        float skuNameWeight = DiamondConfigConstant.GLOBAL_LIMIT_CONFIG.getFloatValue("skuNameWeight");
        float picUrlWeight = DiamondConfigConstant.GLOBAL_LIMIT_CONFIG.getFloatValue("picUrlWeight");

        logger.biz("统计订单商品信息开始,similarity:{},titleWeight:{},skuNameWeight:{},picUrlWeight:{}", similarity, titleWeight, skuNameWeight, picUrlWeight);

        for (AIItem aiItem : allAIItemList) {
            if (aiItem == null) continue;

            ReqSkuSimilaritySearchDTO request = new ReqSkuSimilaritySearchDTO();
            request.setNumIid(aiItem.getNumIid());
            request.setSkuId(aiItem.getSkuId());
            request.setSellerId(aiItem.getTaobaoId());
            request.setSimilarity(similarity);
            request.setTitleWeight(titleWeight);
            request.setSkuNameWeight(skuNameWeight);
            request.setPicUrlWeight(picUrlWeight);

            // 查找所有关联店铺下的相似商品
            List<RspSkuSimilaritySearchDTO> searchDTOList = aiSkuSimilarService.similarSearchBatchUser(request, groupId);

            if (CollectionUtils.isNotEmpty(searchDTOList)) {
                List<String> keyList = new ArrayList<>();
                for (RspSkuSimilaritySearchDTO searchDTO : searchDTOList) {
                    String key = searchDTO.getSellerId() + "_" + searchDTO.getNumIid() + "_" + searchDTO.getSkuId();
                    keyList.add(key);
                }

                if (CollectionUtils.isNotEmpty(keyList)) {
                    aiItem.setSimilarGroup(JSONObject.toJSONString(keyList));
                }

                User user = userMap.get(aiItem.getTaobaoId());
                if (user != null) {
                    logger.biz("修改图片：" + JSONObject.toJSONString(aiItem));

                    aiItemService.updateAiItem(user, aiItem);
                }
            }
        }

        logger.biz("统计订单商品信息结束");
    }

}
