package com.kuaidizs.jxc.ai.xxljob.ai;

import com.kuaidizs.jxc.common.util.DataBaseUtil;
import com.xxl.job.core.handler.IJobHandler;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public abstract class AbstractDeleteTempDataHandler extends IJobHandler {
    protected static final List<String> DEFAULT_FK_IDS = Arrays.asList("0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23");
    protected static final List<String> DEFAULT_TABLE_INDEXS = new ArrayList<>(DataBaseUtil.AI_TRADE_SPLIT);
    protected static final int DEFAULT_INTERVAL_DAYS = 3;

    static {
        for (int i = 0; i < DataBaseUtil.AI_TRADE_SPLIT; i++) {
            DEFAULT_TABLE_INDEXS.add(i + "");
        }
    }
}
