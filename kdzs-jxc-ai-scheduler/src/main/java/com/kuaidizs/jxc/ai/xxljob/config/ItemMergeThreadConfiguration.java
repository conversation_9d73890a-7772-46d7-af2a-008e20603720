package com.kuaidizs.jxc.ai.xxljob.config;

import com.alibaba.ttl.threadpool.TtlExecutors;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.kuaidizs.jxc.common.constant.ItemConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
@Configuration
public class ItemMergeThreadConfiguration implements BeanFactoryAware {

    private BeanFactory beanFactory;

    @PostConstruct
    public void init() {
        System.out.println("-----------开始线程池初始化!-----------");

        ThreadPoolExecutor similarSkuExecutor = new ThreadPoolExecutor(2, 32, 30L,
                TimeUnit.SECONDS, new SynchronousQueue<>(), new ThreadFactoryBuilder().setNameFormat("similar-sku-pool-%d").build(), new ThreadPoolExecutor.CallerRunsPolicy());

        //同款商品推荐组合货品解析线程池
        ItemConstant.similarSkuExecutor = TtlExecutors.getTtlExecutorService(similarSkuExecutor);

        //优雅停机
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println(">>>>>>>>>>> kdzs-ai-task shutdown<<<<<<<<<<<<<<");
            ItemConstant.similarSkuExecutor.shutdown();
        }));

        System.out.println("-----------线程池初始化完成!-----------");

    }

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }
}
