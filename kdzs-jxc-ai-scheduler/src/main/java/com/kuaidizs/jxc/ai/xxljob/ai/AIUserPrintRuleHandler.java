package com.kuaidizs.jxc.ai.xxljob.ai;

import com.kuaidizs.jxc.common.db.Result;
import com.kuaidizs.jxc.common.util.*;
import com.kuaidizs.jxc.dao.UserDAO;
import com.kuaidizs.jxc.domain.User;
import com.kuaidizs.jxc.domain.ai.AIPrintRule;
import com.kuaidizs.jxc.domain.trade.PrintVersionRecord;
import com.kuaidizs.jxc.query.UserQuery;
import com.kuaidizs.jxc.service.UserService;
import com.kuaidizs.jxc.service.ai.AIPrintRuleService;
import com.kuaidizs.jxc.service.trade.PrintVersionRecordService;
import com.raycloud.bizlogger.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 根据打印记录统计操作习惯
 */
@Component
@JobHandler("aIUserPrintRuleHandler")
public class AIUserPrintRuleHandler extends IJobHandler {

    private static final Logger logger = Logger.getLogger(AIUserPrintRuleHandler.class);

    private static final int INTERVAL_DAYS = 7;

    @Resource
    private UserDAO userDAO;
    @Resource
    private AIPrintRuleService aiPrintRuleService;
    @Resource
    private PrintVersionRecordService printVersionRecordService;
    @Resource
    private UserService userService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Date now = new Date();

        if (StringUtils.isNotBlank(s)) {
            List<User> userList = userDAO.getUsersBytaobaoIds(Stream.of(s.split(",")).map(String::trim).mapToLong(Long::parseLong).boxed().collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(userList)) {
                for (User user : userList) {
                    logger.biz(LogHelper.buildLogHead(user).append("开始统计打印记录操作习惯"));

                    try {
                        //重新从userService获取用户信息，不然信息不完整
                        User tempUser = userService.getUserByTaobaoId(user.getTaobaoId());
                        aiPrintRuleService.calculateAiPrintRule(tempUser, INTERVAL_DAYS);
                    } catch (Exception e) {
                        logger.error(LogHelper.buildLogHead(user).append("统计打印记录操作习惯异常"), e);
                    }
                }
            }
        } else {
            int userPageNo = 1;
            int userPageSize = 100;

            UserQuery userQuery = new UserQuery();
            userQuery.setEnableStatus(true);
            userQuery.setPage(userPageNo);
            userQuery.setPageSize(userPageSize);

            Result<User> userResult = userDAO.getUserListWithPage(userQuery, false);

            while (userResult != null && CollectionUtils.isNotEmpty(userResult.getList())) {
                for (User user : userResult.getList()) {

                    PrintVersionRecord printVersionRecord = printVersionRecordService.getPrintVersionRecordByTaobaoId(user);

                    if (user != null && printVersionRecord != null && printVersionRecord.getTopExpired() != null && printVersionRecord.getTopExpired().after(now) && user.getEnableStatus()) {
                        logger.biz(LogHelper.buildLogHead(user).append("开始统计打印记录操作习惯"));

                        AIPrintRule aiPrintRule = aiPrintRuleService.getPrintRuleWithoutDefault(user);

                        if (aiPrintRule != null) {
                            continue;
                        }

                        try {
                            //重新从userService获取用户信息，不然信息不完整
                            User tempUser = userService.getUserByTaobaoId(user.getTaobaoId());
                            aiPrintRuleService.calculateAiPrintRule(tempUser, INTERVAL_DAYS);
                        } catch (Exception e) {
                            logger.error(LogHelper.buildLogHead(user).append("统计打印记录操作习惯异常"), e);
                        }
                    }
                }

                logger.biz("统计打印记录操作习惯 page:{},pageSize:{}", userPageNo, userPageSize);

                userPageNo++;
                userQuery.setPage(userPageNo);

                userResult = userDAO.getUserListWithPage(userQuery, false);
            }
        }

        return ReturnT.SUCCESS;
    }
}
