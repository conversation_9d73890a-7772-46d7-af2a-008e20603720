package com.kuaidizs.jxc.ai.xxljob.ai;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kuaidizs.jxc.common.util.DateUtil;
import com.kuaidizs.jxc.query.ai.AiTempDataDeleteQuery;
import com.kuaidizs.jxc.service.ai.AiTempDataService;
import com.raycloud.bizlogger.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.JobHandler;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@JobHandler("aiTempDataDeleteHandler")
public class AiTempDataDeleteHandler extends AbstractDeleteTempDataHandler {
    private static final Logger logger = Logger.getLogger(AiTempDataDeleteHandler.class);

    @Autowired
    private AiTempDataService aiTempDataService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {

        logger.biz("[AiTempDataDeleteHandler] start param : {}", param);

        List<String> fkIds = DEFAULT_FK_IDS;
        List<String> tableIndexs = DEFAULT_TABLE_INDEXS;
        Date todayStart = DateUtil.getTodayStart();
        Date createdEnd = DateUtil.addDateAppointDay(todayStart, -DEFAULT_INTERVAL_DAYS);

        if (StringUtils.isNotBlank(param)) {
            JSONObject json = JSON.parseObject(param);
            if (json.containsKey("fkIds")) {
                fkIds = JSON.parseArray(json.getString("fkIds"), String.class);
            }
            if (json.containsKey("tableIndexs")) {
                tableIndexs = JSON.parseArray(json.getString("tableIndexs"), String.class);
            }
            if (json.containsKey("createdEnd")) {
                createdEnd = json.getDate("createdEnd");
            } else {
                if (json.containsKey("intervalDays")) {
                    createdEnd = DateUtil.addDateAppointDay(todayStart, -json.getInteger("intervalDays"));
                }
            }
        }

        AiTempDataDeleteQuery query = new AiTempDataDeleteQuery();
        query.setCreatedEnd(createdEnd);

        for (String fkId : fkIds) {
            query.setFkId(fkId);
            for (String tableIndex : tableIndexs) {
                query.setTableIndex(tableIndex);

                aiTempDataService.deleteAiTrade(query);

                aiTempDataService.deleteAiOrder(query);

                aiTempDataService.deleteAiSortTrade(query);

                aiTempDataService.deleteAiSortOrder(query);
            }

            aiTempDataService.deleteAiSortTradeStatisticsOfCategory(query);

            aiTempDataService.deleteAiSortTradeStatisticsOfItem(query);

            aiTempDataService.deleteAiSortTradeStatisticsOfSku(query);
        }

        return ReturnT.SUCCESS;
    }
}
