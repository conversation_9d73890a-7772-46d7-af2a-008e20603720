package com.kuaidizs.web.db.router.rules.support;

import org.apache.log4j.Logger;


public class HashFunction {

	private static final Logger logger = Logger.getLogger(HashFunction.class);

	/**
	 * 0标识1库，1标识2库
	 * @param fkId
	 * @return
	 */

	public Long apply(Long fkId) {
//		logger.debug("fk router input = " + fkId);
		if(fkId == null){
			return 1L;
		}else if(fkId.longValue() == 0L){
			return 1L;
		}else if(fkId.longValue() == 1L){
			return 2L;
		}else if(fkId.longValue() == 2L){
			return 3L;
		}else if(fkId.longValue() == 3L){
			return 4L;
		}else if(fkId.longValue() == 4L){
			return 5L;
		}else if(fkId.longValue() == 5L){
			return 6L;
		}else if(fkId.longValue() == 6L){
			return 7L;
		}else if(fkId.longValue() == 7L){
			return 8L;
		}else if(fkId.longValue() == 8L){
			return 9L;
		}else if(fkId.longValue() == 9L){
			return 10L;
		}else if(fkId.longValue() == 10L){
			return 11L;
		}else if(fkId.longValue() == 11L){
			return 12L;
		}else if(fkId.longValue() == 12L){
			return 13L;
		}else if(fkId.longValue() == 13L){
			return 14L;
		}else if(fkId.longValue() == 14L){
			return 15L;
		}else if(fkId.longValue() == 15L){
			return 16L;
		}else if(fkId.longValue() == 16L){
			return 17L;
		}else if(fkId.longValue() == 17L){
			return 18L;
		}else if(fkId.longValue() == 18L){
			return 19L;
		}else if(fkId.longValue() == 19L){
			return 20L;
		}else if(fkId.longValue() == 20L){
			return 21L;
		}else if(fkId.longValue() == 21L){
			return 22L;
		}else if(fkId.longValue() == 22L){
			return 23L;
		}else if(fkId.longValue() == 23L){
			return 24L;
		}else{
			return 1L;
		}
	}

}
