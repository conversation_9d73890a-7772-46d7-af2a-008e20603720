<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:task="http://www.springframework.org/schema/task"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="
        http://www.springframework.org/schema/task http://www.springframework.org/schema/task/spring-task-3.1.xsd
        http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-2.5.xsd
        http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
    <context:annotation-config/>
    <!-- 使用annotation 自动注册bean,并检查@Required,@Autowired的属性已被注入 -->
    <!--<context:component-scan base-package="com.raycloud" />-->
    <context:component-scan base-package="com.kuaidizs"/>

    <task:annotation-driven/>


    <bean id="domainConfig" class="com.kuaidizs.jxc.common.domain.DomainConfig">
        <property name="sdDomain" value="${sd.domain}"/>
    </bean>

    <bean id="redissonClient" class="com.kuaidizs.jxc.service.hotitem.RedissonConfiguration" factory-method="init"></bean>

</beans>