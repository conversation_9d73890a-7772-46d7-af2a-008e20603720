<?xml version="1.0" encoding="UTF-8"?>
<beans default-autowire="byName" xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd
        ">
    <dubbo:application name="kdzs-jxc-ai-scheduler"/>

    <!--<dubbo:registry protocol="zookeeper" address="${zkHost}"/>-->
    <dubbo:registry address="multicast://*********:1234"/>

    <!--
    <bean id="dbPasswordCallback" class="com.raycloud.dBPasswordCallback.NsDbPasswordCallback">
        <property name="secretRequest" ref="secretRequest"/>
    </bean>
    -->
    <dubbo:reference filter="rds_trade_access" id="SyncRDSTradeGetRequest"
                     interface="com.raycloud.data.api.SyncRDSTradeGetRequest"
                     version="${trade_rds_sync_version}" retries="1"/>
    <dubbo:reference filter="rds_trade_access"  id="SyncUserRegistRequest" interface="com.raycloud.data.api.SyncUserRegistRequest" check="false"
                     init="false" version="${trade_rds_sync_version}" retries="1"/>
    <dubbo:reference filter="rds_trade_access"  id="syncUserGetRequest" interface="com.raycloud.data.api.SyncUserGetRequest" check="false"
                     init="false" version="${trade_rds_sync_version}" retries="1"/>
    <dubbo:reference filter="rds_trade_access" id="rdsUserRegistRequest" retries="1" interface="com.raycloud.data.api.RDSUserRegistRequest" check="false"
                     init="false" version="${trade_rds_sync_version}"/>

    <!--加密服务-->
    <dubbo:reference check="false" init="false" id="secretRequest" url="121.41.227.130:30003"
                     interface="com.raycloud.secret_api.api.SecretRequest"/>

    <dubbo:reference check="false" init="false" id="itemService" interface="com.raycloud.items.IItemSearcher"
                     url="dubbo://121.41.168.151:30003/com.raycloud.items.IItemSearcher" loadbalance="rcconsistenthash"
                     version="1.1.0-test" timeout="20000"/>

    <dubbo:reference check="false" init="false" id="itemSkuService" interface="com.raycloud.items.IItemSkuSearcher"
                     url="dubbo://121.41.168.151:30003/com.raycloud.items.IItemSkuSearcher"
                     loadbalance="rcconsistenthash" version="1.1.0-test" timeout="20000"/>

    <!-- 疫情地区服务 -->
    <dubbo:reference init="false" check="false" id="epidemicAreaService"
                     interface="com.kuaidizs.express.service.api.IEpidemicAreaService"
                     version="${dubbo.express.version}"
                     timeout="5000"/>

    <!-- 地址查询服务 -->
    <dubbo:reference init="false" check="false" id="areaMiddleService"
                     interface="com.kuaidizs.express.service.api.IAreaService"
                     version="${dubbo.express.version}"
                     timeout="5000"/>

</beans>
