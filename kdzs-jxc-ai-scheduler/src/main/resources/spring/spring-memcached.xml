<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:cache="http://www.springframework.org/schema/cache"

       xsi:schemaLocation="http://www.springframework.org/schema/beans
         http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
         http://www.springframework.org/schema/context
         http://www.springframework.org/schema/context/spring-context-3.2.xsd
         http://www.springframework.org/schema/aop
         http://www.springframework.org/schema/aop/spring-aop-3.2.xsd
         http://www.springframework.org/schema/cache
           http://www.springframework.org/schema/cache/spring-cache-3.1.xsd
        http://www.springframework.org/schema/mvc
        http://www.springframework.org/schema/mvc/spring-mvc-3.2.xsd">

    <aop:aspectj-autoproxy proxy-target-class="true"/>
    <context:annotation-config />
    <cache:annotation-driven cache-manager="cacheManager" proxy-target-class="true"/>
    <context:component-scan base-package="com.kuaidizs" />
    <!--<context:component-scan base-package="com.google.code.ssm"/>-->

    <bean name="cacheManager" class="com.google.code.ssm.spring.ExtendedSSMCacheManager">
        <property name="caches">
            <set>
                <bean class="com.google.code.ssm.spring.SSMCache">
                    <qualifier value="jxcCache"/>
                    <constructor-arg name="cache" index="0" ref="jxcCache" />
                    <!-- 5 minutes -->
                    <constructor-arg name="expiration" index="1" value="1800" />
                    <!-- @CacheEvict(..., "allEntries" = true) doesn't work -->
                    <!--<constructor-arg name="allowClear" index="2" value="false" />-->
                </bean>
            </set>
        </property>
    </bean>

    <!-- easy way to test different types of serialization mechanizm by passing it as system property (-Dssm.provider=JSON) -->
    <bean name="defaultSerializationTypeAsString" class="java.lang.String">
        <constructor-arg value="#{systemProperties['ssm.defaultSerializationType']?:'JSON'}" />
    </bean>

    <bean id="cacheBase" class="com.google.code.ssm.aop.CacheBase" />

    <bean name="jxcCache" class="com.google.code.ssm.CacheFactory" depends-on="cacheBase">
        <property name="cacheName" value="jxcCache" />
        <property name="cacheClientFactory" ref="cacheClientFactory"/>
        <property name="addressProvider">
            <bean class="com.google.code.ssm.config.DefaultAddressProvider">
                <property name="address" value="${memcached.addr}" />
            </bean>
        </property>
        <property name="configuration" ref="clientConfig" />
        <property name="defaultSerializationType"
                  value="#{T(com.google.code.ssm.api.format.SerializationType).valueOf(@defaultSerializationTypeAsString)}" />
    </bean>

    <bean name="cacheClientFactory" class="com.google.code.ssm.providers.spymemcached.MemcacheClientFactoryImpl" />
    <bean name="clientConfig" class="com.google.code.ssm.providers.CacheConfiguration">
        <property name="consistentHashing" value="true" />
        <property name="operationTimeout" value="2000"/>
        <property name="useBinaryProtocol" value="true"/>
    </bean>

    <!-- 一些情况下可以直接连接上memcached -->
    <bean id="memcachedClient" class="net.spy.memcached.spring.MemcachedClientFactoryBean">
        <property name="servers" value="${memcached.addr}"/>
        <property name="protocol" value="BINARY"/>
        <property name="transcoder">
            <bean class="net.spy.memcached.transcoders.SerializingTranscoder">
                <property name="compressionThreshold" value="1024"/>
            </bean>
        </property>
        <property name="opTimeout" value="2000"/>
        <property name="timeoutExceptionThreshold" value="2000"/>
        <property name="hashAlg">
            <value type="net.spy.memcached.DefaultHashAlgorithm">KETAMA_HASH</value>
        </property>
        <property name="locatorType" value="CONSISTENT"/>
        <property name="failureMode" value="Redistribute"/>
        <property name="useNagleAlgorithm" value="false"/>
    </bean>

    <!-- 一些情况下可以直接连接上memcached -->
    <bean id="memcachedBhdClient" class="net.spy.memcached.RayMemcachedClientFactoryBean">
        <property name="servers" value="${memcached.bhd.addr}"/>
        <property name="protocol" value="BINARY"/>
        <property name="transcoder">
            <bean class="net.spy.memcached.transcoders.SerializingTranscoder">
                <property name="compressionThreshold" value="1024"/>
            </bean>
        </property>
        <property name="opTimeout" value="2000"/>
        <property name="timeoutExceptionThreshold" value="2000"/>
        <property name="hashAlg">
            <value type="net.spy.memcached.DefaultHashAlgorithm">KETAMA_HASH</value>
        </property>
        <property name="locatorType" value="CONSISTENT"/>
        <property name="failureMode" value="Redistribute"/>
        <property name="useNagleAlgorithm" value="false"/>
        <property name="authDescriptor">
            <bean class="net.spy.memcached.auth.AuthDescriptor">
                <constructor-arg index="0">
                    <value>PLAIN</value>
                </constructor-arg>
                <constructor-arg index="1">
                    <bean class="net.spy.memcached.auth.PlainCallbackHandler">
                        <constructor-arg index="0">
                            <value>${memcached.bhd.name}</value>
                        </constructor-arg>
                        <constructor-arg index="1">
                            <value>${memcached.bhd.pwd}</value>
                        </constructor-arg>
                    </bean>
                </constructor-arg>
            </bean>
        </property>
    </bean>

</beans>