<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
     http://www.springframework.org/schema/beans/spring-beans-3.0.xsd">

    <!-- 触发器的配置 -->

    <bean name="startQuertz" lazy-init="false" autowire="no"
          class="org.springframework.scheduling.quartz.SchedulerFactoryBean">
<!--        <property name="dataSource" ref="dataSource"/>-->
        <property name="configLocation" value="classpath:/quartz.properties"/>
        <!--这个是必须的，QuartzScheduler 延时启动，应用启动完后 QuartzScheduler 再启动 -->
<!--        <property name="startupDelay" value="120" />-->
        <property name="jobFactory">
            <bean class="com.kuaidizs.jxc.ai.scheduler.LocalSpringBeanJobFactory"/>
        </property>
        <property name="triggers">
            <list>

<!--                <ref bean="userAccessTokenJobTrigger"/>-->

            </list>
        </property>
    </bean>


</beans>