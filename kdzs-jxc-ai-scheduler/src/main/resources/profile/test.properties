spring.profiles.active = ${activeProfile}
data.dubbo.filter=
memSessionVer=3.3.0_NOIP_LOCAL_CESHI

jdbc.password        = guanghua
jdbc.url             = **********************************************************************************************************************************************************************
jdbc.username        = kdzs
jdbc.maxActive		 = 10
jdbc.minIdle		 = 5
jdbc.maxWait		 = 60000

jdbc.x = test
jdbc.y = local

jdbc2.password        = guanghua
jdbc2.url             = **********************************************************************************************************************************************************************
jdbc2.username        = kdzs

jdbc2.x = test
jdbc2.y = local

env=test

#memcached.addr = 6c441d3bbc3e4901.m.jst.cnhzalicm10pub001.ocs.aliyuncs.com:11211
#memcached.name=6c441d3bbc3e4901
#memcached.pwd=HZH7Vgreju8PciRe
memcached.addr=***************:11211

zkHost=zoo1.kuaidizs.cn:30002,zoo2.kuaidizs.cn:30002,zoo3.kuaidizs.cn:30002
common.zookeeper.connect = zoo1.superboss.cc:30002,zoo2.superboss.cc:30002,zoo3.superboss.cc:30002
#ali.zkHost=ali-zk.superboss.cc:2181


#网点
dubbo.express.version = kdzs_express_platform_1.0.0_test
#智选物流
dubbo.express.match.version = 1.0.0_zxwl_server_test

twcn.dubbo = kdzs_twcn_cnMultiPlat_2.2.0_test
group.dubbo = kdzs_group_stage_1.0.0

trade_rds_sync_version =kdzs-super-sync-trade-ray-task-1.0.0
user_service_ver=1.0.0-ZS
kdzs.erp.dubbo=kdzs_erp_1.0_test

dubbo.wly.version=kdzs_wly_1.2.0

#创建手工单
dubbo.hand.version=kdzs.hand.1.0.0_test

#地址解析版本
kdzs.address.parse=1.0.0

#新版商品服务版本
dubbo.item.service.version=1.0.0-test

kdzs.special.dubbo=1.0.0

#crm违禁词
dubbo.crm.version=1.0-test

#p88888域名
sd.domain=p88888.kuaidizs.cn

#违禁词
dubbo.ocr.version=1.0.0-test

#自助版多平台单号分享
dubbo.share.version=1.0.0_share_test

#redis配置
spring.redis.host=***************
spring.redis.port=6379
spring.redis.password=hEShr1OfC190