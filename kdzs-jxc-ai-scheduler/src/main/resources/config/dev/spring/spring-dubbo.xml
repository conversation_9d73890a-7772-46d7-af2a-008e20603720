<?xml version="1.0" encoding="UTF-8"?>
<beans default-autowire="byName" xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd
        ">
    <dubbo:application name="kdzs-jxc-job-scheduler"/>

    <!--dev-->
    <!--<dubbo:registry protocol="zookeeper" address="${zkHost}"/>-->
    <dubbo:registry address="multicast://*********:1234"/>

    <!--
    <bean id="dbPasswordCallback" class="com.raycloud.dBPasswordCallback.NsDbPasswordCallback">
        <property name="secretRequest" ref="secretRequest"/>
    </bean>
    -->

    <!--加密服务-->
    <dubbo:reference check="false" init="false" id="secretRequest" url="**************:30003"
                     interface="com.raycloud.secret_api.api.SecretRequest"/>

    <dubbo:reference check="false" init="false" id="itemService" interface="com.raycloud.items.IItemSearcher"
                     url="dubbo://**************:30003/com.raycloud.items.IItemSearcher" loadbalance="rcconsistenthash"
                     version="1.1.0-test" timeout="20000"/>
    <dubbo:reference check="false" init="false" id="itemSkuService" interface="com.raycloud.items.IItemSkuSearcher"
                     url="dubbo://**************:30003/com.raycloud.items.IItemSkuSearcher"
                     loadbalance="rcconsistenthash" version="1.1.0-test" timeout="20000"/>

    <dubbo:reference check="false" init="false" id="msgJobRequest" url="***************:30002" retries="0"
                     interface="com.raycloud.api.msgjob.service.MsgJobRequest" version="1.1.0" timeout="500"/>
    <dubbo:reference check="false" init="false" id="msgCountRequest" url="***************:30002" retries="0"
                     interface="com.raycloud.api.msgjob.service.MsgCountRequest" version="1.1.0" timeout="500"/>
    <dubbo:reference check="false" init="false" id="msgJobAppTypeRequest" url="***************:30002" retries="0"
                     interface="com.raycloud.api.msgjob.service.MsgJobAppTypeRequest" version="1.1.0" timeout="500"/>

    <!--网点对接-->
    <!-- 可达接口2.0 -->
    <dubbo:reference check="false" id="platformReachableService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.platform.IPlatformReachableService"/>
    <!--中通-->
    <dubbo:reference check="false" id="ztoExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.zto.IZtoExpressService"/>
    <!--圆通-->
    <dubbo:reference check="false" id="ytoExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.yto.IYtoExpressService"/>
    <dubbo:reference check="false" id="ytoBExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.ytob.IYtoBExpressService"/>
    <!--百世汇通-->
    <dubbo:reference check="false" id="htExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.ht.IHtExpressService"/>
    <!--天天-->
    <dubbo:reference check="false" id="ttExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.tt.ITtExpressService"/>
    <!--申通-->
    <dubbo:reference check="false" id="stoExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.sto.IStoExpressService"/>
    <!--顺丰-->
    <dubbo:reference check="false" id="sfExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.sf.ISfExpressService"/>
    <!--顺丰国际-->
    <dubbo:reference check="false" id="sfGjExpressService" retries="0" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.sf.ISfGjExpressService"/>
    <!--顺丰丰密-->
    <dubbo:reference check="false" id="sffmExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.sf.ISffmExpressService"/>
    <!--顺丰快运-->
    <dubbo:reference check="false" id="sfkyExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.sf.ISfkyExpressService"/>
    <!--丰网速运-->
    <dubbo:reference check="false" id="fengWangExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.fengwang.IFengWangExpressService"/>
    <!--韵达-->
    <dubbo:reference check="false" id="yunDaExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.yunda.IYunDaExpressService"/>
    <!--优速-->
    <dubbo:reference check="false" id="ucExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.uc.IUcExpressService"/>
    <!--国通-->
    <dubbo:reference check="false" id="gtExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.gt.IGtExpressService"/>
    <!--全峰-->
    <dubbo:reference check="false" id="qfExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.qf.IQfExpressService"/>
    <!--快捷-->
    <dubbo:reference check="false" id="fastExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.fast.IFastExpressService"/>
    <!--跨越快递-->
    <dubbo:reference check="false" id="kuaYueExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.kuayue.IKuaYueExpressService"/>
    <!-- 德邦快递 -->
    <dubbo:reference check="false" id="depponExpressService" version="${dubbo.express.version}" timeout="15000"
                     interface="com.kuaidizs.express.service.api.deppon.IDepponExpressService"/>
    <!--中通国际-->
    <dubbo:reference check="false" id="ztoGjExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.zto.IZtoGjExpressService"/>
    <!--中通快运-->
    <dubbo:reference check="false" id="ztoKyExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.zto.IZtoKyExpressService"/>
    <!-- 百世云配 -->
    <dubbo:reference check="false" id="bsycExpressService" version="${dubbo.express.version}" timeout="15000"
                     interface="com.kuaidizs.express.service.api.bsyc.IBsycExpressService"/>

    <!-- 疫情地区服务 -->
    <dubbo:reference init="false" check="false" id="epidemicAreaService"
                     interface="com.kuaidizs.express.service.api.IEpidemicAreaService"
                     version="${dubbo.express.version}"
                     timeout="5000"/>

    <!-- 地址查询服务 -->
    <dubbo:reference init="false" check="false" id="areaMiddleService"
                     interface="com.kuaidizs.express.service.api.IAreaService"
                     version="${dubbo.express.version}"
                     timeout="5000"/>

    <!-- 物流云服务 -->
    <dubbo:reference check="false" id="logisticsQueryService" version="${dubbo.wly.version}" timeout="5000"
                     init="false" interface="com.kdzs.wly.service.ILogisticsQueryService"/>

    <!--物流云订阅-->
    <dubbo:reference init="false" check="false" id="logisticsSubService"
                     interface="com.kdzs.wly.service.ILogisticsSubService" version="${dubbo.wly.version}"
                     timeout="5000"/>

</beans>
