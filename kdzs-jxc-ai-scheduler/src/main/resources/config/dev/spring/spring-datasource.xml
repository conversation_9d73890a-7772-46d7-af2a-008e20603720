<?xml version="1.0" encoding="utf-8"?>
<beans default-autowire="byName"
       xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
       http://www.springframework.org/schema/util  http://www.springframework.org/schema/util/spring-util-3.0.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd">

    <!--dev-->
    <!-- 数据源1 -->
    <bean id="dataSource" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc.url}"/>
        <property name="username" value="${jdbc.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc.x}.${jdbc.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="initialSize" value="5"/>
        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="minIdle" value="${jdbc.minIdle}"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
    </bean>

    <tx:annotation-driven transaction-manager="txManager"
                          proxy-target-class="true"/>

    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <bean id="sqlMapClient" class="org.springframework.orm.ibatis.SqlMapClientFactoryBean">
        <property name="configLocation">
            <value>classpath:ibatis/sqlmap-config.xml</value>
        </property>
        <property name="mappingLocations">
            <value>classpath*:/ibatis/sqlmap/*.xml</value>
        </property>
    </bean>

    <util:set id="shardSet" set-class="java.util.LinkedHashSet">
        <ref local="master"/>
    </util:set>

    <bean id="master" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master"/>
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <bean id="router" class="com.alibaba.cobarclient.config.SimpleRouterFactoryBean">
        <property name="configLocation"
                  value="classpath:spring/namespace.xml"/>
        <property name="shards" ref="shardSet"/>
    </bean>

    <!-- 工程里一定要使用此工程模板，不能再使用ibatis原生的api，不然有的情况会不经过cobar的过滤-->
    <bean id="sqlMapClientTemplate" class="com.alibaba.cobarclient.MysdalSqlMapClientTemplate">
        <property name="sqlMapClient" ref="sqlMapClient"/>
        <property name="shards" ref="shardSet"/>
        <property name="router" ref="router"/>
    </bean>

    <bean id="txManager"
          class="com.alibaba.cobarclient.transaction.BestEffortMultiDataSourceTransactionManager">
        <property name="shards" ref="shardSet"/>
        <property name="transactionSynchronization" value="2"/>
    </bean>

    <bean id="jdbcTemplate"
          class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource" ref="dataSource"/>
    </bean>
</beans>
