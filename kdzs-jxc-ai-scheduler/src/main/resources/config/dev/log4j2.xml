<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE log4j:configuration PUBLIC
        "-//APACHE//DTD LOG4J 1.2//EN"
        "http://logging.apache.org/log4j/1.2/apidocs/org/apache/log4j/xml/doc-files/log4j.dtd">
<log4j:configuration xmlns:log4j='http://jakarta.apache.org/log4j/'>
    <!--控制台输出日志-->
    <appender name="CONSOLE" class="org.apache.log4j.ConsoleAppender">
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss} %5p [%F:%L]  - %m%n"/>
        </layout>
    </appender>

    <logger name="com.alibaba.druid.sql" additivity="false">
        <level value="DEBUG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <logger name="com.kuaidizs.jxc.job" additivity="false">
        <level value="DEBUG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <!--<logger name="cn.org.authority.manager.com.kuaidizs.yjorder.web.arc_3.taobao.service" additivity="false">-->
        <!--<level value="DEBUG"/>-->
        <!--<appender-ref ref="CONSOLE"/>-->
    <!--</logger>-->

    <logger name="httpclient.wire" additivity="false">
        <level value="DEBUG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <logger name="token" additivity="false">
        <level value="DEBUG"/>
        <appender-ref ref="CONSOLE"/>
    </logger>

    <root>
        <priority value="debug"/>
        <appender-ref ref="CONSOLE"/>
    </root>
</log4j:configuration>