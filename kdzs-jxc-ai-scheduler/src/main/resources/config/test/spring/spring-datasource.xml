<?xml version="1.0" encoding="utf-8"?>
<beans default-autowire="byName"
       xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
       http://www.springframework.org/schema/util  http://www.springframework.org/schema/util/spring-util-3.0.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd">

    <!-- 数据源1 -->
    <bean id="dataSource" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc.url}"/>
        <property name="username" value="${jdbc.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc.x}.${jdbc.y}"/>


        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
    </bean>

    <!-- 数据源1 -->
    <bean id="dataSource0" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc.url}"/>
        <property name="username" value="${jdbc.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc.x}.${jdbc.y}"/>


        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
    </bean>




    <tx:annotation-driven transaction-manager="txManager"
                          proxy-target-class="true"/>

    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <bean id="sqlMapClient" class="org.springframework.orm.ibatis.SqlMapClientFactoryBean">
        <property name="configLocation">
            <value>classpath:ibatis/sqlmap-config.xml</value>
        </property>
        <property name="mappingLocations">
            <value>classpath*:/ibatis/sqlmap/*.xml</value>
        </property>
    </bean>

    <util:set id="shardSet" set-class="java.util.LinkedHashSet">
        <ref local="master1"/>
        <ref local="master2"/>
        <ref local="master3"/>
        <ref local="master4"/>
        <ref local="master5"/>
        <ref local="master6"/>
        <ref local="master7"/>
        <ref local="master8"/>
        <ref local="master9"/>
        <ref local="master10"/>
        <ref local="master11"/>
        <ref local="master12"/>
        <ref local="master13"/>
        <ref local="master14"/>
        <ref local="master15"/>
        <ref local="master16"/>
        <ref local="master17"/>
        <ref local="master18"/>
        <ref local="master19"/>
        <ref local="master20"/>
        <ref local="master21"/>
        <ref local="master22"/>
        <ref local="master0"/>
        <ref local="master23"/>
        <ref local="master24"/>
    </util:set>

    <bean id="master1" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master1"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>

    <bean id="master2" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master2"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>

    <bean id="master3" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master3"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>

    <bean id="master4" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master4"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>
    <bean id="master5" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master5"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>
    <bean id="master6" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master6"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>
    <bean id="master7" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master7"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>
    <bean id="master8" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master8"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>
    <bean id="master9" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master9"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>
    <bean id="master10" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master10"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>

    <bean id="master11" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master11"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>
    <bean id="master12" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master12"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>
    <bean id="master13" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master13"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>
    <bean id="master14" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master14"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>
    <bean id="master15" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master15"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>
    <bean id="master16" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master16"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>
    <bean id="master17" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master17"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>
    <bean id="master18" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master18"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>

    <bean id="master19" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master19"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>

    <bean id="master20" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master20"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>

    <bean id="master21" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master21"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>

    <bean id="master22" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master22"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>

    <bean id="master0" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master0"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>

    <bean id="master23" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master23"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>

    <bean id="master24" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master24"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>


    <util:map id="functionsMap">
        <entry key="hash" value-ref="hashFunction"></entry>
    </util:map>

    <bean id="hashFunction" class="com.kuaidizs.web.db.router.rules.support.HashFunction"></bean>

    <bean id="router" class="com.alibaba.cobarclient.config.SimpleRouterFactoryBean">
        <property name="configLocations">
            <list>
                <value>classpath:spring/namespace.xml</value>
                <value>classpath:spring/sharding-rules-on-namespace.xml</value>
            </list>
        </property>
        <property name="functions" ref="functionsMap"></property>
        <property name="shards" ref="shardSet"/>
    </bean>

    <!-- 工程里一定要使用此工程模板，不能再使用ibatis原生的api，不然有的情况会不经过cobar的过滤-->
    <bean id="sqlMapClientTemplate" class="com.alibaba.cobarclient.MysdalSqlMapClientTemplate">
        <property name="sqlMapClient" ref="sqlMapClient"/>
        <property name="shards" ref="shardSet"/>
        <property name="router" ref="router"/>
    </bean>

    <bean id="txManager"
          class="com.alibaba.cobarclient.transaction.BestEffortMultiDataSourceTransactionManager">
        <property name="shards" ref="shardSet"/>
        <property name="transactionSynchronization" value="2"/>
    </bean>

    <bean id="jdbcTemplate"
          class="org.springframework.jdbc.core.JdbcTemplate">
        <property name="dataSource" ref="dataSource"/>
    </bean>




    <bean id="sqlSessionFactory" class="com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean">
        <property name="dataSource" ref="dynamicDataSource"/>

        <!--        <property name="configuration" ref="configuration"/>-->
        <property name="mapperLocations" value="classpath:mappers/*.xml"/>
        <!--        <property name="globalConfig" ref="globalConfig"/>-->
        <property name="plugins">
            <array>
                <ref bean="mybatisPlusInterceptor"/>
            </array>
        </property>

    </bean>


    <bean id="dynamicDataSource" class="com.kuaidizs.jxc.config.TbDynamicDataSource">
        <property name="targetDataSources">
            <map key-type="java.lang.String">
                <entry key="1" value-ref="dataSource0"/>
                <entry key="2" value-ref="dataSource0"/>
                <entry key="3" value-ref="dataSource0"/>
                <entry key="4" value-ref="dataSource0"/>
                <entry key="5" value-ref="dataSource0"/>
                <entry key="6" value-ref="dataSource0"/>
                <entry key="7" value-ref="dataSource0"/>
                <entry key="8" value-ref="dataSource0"/>
                <entry key="9" value-ref="dataSource0"/>
                <entry key="10" value-ref="dataSource0"/>
                <entry key="11" value-ref="dataSource0"/>
                <entry key="12" value-ref="dataSource0"/>
                <entry key="13" value-ref="dataSource0"/>
                <entry key="14" value-ref="dataSource0"/>
                <entry key="15" value-ref="dataSource0"/>
                <entry key="16" value-ref="dataSource0"/>
                <entry key="17" value-ref="dataSource0"/>
                <entry key="18" value-ref="dataSource0"/>
                <entry key="19" value-ref="dataSource0"/>
                <entry key="20" value-ref="dataSource0"/>
                <entry key="21" value-ref="dataSource0"/>
                <entry key="22" value-ref="dataSource0"/>
            </map>
        </property>
        <property name="defaultTargetDataSource" ref="dataSource"/>
    </bean>
</beans>
