<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration xmlns:log4j='http://jakarta.apache.org/log4j/'>
    <!--test-->
    <appender name="ERROR_LOGGER" class="org.apache.log4j.DailyRollingFileAppender">
        <param name="File" value="${webapp.root}/logs2/error/error.log"/>
        <param name="Append" value="true"/>
        <param name="DatePattern" value="'.'yyyy-MM-dd-HH'.log'"/>
        <param name="MaxBackupIndex" value="10"/>
        <param name="MaxFileSize" value="4000000"/>
        <param name="encoding" value="utf-8"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="[date:%d{yyyy-MM-dd HH:mm:ss}] %m%n"/>
        </layout>
        <filter class="org.apache.log4j.varia.LevelRangeFilter">
            <param name="LevelMin" value="ERROR"/>
            <param name="LevelMax" value="ERROR"/>
        </filter>
    </appender>

    <appender name="INFO_LOGGER" class="org.apache.log4j.DailyRollingFileAppender">
        <param name="File" value="${webapp.root}/logs2/info/info.log"/>
        <param name="Append" value="true"/>
        <param name="DatePattern" value="'.'yyyy-MM-dd-HH'.log'"/>
        <param name="MaxBackupIndex" value="10"/>
        <param name="MaxFileSize" value="4000000"/>
        <param name="encoding" value="utf-8"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="[date:%d{yyyy-MM-dd HH:mm:ss}] %m%n"/>
        </layout>
        <filter class="org.apache.log4j.varia.LevelRangeFilter">
            <param name="LevelMin" value="INFO"/>
            <param name="LevelMax" value="WARN"/>
        </filter>
    </appender>

    <appender name="BIZ_LOGGER" class="org.apache.log4j.DailyRollingFileAppender">
        <param name="File" value="${webapp.root}/logs2/biz/biz.log"/>
        <param name="Append" value="true"/>
        <param name="DatePattern" value="'.'yyyy-MM-dd-HH'.log'"/>
        <param name="MaxBackupIndex" value="10"/>
        <param name="MaxFileSize" value="4096"/>
        <param name="encoding" value="utf-8"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="[date:%d{yyyy-MM-dd HH:mm:ss}] %m%n"/>
        </layout>
        <filter class="com.raycloud.bizlogger.LoggerFilter">
            <param name="LevelMin" value="40050"/>
            <param name="LevelMax" value="40050"/>
        </filter>
    </appender>
    <!--##################################################################################-->
    <!--错误日志-添加到root中-->
    <appender name="TRACE_ERROR_LOGGER" class="org.apache.log4j.DailyRollingFileAppender">
        <!-- 设置是否在重新启动服务时，在原有日志的基础添加新日志 test dev -->
        <param name="File" value="${webapp.root}/logs2/trace/error.log"/>
        <param name="DatePattern" value="'.'yyyy-MM-dd-HH'.log'"/>
        <param name="encoding" value="utf-8"/>
        <layout class="com.raycloud.eagle.log.ErrorLogLayout"/>
        <filter class="org.apache.log4j.varia.LevelRangeFilter">
            <param name="LevelMin" value="ERROR"/>
            <param name="LevelMax" value="ERROR"/>
        </filter>
    </appender>
    <!--SQL执行日志-->
    <appender name="TRACE_SQL_LOGGER" class="org.apache.log4j.DailyRollingFileAppender">
        <param name="File" value="${webapp.root}/logs2/trace/sql.log"/>
        <param name="DatePattern" value="'.'yyyy-MM-dd-HH'.log'"/>
        <param name="encoding" value="utf-8"/>
        <layout class="com.raycloud.eagle.log.SQLLogLayout"/>
        <filter class="org.apache.log4j.varia.LevelRangeFilter">
            <param name="LevelMin" value="DEBUG"/>
            <param name="LevelMax" value="ERROR"/>
        </filter>
    </appender>
    <!--trace日志-->
    <appender name="TRACE_LOGGER" class="org.apache.log4j.DailyRollingFileAppender">
        <param name="File" value="${webapp.root}/logs2/trace/trace.log"/>
        <param name="DatePattern" value="'.'yyyy-MM-dd-HH'.log'"/>
        <param name="encoding" value="utf-8"/>
        <layout class="com.raycloud.eagle.log.TraceLogLayout"/>
        <!--40100为trace日志,40050为biz日志-->
        <filter class="com.raycloud.bizlogger.LoggerFilter">
            <param name="LevelMin" value="40100"/>
            <param name="LevelMax" value="40100"/>
        </filter>
    </appender>
    <appender name="CUSTOM_BIZ_LOGGER" class="org.apache.log4j.DailyRollingFileAppender">
        <param name="File" value="${webapp.root}/logs2/trace/custom_biz.log"/>
        <!-- 设置日志输出文件名 -->
        <param name="DatePattern" value="'.'yyyy-MM-dd-HH'.log'"/>
        <param name="encoding" value="utf-8"/>
        <layout class="com.raycloud.eagle.log.CustomBizLogLayout"/>
        <filter class="com.raycloud.bizlogger.LoggerFilter">
            <param name="LevelMin" value="90001"/>
            <param name="LevelMax" value="90001"/>
        </filter>
    </appender>
    <appender name="FULLINFO_BIZ_LOGGER" class="org.apache.log4j.DailyRollingFileAppender">
        <param name="File" value="${webapp.root}/logs2/trace/fullinfo_biz.log"/>
        <!-- 设置日志输出文件名 -->
        <param name="DatePattern" value="'.'yyyy-MM-dd-HH'.log'"/>
        <param name="encoding" value="utf-8"/>
        <layout class="com.raycloud.eagle.log.CustomBizLogLayout"/>
        <filter class="com.raycloud.bizlogger.LoggerFilter">
            <param name="LevelMin" value="90002"/>
            <param name="LevelMax" value="90002"/>
        </filter>
    </appender>

    <!--##################################################################################-->
    <!--SQL配置-->
    <logger name="trace.sql" additivity="false">
        <level value="DEBUG"/>
        <appender-ref ref="TRACE_SQL_LOGGER"/>
    </logger>
    <!--trace配置-->
    <logger name="trace" additivity="false">
        <appender-ref ref="TRACE_LOGGER"/>
    </logger>

    <logger name="com.opensymphony.xwork2" additivity="false">
        <level value="ERROR"/>
        <appender-ref ref="INFO_LOGGER"/>
    </logger>
    <logger name="org.apache.struts2" additivity="false">
        <level value="ERROR"/>
        <appender-ref ref="INFO_LOGGER"/>
    </logger>
    <logger name="com.mchange.v2" additivity="false">
        <level value="WARN"/>
        <appender-ref ref="INFO_LOGGER"/>
    </logger>
    <logger name="cn.com.raycloud.taobao.service" additivity="false">
        <level value="WARN"/>
        <appender-ref ref="INFO_LOGGER"/>
    </logger>
    <logger name="com.kuaidizs.jxc.mybatisplus.mapper" additivity="false">
        <level value="DEBUG"/>
        <appender-ref ref="INFO_LOGGER"/>
    </logger>

    <root>
        <priority value="INFO"/>
        <!--鹰眼日志-->
        <appender-ref ref="FULLINFO_BIZ_LOGGER"/>
        <appender-ref ref="CUSTOM_BIZ_LOGGER"/>
        <appender-ref ref="TRACE_ERROR_LOGGER"/>
        <!--应用日志-->
        <appender-ref ref="INFO_LOGGER"/>
        <appender-ref ref="DEBUG_LOGGER"/>
        <appender-ref ref="BIZ_LOGGER"/>
        <appender-ref ref="ERROR_LOGGER"/>
    </root>
</log4j:configuration>