<?xml version="1.0" encoding="UTF-8"?>
<beans default-autowire="byName" xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd
        ">
    <dubbo:application name="kdzs-job-scheduler"/>

    <!--production-->
    <dubbo:registry protocol="zookeeper" address="${zkHost}"/>
    <dubbo:registry register="false" id="north-common" protocol="zookeeper" address="${common.zookeeper.connect}"/>

    <!--<dubbo:registry address="multicast://*********:1234"/>-->

    <bean id="dbPasswordCallback" class="com.raycloud.dBPasswordCallback.NsDbPasswordCallback">
        <property name="secretRequest" ref="secretRequest"/>
    </bean>

    <!--加密服务-->
    <dubbo:reference check="false" init="false" id="secretRequest" version="2.0.0_KDZS"
                     interface="com.raycloud.secret_api.api.SecretRequest"/>

    <dubbo:reference check="false" id="itemService" interface="com.raycloud.items.IItemSearcher"
                     loadbalance="rcconsistenthash" version="1.1.0" timeout="20000"/>
    <dubbo:reference check="false" id="itemSkuService" interface="com.raycloud.items.IItemSkuSearcher"
                     loadbalance="rcconsistenthash" version="1.1.0" timeout="20000"/>

    <dubbo:reference filter="rds_trade_access" id="SyncRDSTradeGetRequest"
                     interface="com.raycloud.data.api.SyncRDSTradeGetRequest"
                     version="${trade_rds_sync_version}" retries="1"/>
    <dubbo:reference filter="rds_trade_access" id="SyncUserRegistRequest"
                     interface="com.raycloud.data.api.SyncUserRegistRequest" check="false"
                     init="false" version="${trade_rds_sync_version}" retries="1"/>
    <dubbo:reference filter="rds_trade_access" id="syncRDSRefundGetRequest"
                     interface="com.raycloud.data.api.SyncRDSRefundGetRequest" check="false"
                     init="false" version="${trade_rds_sync_version}" timeout="1000"/>
    <dubbo:reference filter="rds_trade_access" id="syncUserGetRequest"
                     interface="com.raycloud.data.api.SyncUserGetRequest" check="false"
                     init="false" version="${trade_rds_sync_version}" retries="1"/>
    <dubbo:reference filter="rds_trade_access" id="rdsUserRegistRequest" retries="1"
                     interface="com.raycloud.data.api.RDSUserRegistRequest" check="false"
                     init="false" version="${trade_rds_sync_version}"/>

    <dubbo:reference check="true" init="false" id="msgJobRequest" retries="0"
                     interface="com.raycloud.api.msgjob.service.MsgJobRequest" version="1.2.0" timeout="10000"/>
    <dubbo:reference check="true" init="false" id="msgCountRequest" retries="0"
                     interface="com.raycloud.api.msgjob.service.MsgCountRequest" version="1.2.0" timeout="10000"/>
    <dubbo:reference check="true" init="false" id="msgJobAppTypeRequest" retries="0"
                     interface="com.raycloud.api.msgjob.service.MsgJobAppTypeRequest" version="1.2.0" timeout="10000"/>

    <dubbo:reference check="false" init="false" id="notifyJobRequest" retries="0"
                     interface="com.raycloud.notify.api.service.NotifyJobRequest" version="2.0.1-kdzs" timeout="10000"/>

    <!--网点对接-->
    <!-- 可达接口2.0 -->
    <dubbo:reference check="false" id="platformReachableService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.platform.IPlatformReachableService"/>
    <!--中通-->
    <dubbo:reference check="false" id="ztoExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.zto.IZtoExpressService"/>
    <!--圆通-->
    <dubbo:reference check="false" id="ytoExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.yto.IYtoExpressService"/>
    <dubbo:reference check="false" id="ytoBExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.ytob.IYtoBExpressService"/>
    <!--百世汇通-->
    <dubbo:reference check="false" id="htExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.ht.IHtExpressService"/>
    <!--天天-->
    <dubbo:reference check="false" id="ttExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.tt.ITtExpressService"/>
    <!--申通-->
    <dubbo:reference check="false" id="stoExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.sto.IStoExpressService"/>
    <!--顺丰-->
    <dubbo:reference check="false" id="sfExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.sf.ISfExpressService"/>
    <!--顺丰国际-->
    <dubbo:reference check="false" id="sfGjExpressService" retries="0" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.sf.ISfGjExpressService"/>
    <!--顺丰丰密-->
    <dubbo:reference check="false" id="sffmExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.sf.ISffmExpressService"/>
    <!--顺丰快运-->
    <dubbo:reference check="false" id="sfkyExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.sf.ISfkyExpressService"/>
    <!--丰网速运-->
    <dubbo:reference check="false" id="fengWangExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.fengwang.IFengWangExpressService"/>
    <!--韵达-->
    <dubbo:reference check="false" id="yunDaExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.yunda.IYunDaExpressService"/>
    <!--优速-->
    <dubbo:reference check="false" id="ucExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.uc.IUcExpressService"/>
    <!--国通-->
    <dubbo:reference check="false" id="gtExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.gt.IGtExpressService"/>
    <!--全峰-->
    <dubbo:reference check="false" id="qfExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.qf.IQfExpressService"/>
    <!--快捷-->
    <dubbo:reference check="false" id="fastExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.fast.IFastExpressService"/>
    <!--安能-->
    <dubbo:reference check="false" id="aneExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.ane.IAneExpressService"/>
    <!--全一快递-->
    <dubbo:reference check="false" id="qyExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.qy.IQyExpressService"/>

    <!--速尔快递-->
    <dubbo:reference check="false" id="sureExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.sure.ISureExpressService"/>
    <!--跨越快递-->
    <dubbo:reference check="false" id="kuaYueExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.kuayue.IKuaYueExpressService"/>
    <!--安能快运-->
    <dubbo:reference check="false" id="aneKyExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.ane.IAneKyExpressService"/>
    <!--极兔快运-->
    <dubbo:reference check="false" id="jtsdExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.jtsd.IJtsdExpressService"/>
    <!--加运美快运-->
    <dubbo:reference check="false" id="jymExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.jym.IJymExpressService"/>
    <!--京广快递-->
    <dubbo:reference check="false" id="jgOrderExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.jg.JgOrderExpressService"/>
    <!--中通国际-->
    <dubbo:reference check="false" id="ztoGjExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.zto.IZtoGjExpressService"/>
    <!--中通快运-->
    <dubbo:reference check="false" id="ztoKyExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.zto.IZtoKyExpressService"/>
    <!-- 百世云配 -->
    <dubbo:reference check="false" id="bsycExpressService" version="${dubbo.express.version}" timeout="15000"
                     interface="com.kuaidizs.express.service.api.bsyc.IBsycExpressService"/>

    <!--安鲜达-->
    <dubbo:reference check="false" id="axdExpressService" version="${dubbo.express.version}" timeout="15000"
                     interface="com.kuaidizs.express.service.api.axd.IAxdExpressService"/>

    <!--分拣码服务-->
    <dubbo:reference check="false" id="iSortCodeService" version="${dubbo.express.version}" timeout="10000"
                     interface="com.kuaidizs.express.service.api.ISortCodeService"/>
    <!-- 德邦快递 -->
    <dubbo:reference check="false" id="depponExpressService" version="${dubbo.express.version}" timeout="15000"
                     interface="com.kuaidizs.express.service.api.deppon.IDepponExpressService"/>

    <!-- 疫情地区服务 -->
    <dubbo:reference init="false" check="false" id="epidemicAreaService"
                     interface="com.kuaidizs.express.service.api.IEpidemicAreaService"
                     version="${dubbo.express.version}"
                     timeout="5000"/>

    <!-- 地址查询服务 -->
    <dubbo:reference init="false" check="false" id="areaMiddleService"
                     interface="com.kuaidizs.express.service.api.IAreaService"
                     version="${dubbo.express.version}"
                     timeout="5000"/>

    <!--淘外菜鸟-->
    <dubbo:reference check="false" id="cnUserDubboService" version="${twcn.dubbo}" timeout="20000"
                     interface="com.kuaidizs.twcn.dubbo.api.CnUserDubboService"/>

    <!--多平台店铺绑定-->
    <dubbo:reference check="false" id="platformGroupConnectionServiceDubbo" version="${group.dubbo}" timeout="20000"
                     interface="com.kuaidizs.group.service.IPlatformGroupConnectionService"/>

    <!-- 物流云服务 -->
    <dubbo:reference check="false" id="logisticsQueryService" version="${dubbo.wly.version}" timeout="5000"
                     init="false" interface="com.kdzs.wly.service.ILogisticsQueryService"/>


    <!--物流云订阅-->
    <dubbo:reference init="false" check="false" id="logisticsSubService"
                     interface="com.kdzs.wly.service.ILogisticsSubService" version="${dubbo.wly.version}"
                     timeout="5000"/>
    <!--jxc-->
    <dubbo:reference check="false" id="jxcUserDubboService" version="${kdzs.erp.dubbo}" timeout="10000" retries="0"
                     interface="com.kuaidizs.erp.service.jxc.IJxcUserDubboService"/>
    <dubbo:reference check="false" id="jxcOrderService" version="${kdzs.erp.dubbo}" timeout="10000" retries="3"
                     interface="com.kuaidizs.erp.service.order.IJxcOrderService"/>
    <dubbo:reference check="false" id="orderStockService" version="${kdzs.erp.dubbo}" timeout="10000" retries="0"
                     interface="com.kuaidizs.erp.service.stock.IOrderStockService"/>

    <!--智选物流-->
    <dubbo:reference init="false" check="false" id="expressMatchConfigApi"
                     interface="com.kuaidizs.zxwl.api.service.zxwl.ExpressMatchConfigApi"
                     version="${dubbo.express.match.version}"
                     retries="1"
                     timeout="5000">
        <dubbo:method name="updateUserExNames" timeout="1000"/>
    </dubbo:reference>
    <dubbo:reference init="false" check="false" id="expressMatchApi"
                     interface="com.kuaidizs.zxwl.api.service.zxwl.ExpressMatchApi"
                     version="${dubbo.express.match.version}"
                     retries="1"
                     timeout="5000"/>
    <dubbo:reference init="false" check="false" id="keyWordsApi"
                     interface="com.kuaidizs.zxwl.api.service.zxwl.KeyWordsApi"
                     version="${dubbo.express.match.version}"
                     retries="1"
                     timeout="5000"/>

    <!--地址解析服务-->
    <dubbo:reference init="false" check="false" id="parserAddressService" version="${kdzs.address.parse}" timeout="5000"
                     interface="com.kuaidizs.address.ParserAddressService"/>
    <!--erp-plus-->
    <dubbo:reference init="false" check="false" id="dubboStockService"
                     interface="com.kdzs.erp.plus.api.service.DubboStockService"
                     version="1.0.0_erp_plus_pro"
                     timeout="5000"/>
    <dubbo:reference init="false" check="false" id="dubboAdminUserService"
                     interface="com.kdzs.erp.plus.api.service.DubboAdminUserService"
                     version="1.0.0_erp_plus_pro"
                     timeout="5000"/>

    <!--logistics-service-->
    <dubbo:reference init="false" check="false" id="dubboLogisticsInfoService"
                     interface="com.kdzs.logistics.service.api.service.logistics.DubboLogisticsInfoService"
                     version="logistics_service_prod_1.0.0"
                     timeout="5000"/>
    <dubbo:reference init="false" check="false" id="dubboLogisticsUserConfigService"
                     interface="com.kdzs.logistics.service.api.service.logistics.DubboLogisticsUserConfigService"
                     version="logistics_service_prod_1.0.0"
                     timeout="5000"/>

    <!--中运全速快递-->
    <dubbo:reference check="false" id="zyQsOrderExpressService" version="${dubbo.express.version}" timeout="20000"
                     interface="com.kuaidizs.express.service.api.zyqs.IZyQsOrderExpressService"/>

    <!--crm违禁词服务-->
    <dubbo:reference init="false" check="false" id="sendClient"
                     interface="com.raycloud.xcrm.send.client.SendClient"
                     version="${dubbo.crm.version}"
                     timeout="3000"/>

    <!--商品查询服务-->
    <dubbo:reference init="false" check="false" id="newItemSearcher" interface="com.raycloud.item.api.ItemSearcher"
                     version="${dubbo.item.service.version}" timeout="10000" retries="0"/>

    <!--规格查询服务-->
    <dubbo:reference init="false" check="false" id="newSkuSearcher" interface="com.raycloud.item.api.SkuSearcher"
                     version="${dubbo.item.service.version}" timeout="10000" retries="0"/>

    <!--商品异步导入和进度查询服务-->
    <dubbo:reference init="false" check="false" id="newItemImport" interface="com.raycloud.item.api.ItemImport"
                     version="${dubbo.item.service.version}" timeout="10000" retries="0"/>

    <!--商品更新服务-->
    <dubbo:reference init="false" check="false" id="newItemUpdate" interface="com.raycloud.item.api.ItemUpdate"
                     version="${dubbo.item.service.version}" timeout="10000" retries="0"/>


    <!--ocr违禁词服务-->
    <dubbo:reference init="false" check="false" id="iKfcService"
                     interface="com.raycloud.ocr.api.IKfcService"
                     version="${dubbo.ocr.version}"
                     timeout="10000" retries="0"/>

    <!--共享单号-->
    <dubbo:reference check="false" init="false" id="quantityControlDubboService" version="${dubbo.share.version}"
                     interface="com.kuaidizs.share.common.api.quantity.QuantityControlService"/>
    <dubbo:reference check="false" init="false" id="wayBillInfoDubboService" version="${dubbo.share.version}"
                     interface="com.kuaidizs.share.common.api.waybill.IWayBillInfoDubboService"/>
    <dubbo:reference check="false" init="false" id="shareElecDubboService" version="${dubbo.share.version}"
                     interface="com.kuaidizs.share.common.api.service.elec.ShareElecDubboService"/>

    <dubbo:reference check="false" id="cnMultiPlatService" version="${twcn.dubbo}" timeout="20000"
                     interface="com.kuaidizs.twcn.dubbo.api.CnMultiPlatService"/>

    <!--雪花算法服务-->
    <dubbo:reference check="false" id="snowFlakeService" version="${kdzs.special.dubbo}" timeout="20000"
                     interface="com.kdzs.special.service.api.snow.TaobaoSnowFlakeService"/>

</beans>
