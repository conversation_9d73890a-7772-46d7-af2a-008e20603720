<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
           http://www.springframework.org/schema/beans/spring-beans.xsd
           http://www.springframework.org/schema/context
           http://www.springframework.org/schema/context/spring-context.xsd">

<!--	<bean id="propertyConfigurer" class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">-->
<!--		<property name="fileEncoding" value="utf-8" />-->
<!--		<property name="locations">-->
<!--			<list>-->
<!--				<value>classpath:profile/xxl-job-executor.properties</value>-->
<!--			</list>-->
<!--		</property>-->
<!--	</bean>-->

    <!-- ********************************* 基础配置 ********************************* -->

	<!-- 配置01、JobHandler 扫描路径 -->
	<context:component-scan base-package="com.kuaidizs.jxc.ai.xxljob" />

	<!-- 配置02、执行器 -->
	<bean id="xxlJobSpringExecutor" class="com.xxl.job.core.executor.impl.XxlJobSpringExecutor" init-method="start" destroy-method="destroy" >
		<!-- 执行器注册中心地址[选填]，为空则关闭自动注册 -->
		<property name="adminAddresses" value="http://xxljob.kuaidizs.cn" />
		<!-- 执行器AppName[选填]，为空则关闭自动注册 -->
		<property name="appName" value="kdzs-jxc-ai-job" />
		<!-- 访问令牌[选填]，非空则进行匹配校验 -->
		<property name="accessToken" value="MJn#cTBsYtyybmzkgV5YPs@q9Vkj9eXM" />
		<!-- 执行器端口号[选填]，小于等于0则自动获取 -->
		<property name="port" value="9999" />
		<property name="logPath" value="${webapp.root}/xxl-job/jobhandler" />
	</bean>


</beans>