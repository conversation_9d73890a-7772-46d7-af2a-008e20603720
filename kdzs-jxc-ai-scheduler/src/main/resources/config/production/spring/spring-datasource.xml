<?xml version="1.0" encoding="utf-8"?>
<beans default-autowire="byName"
       xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx"
       xmlns:util="http://www.springframework.org/schema/util"
       xsi:schemaLocation="
       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd
       http://www.springframework.org/schema/util  http://www.springframework.org/schema/util/spring-util-3.0.xsd
       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.0.xsd">

    <!-- 数据源1 -->
    <bean id="dataSource" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc.url}"/>
        <property name="username" value="${jdbc.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc.x}.${jdbc.y}"/>


        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>

        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源2 -->
    <bean id="dataSource2" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc2.url}"/>
        <property name="username" value="${jdbc2.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc2.x}.${jdbc2.y}"/>



        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源3 -->
    <bean id="dataSource3" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc3.url}"/>
        <property name="username" value="${jdbc3.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc3.x}.${jdbc3.y}"/>



        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源4 -->
    <bean id="dataSource4" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc4.url}"/>
        <property name="username" value="${jdbc4.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc4.x}.${jdbc4.y}"/>



        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源5 -->
    <bean id="dataSource5" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc5.url}"/>
        <property name="username" value="${jdbc5.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc5.x}.${jdbc5.y}"/>



        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源6 -->
    <bean id="dataSource6" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc6.url}"/>
        <property name="username" value="${jdbc6.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc6.x}.${jdbc6.y}"/>



        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源7 -->
    <bean id="dataSource7" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc7.url}"/>
        <property name="username" value="${jdbc7.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc7.x}.${jdbc7.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源8 -->
    <bean id="dataSource8" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc8.url}"/>
        <property name="username" value="${jdbc8.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc8.x}.${jdbc8.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源9 -->
    <bean id="dataSource9" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc9.url}"/>
        <property name="username" value="${jdbc9.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc9.x}.${jdbc9.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源10 -->
    <bean id="dataSource10" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc10.url}"/>
        <property name="username" value="${jdbc10.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc10.x}.${jdbc10.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源11 -->
    <bean id="dataSource11" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc11.url}"/>
        <property name="username" value="${jdbc11.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc11.x}.${jdbc11.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源12 -->
    <bean id="dataSource12" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc12.url}"/>
        <property name="username" value="${jdbc12.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc12.x}.${jdbc12.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源13 -->
    <bean id="dataSource13" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc13.url}"/>
        <property name="username" value="${jdbc13.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc13.x}.${jdbc13.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源14 -->
    <bean id="dataSource14" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc14.url}"/>
        <property name="username" value="${jdbc14.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc14.x}.${jdbc14.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源15 -->
    <bean id="dataSource15" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc15.url}"/>
        <property name="username" value="${jdbc15.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc15.x}.${jdbc15.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>


    <!-- 数据源16 -->
    <bean id="dataSource16" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc16.url}"/>
        <property name="username" value="${jdbc16.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc16.x}.${jdbc16.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源17 -->
    <bean id="dataSource17" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc17.url}"/>
        <property name="username" value="${jdbc17.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc17.x}.${jdbc17.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>


    <!-- 数据源18 -->
    <bean id="dataSource18" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc18.url}"/>
        <property name="username" value="${jdbc18.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc18.x}.${jdbc18.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源19 -->
    <bean id="dataSource19" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc19.url}"/>
        <property name="username" value="${jdbc19.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc19.x}.${jdbc19.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源20 -->
    <bean id="dataSource20" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc20.url}"/>
        <property name="username" value="${jdbc20.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc20.x}.${jdbc20.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源21 -->
    <bean id="dataSource21" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc21.url}"/>
        <property name="username" value="${jdbc21.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc21.x}.${jdbc21.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源22 -->
    <bean id="dataSource22" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc22.url}"/>
        <property name="username" value="${jdbc22.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc22.x}.${jdbc22.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源0 -->
    <bean id="dataSource0" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc0.url}"/>
        <property name="username" value="${jdbc0.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc0.x}.${jdbc0.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源23 -->
    <bean id="dataSource23" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc23.url}"/>
        <property name="username" value="${jdbc23.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc23.x}.${jdbc23.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源24 -->
    <bean id="dataSource24" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbc24.url}"/>
        <property name="username" value="${jdbc24.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbc24.x}.${jdbc24.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>

    <!-- 数据源24 -->
    <bean id="dataSourceFxdf" class="com.alibaba.druid.pool.RayDruidDataSource"
          init-method="init" destroy-method="close">

        <property name="url" value="${jdbcFxdf.url}"/>
        <property name="username" value="${jdbcFxdf.username}"/>
        <property name="secretRequest">
            <ref bean="secretRequest"/>
        </property>
        <property name="diamondCoord" value="${jdbcFxdf.x}.${jdbcFxdf.y}"/>

        <!-- <property name="filters" value="stat"/> -->

        <property name="maxActive" value="${jdbc.maxActive}"/>
        <property name="initialSize" value="5"/>
        <property name="maxWait" value="${jdbc.maxWait}"/>
        <property name="minIdle" value="5"/>

        <property name="timeBetweenEvictionRunsMillis" value="3000"/>
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 'x'"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="true"/>
        <property name="testOnReturn" value="false"/>

        <property name="poolPreparedStatements" value="true"/>
        <property name="maxPoolPreparedStatementPerConnectionSize"
                  value="20"/>
        <property name="removeAbandoned" value="true"/> <!-- 打开removeAbandoned功能 -->
        <property name="removeAbandonedTimeout" value="600"/> <!-- 600秒，也就是10分钟 -->
        <property name="logAbandoned" value="true"/> <!-- 关闭abanded连接时输出错误日志 -->
    </bean>


    <tx:annotation-driven transaction-manager="txManager"
                          proxy-target-class="true"/>

    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <bean id="sqlMapClient" class="org.springframework.orm.ibatis.SqlMapClientFactoryBean">
        <property name="configLocation">
            <value>classpath:ibatis/sqlmap-config.xml</value>
        </property>
        <property name="mappingLocations">
            <value>classpath*:/ibatis/sqlmap/*.xml</value>
        </property>
    </bean>

    <util:set id="shardSet" set-class="java.util.LinkedHashSet">
        <ref local="master1"/>
        <ref local="master2"/>
        <ref local="master3"/>
        <ref local="master4"/>
        <ref local="master5"/>
        <ref local="master6"/>
        <ref local="master7"/>
        <ref local="master8"/>
        <ref local="master9"/>
        <ref local="master10"/>
        <ref local="master11"/>
        <ref local="master12"/>
        <ref local="master13"/>
        <ref local="master14"/>
        <ref local="master15"/>
        <ref local="master16"/>
        <ref local="master17"/>
        <ref local="master18"/>
        <ref local="master19"/>
        <ref local="master20"/>
        <ref local="master21"/>
        <ref local="master22"/>
        <ref local="master0"/>
        <ref local="master23"/>
        <ref local="master24"/>
        <ref local="masterFxdf"/>
    </util:set>

    <bean id="master1" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master1"/>
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <bean id="master2" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master2"/>
        <property name="dataSource" ref="dataSource2"/>
    </bean>

    <bean id="master3" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master3"/>
        <property name="dataSource" ref="dataSource3"/>
    </bean>

    <bean id="master4" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master4"/>
        <property name="dataSource" ref="dataSource4"/>
    </bean>
    <bean id="master5" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master5"/>
        <property name="dataSource" ref="dataSource5"/>
    </bean>
    <bean id="master6" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master6"/>
        <property name="dataSource" ref="dataSource6"/>
    </bean>
    <bean id="master7" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master7"/>
        <property name="dataSource" ref="dataSource7"/>
    </bean>
    <bean id="master8" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master8"/>
        <property name="dataSource" ref="dataSource8"/>
    </bean>
    <bean id="master9" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master9"/>
        <property name="dataSource" ref="dataSource9"/>
    </bean>
    <bean id="master10" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master10"/>
        <property name="dataSource" ref="dataSource10"/>
    </bean>
    <bean id="master11" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master11"/>
        <property name="dataSource" ref="dataSource11"/>
    </bean>
    <bean id="master12" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master12"/>
        <property name="dataSource" ref="dataSource12"/>
    </bean>
    <bean id="master13" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master13"/>
        <property name="dataSource" ref="dataSource13"/>
    </bean>
    <bean id="master14" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master14"/>
        <property name="dataSource" ref="dataSource14"/>
    </bean>
    <bean id="master15" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master15"/>
        <property name="dataSource" ref="dataSource15"/>
    </bean>
    <bean id="master16" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master16"/>
        <property name="dataSource" ref="dataSource16"/>
    </bean>
    <bean id="master17" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master17"/>
        <property name="dataSource" ref="dataSource17"/>
    </bean>
    <bean id="master18" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master18"/>
        <property name="dataSource" ref="dataSource18"/>
    </bean>

    <bean id="master19" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master19"/>
        <property name="dataSource" ref="dataSource19"/>
    </bean>

    <bean id="master20" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master20"/>
        <property name="dataSource" ref="dataSource20"/>
    </bean>

    <bean id="master21" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master21"/>
        <property name="dataSource" ref="dataSource21"/>
    </bean>

    <bean id="master22" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master22"/>
        <property name="dataSource" ref="dataSource22"/>
    </bean>

    <bean id="master0" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master0"/>
        <property name="dataSource" ref="dataSource0"/>
    </bean>

    <bean id="master23" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master23"/>
        <property name="dataSource" ref="dataSource23"/>
    </bean>

    <bean id="master24" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="master24"/>
        <property name="dataSource" ref="dataSource24"/>
    </bean>

    <bean id="masterFxdf" class="com.alibaba.cobarclient.Shard">
        <property name="id" value="masterFxdf"/>
        <property name="dataSource" ref="dataSourceFxdf"/>
    </bean>

    <util:map id="functionsMap">
        <entry key="hash" value-ref="hashFunction"></entry>
    </util:map>

    <bean id="hashFunction" class="com.kuaidizs.web.db.router.rules.support.HashFunction"></bean>

    <bean id="router" class="com.alibaba.cobarclient.config.SimpleRouterFactoryBean">
        <property name="configLocations">
            <list>
                <value>classpath:spring/namespace.xml</value>
                <value>classpath:spring/sharding-rules-on-namespace.xml</value>
            </list>
        </property>
        <property name="functions" ref="functionsMap"></property>
        <property name="shards" ref="shardSet"/>
    </bean>

    <!-- 工程里一定要使用此工程模板，不能再使用ibatis原生的api，不然有的情况会不经过cobar的过滤-->
    <bean id="sqlMapClientTemplate" class="com.alibaba.cobarclient.MysdalSqlMapClientTemplate">
        <property name="sqlMapClient" ref="sqlMapClient"/>
        <property name="shards" ref="shardSet"/>
        <property name="router" ref="router"/>
    </bean>

    <bean id="txManager"
          class="com.alibaba.cobarclient.transaction.BestEffortMultiDataSourceTransactionManager">
        <property name="shards" ref="shardSet"/>
        <property name="transactionSynchronization" value="2"/>
    </bean>



    <bean id="sqlSessionFactory" class="com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean">
        <property name="dataSource" ref="dynamicDataSource"/>

        <!--        <property name="configuration" ref="configuration"/>-->
        <property name="mapperLocations" value="classpath:mappers/*.xml"/>
        <!--        <property name="globalConfig" ref="globalConfig"/>-->
        <property name="plugins">
            <array>
                <ref bean="mybatisPlusInterceptor"/>
            </array>
        </property>

    </bean>


    <bean id="dynamicDataSource" class="com.kuaidizs.jxc.config.TbDynamicDataSource">
        <property name="targetDataSources">
            <map key-type="java.lang.String">
                <entry key="1" value-ref="dataSource0"/>
                <entry key="2" value-ref="dataSource2"/>
                <entry key="3" value-ref="dataSource3"/>
                <entry key="4" value-ref="dataSource4"/>
                <entry key="5" value-ref="dataSource5"/>
                <entry key="6" value-ref="dataSource6"/>
                <entry key="7" value-ref="dataSource7"/>
                <entry key="8" value-ref="dataSource8"/>
                <entry key="9" value-ref="dataSource9"/>
                <entry key="10" value-ref="dataSource10"/>
                <entry key="11" value-ref="dataSource11"/>
                <entry key="12" value-ref="dataSource12"/>
                <entry key="13" value-ref="dataSource13"/>
                <entry key="14" value-ref="dataSource14"/>
                <entry key="15" value-ref="dataSource15"/>
                <entry key="16" value-ref="dataSource16"/>
                <entry key="17" value-ref="dataSource17"/>
                <entry key="18" value-ref="dataSource18"/>
                <entry key="19" value-ref="dataSource19"/>
                <entry key="20" value-ref="dataSource20"/>
                <entry key="21" value-ref="dataSource21"/>
                <entry key="22" value-ref="dataSource22"/>
                <entry key="23" value-ref="dataSource23"/>
                <entry key="24" value-ref="dataSource24"/>
                <entry key="25" value-ref="dataSourceFxdf"/>
            </map>
        </property>
        <property name="defaultTargetDataSource" ref="dataSource"/>
    </bean>
</beans>
