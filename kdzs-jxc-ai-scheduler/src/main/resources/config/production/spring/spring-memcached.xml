<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:cache="http://www.springframework.org/schema/cache"

       xsi:schemaLocation="http://www.springframework.org/schema/beans
         http://www.springframework.org/schema/beans/spring-beans-3.2.xsd
         http://www.springframework.org/schema/context
         http://www.springframework.org/schema/context/spring-context-3.2.xsd
         http://www.springframework.org/schema/aop
         http://www.springframework.org/schema/aop/spring-aop-3.2.xsd
         http://www.springframework.org/schema/cache
           http://www.springframework.org/schema/cache/spring-cache-3.1.xsd
        http://www.springframework.org/schema/mvc
        http://www.springframework.org/schema/mvc/spring-mvc-3.2.xsd">

    <!--production-->

    <import resource="classpath*:simplesm-context.xml"/>

    <aop:aspectj-autoproxy proxy-target-class="true"/>

    <bean name="defaultMemcachedClient" class="com.google.code.ssm.CacheFactory">
        <property name="cacheClientFactory">
            <bean class="com.google.code.ssm.providers.spymemcached.MemcacheClientFactoryImpl"/>
        </property>
        <property name="addressProvider">
            <bean class="com.google.code.ssm.config.DefaultAddressProvider">
                <property name="address" value="${memcached.addr}"/>
            </bean>
        </property>
        <property name="configuration">
            <bean class="com.google.code.ssm.providers.CacheConfiguration">
                <property name="consistentHashing" value="true"/>
                <property name="useBinaryProtocol" value="true"/>
                <property name="operationTimeout" value="2000"/>
            </bean>
        </property>
    </bean>

    <!-- 一些情况下可以直接连接上memcached -->
    <bean id="memcachedClient" class="net.spy.memcached.RayMemcachedClientFactoryBean">
        <property name="servers" value="${memcached.addr}"/>
        <property name="protocol" value="BINARY"/>
        <property name="transcoder">
            <bean class="net.spy.memcached.transcoders.SerializingTranscoder">
                <property name="compressionThreshold" value="1024"/>
            </bean>
        </property>
        <property name="opTimeout" value="2000"/>
        <property name="timeoutExceptionThreshold" value="2000"/>
        <property name="hashAlg">
            <value type="net.spy.memcached.DefaultHashAlgorithm">KETAMA_HASH</value>
        </property>
        <property name="locatorType" value="CONSISTENT"/>
        <property name="failureMode" value="Redistribute"/>
        <property name="useNagleAlgorithm" value="false"/>
        <property name="authDescriptor">
            <bean class="net.spy.memcached.auth.AuthDescriptor">
                <constructor-arg index="0">
                    <value>PLAIN</value>
                </constructor-arg>
                <constructor-arg index="1">
                    <bean class="net.spy.memcached.auth.PlainCallbackHandler">
                        <constructor-arg index="0">
                            <value>${memcached.name}</value>
                        </constructor-arg>
                        <constructor-arg index="1">
                            <value>${memcached.pwd}</value>
                        </constructor-arg>
                    </bean>
                </constructor-arg>
            </bean>
        </property>
    </bean>

    <!-- 一些情况下可以直接连接上memcached -->
    <bean id="memcachedSessionClient" class="net.spy.memcached.spring.MemcachedClientFactoryBean">
        <property name="servers" value="${memcached.session.addr}"/>
        <property name="protocol" value="BINARY"/>
        <property name="transcoder">
            <bean class="net.spy.memcached.transcoders.SerializingTranscoder">
                <property name="compressionThreshold" value="1024"/>
            </bean>
        </property>
        <property name="opTimeout" value="2000"/>
        <property name="timeoutExceptionThreshold" value="2000"/>
        <property name="hashAlg">
            <value type="net.spy.memcached.DefaultHashAlgorithm">KETAMA_HASH</value>
        </property>
        <property name="locatorType" value="CONSISTENT"/>
        <property name="failureMode" value="Redistribute"/>
        <property name="useNagleAlgorithm" value="false"/>
        <property name="authDescriptor">
            <bean class="net.spy.memcached.auth.AuthDescriptor">
                <constructor-arg index="0">
                    <value>PLAIN</value>
                </constructor-arg>
                <constructor-arg index="1">
                    <bean class="net.spy.memcached.auth.PlainCallbackHandler">
                        <constructor-arg index="0">
                            <value>${memcached.session.name}</value>
                        </constructor-arg>
                        <constructor-arg index="1">
                            <value>${memcached.session.pwd}</value>
                        </constructor-arg>
                    </bean>
                </constructor-arg>
            </bean>
        </property>
    </bean>

    <!-- 一些情况下可以直接连接上memcached -->
    <bean id="memcachedBhdClient" class="net.spy.memcached.spring.MemcachedClientFactoryBean">
        <property name="servers" value="${memcached.bhd.addr}"/>
        <property name="protocol" value="BINARY"/>
        <property name="transcoder">
            <bean class="net.spy.memcached.transcoders.SerializingTranscoder">
                <property name="compressionThreshold" value="1024"/>
            </bean>
        </property>
        <property name="opTimeout" value="2000"/>
        <property name="timeoutExceptionThreshold" value="2000"/>
        <property name="hashAlg">
            <value type="net.spy.memcached.DefaultHashAlgorithm">KETAMA_HASH</value>
        </property>
        <property name="locatorType" value="CONSISTENT"/>
        <property name="failureMode" value="Redistribute"/>
        <property name="useNagleAlgorithm" value="false"/>
        <property name="authDescriptor">
            <bean class="net.spy.memcached.auth.AuthDescriptor">
                <constructor-arg index="0">
                    <value>PLAIN</value>
                </constructor-arg>
                <constructor-arg index="1">
                    <bean class="net.spy.memcached.auth.PlainCallbackHandler">
                        <constructor-arg index="0">
                            <value>${memcached.bhd.name}</value>
                        </constructor-arg>
                        <constructor-arg index="1">
                            <value>${memcached.bhd.pwd}</value>
                        </constructor-arg>
                    </bean>
                </constructor-arg>
            </bean>
        </property>
    </bean>

    <bean id="memcachedModeClient" class="net.spy.memcached.spring.MemcachedClientFactoryBean">
        <property name="servers" value="${memcached.mode.addr}"/>
        <property name="protocol" value="BINARY"/>
        <property name="transcoder">
            <bean class="net.spy.memcached.transcoders.SerializingTranscoder">
                <property name="compressionThreshold" value="1024"/>
            </bean>
        </property>
        <property name="opTimeout" value="2000"/>
        <property name="timeoutExceptionThreshold" value="2000"/>
        <property name="hashAlg">
            <value type="net.spy.memcached.DefaultHashAlgorithm">KETAMA_HASH</value>
        </property>
        <property name="locatorType" value="CONSISTENT"/>
        <property name="failureMode" value="Redistribute"/>
        <property name="useNagleAlgorithm" value="false"/>
        <property name="authDescriptor">
            <bean class="net.spy.memcached.auth.AuthDescriptor">
                <constructor-arg index="0">
                    <value>PLAIN</value>
                </constructor-arg>
                <constructor-arg index="1">
                    <bean class="net.spy.memcached.auth.PlainCallbackHandler">
                        <constructor-arg index="0">
                            <value>${memcached.mode.name}</value>
                        </constructor-arg>
                        <constructor-arg index="1">
                            <value>${memcached.mode.pwd}</value>
                        </constructor-arg>
                    </bean>
                </constructor-arg>
            </bean>
        </property>
    </bean>


</beans>