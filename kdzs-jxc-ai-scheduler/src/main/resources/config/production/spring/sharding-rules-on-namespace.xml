<rules>
    <!--批量打印订单表  -->
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>

    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatapl</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!--单打订单表  -->
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>PrintTempdatafpl</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!-- 厂家代发临时订单表 -->
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>

    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>CjdfTempTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!-- 厂家代发分配记录表 -->
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>

    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>CjdfDistributeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!--菜鸟电子面单申请记录  -->
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>DzmdYzLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!--网点电子面单申请记录  -->
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>

    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>DzmdWdLognumber</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!--快递单打印日志表  -->
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>PrintKddLog</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!--发货单打印日志表  -->
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>PrintBgFhd</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!--底单日志表  -->
    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>

    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>

    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>

    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>

    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>

    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>

    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>

    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>

    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>

    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>

    <rule>
        <namespace>PrintBgKdd</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!--打印标记表  -->
    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>

    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>

    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>

    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>

    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>

    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>

    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>

    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>

    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>

    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>

    <rule>
        <namespace>PrintDg</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!--订单时间表  -->
    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>

    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>

    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>

    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>

    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>

    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>

    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>

    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>

    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>

    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>PrintTime</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!--备货单商品信息设置表  -->
    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>

    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>

    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>

    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>

    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>

    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>

    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>

    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>

    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>

    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>

    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>

    <rule>
        <namespace>ItemProset</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!--手工订单信息设置表  -->
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>PrintHandOrders</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!--模板输入项 -->
    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>

    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>

    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>

    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>

    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>

    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>

    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>

    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>

    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>

    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>

    <rule>
        <namespace>ModeInput</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!--模板描述项 -->
    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>

    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>

    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>

    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>

    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>

    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>

    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>

    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>

    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>

    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>

    <rule>
        <namespace>ModeInputpro</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>PrintNumber</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>SearchRule</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>AdvancedSearch</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>FilterSet</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>PrintVersionRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>TradeOrderFeature</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>YfhOrderlist</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>YfhResult</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>TwcnAuthorizeRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>TwcnUserReal</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>TwcnReadmethod</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>TwcnLogisticsReal</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>UserDefaultTemplate</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>TradePending</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>PrintErrorNotify</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!--扫描打印异常订单记录表-->
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>

    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>FhdTagExceptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!--扫描打印记录表-->
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>

    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>FhdTagPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!--快递对账  -->
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>

    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>ExpressAccount</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!--批量导入发货  -->
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>

    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>ImportDelivery</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!--日志中心-订单操作日志-->
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>OperateLog</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!--扫描打印-标签码日志-->
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>FhdTagLog</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!--扫描打印-商品信息-->
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>FhdTagTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!--扫描打印-ScanPrintLog-->
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>ScanPrintLog</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!--扫描打印-ScanTradeInfo-->
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>ScanTradeInfo</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!-- 发票信息 -->
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>

    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>InvoiceApply</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!--电子面单分享日志表-->
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>WaybillShareLog</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!--电子面单使用日志表-->
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>

    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>WaybillUseLog</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!-- 订单查询进度表 -->
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>

    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>TradeSearchProcess</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!--备货单历史记录-->
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>BhdHistory</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!--用户标记-->
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>

    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>UserMark</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!-- 裹裹记录表 -->
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>

    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>GuoguoOrderLog</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!-- 菜鸟隐私面单的申请日志表 -->
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>

    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>CnPrivacyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>


    <!--试用表-->
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>

    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>ProbationList</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!--解密日志记录表-->
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>

    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>DecryptLog</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>


    <!--模板项 -->
    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>

    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>

    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>

    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>

    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>

    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>

    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>

    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>

    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>

    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>

    <rule>
        <namespace>ModeListshowFk</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>AddressModifyLog</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>TradeBatchUpdateMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <!--备货单下载记录表  -->
    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>

    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>

    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>

    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>

    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>

    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>

    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>

    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>

    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>

    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>BhdDownloadRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>


    <!--首页点击记录表  -->
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>AdvertisementClickTimes</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>


    <!--底单数据扩展表  -->
    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>

    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>

    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>

    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>

    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>

    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>

    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>

    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>

    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>

    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>

    <rule>
        <namespace>PrintBgKddExtra</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!-- 虚拟号时间日志 -->
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>SecretNoTimeLog</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!-- 虚拟号解密额度日志 -->
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>DecryptQuotaLog</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!-- 登录日志 -->
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>LoginLog</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!-- 助手备注 -->
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>CustomMemo</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!--分销代发-->
    <rule>
        <namespace>AgentBindRel</namespace>
        <shards>masterFxdf</shards>
    </rule>
    <rule>
        <namespace>AgentGoodsSkuBindRel</namespace>
        <shards>masterFxdf</shards>
    </rule>
    <rule>
        <namespace>AgentOperationLog</namespace>
        <shards>masterFxdf</shards>
    </rule>
    <rule>
        <namespace>BillCenter</namespace>
        <shards>masterFxdf</shards>
    </rule>
    <rule>
        <namespace>AgentTrade</namespace>
        <shards>masterFxdf</shards>
    </rule>

    <!--fuwuOrder之前打入这个有问题的库-->
    <rule>
        <namespace>FuwuOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>

    <!--备货单自定义排序  -->
    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>

    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>

    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>

    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>

    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>

    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>

    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>

    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>

    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>

    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>

    <rule>
        <namespace>BhdCustomSortRule</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!--易甚订单推送日志表  -->
    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>

    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>

    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>

    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>

    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>

    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>

    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>

    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>

    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>

    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>

    <rule>
        <namespace>EsomOrderPushLog</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>

    <!--AI打单商品  -->
    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>

    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>

    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>

    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>

    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>

    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>

    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>

    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>

    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>

    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>

    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>

    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>

    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>

    <rule>
        <namespace>AIItem</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>AiTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>AiOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>AiSortTrade</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>AiSortOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfCategory</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfItem</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>AiSortTradeStatisticsOfSku</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>AiPrintRecord</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 1</shardingExpression>
        <shards>master0</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 2</shardingExpression>
        <shards>master2</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 3</shardingExpression>
        <shards>master3</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 4</shardingExpression>
        <shards>master4</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 5</shardingExpression>
        <shards>master5</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 6</shardingExpression>
        <shards>master6</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 7</shardingExpression>
        <shards>master7</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 8</shardingExpression>
        <shards>master8</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 9</shardingExpression>
        <shards>master9</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 10</shardingExpression>
        <shards>master10</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 11</shardingExpression>
        <shards>master11</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 12</shardingExpression>
        <shards>master12</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 13</shardingExpression>
        <shards>master13</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 14</shardingExpression>
        <shards>master14</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 15</shardingExpression>
        <shards>master15</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 16</shardingExpression>
        <shards>master16</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 17</shardingExpression>
        <shards>master17</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 18</shardingExpression>
        <shards>master18</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 19</shardingExpression>
        <shards>master19</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 20</shardingExpression>
        <shards>master20</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 21</shardingExpression>
        <shards>master21</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 22</shardingExpression>
        <shards>master22</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 23</shardingExpression>
        <shards>master23</shards>
    </rule>
    <rule>
        <namespace>AiPrintOrder</namespace>
        <shardingExpression>hash.apply(fkId) == 24</shardingExpression>
        <shards>master24</shards>
    </rule>
</rules>