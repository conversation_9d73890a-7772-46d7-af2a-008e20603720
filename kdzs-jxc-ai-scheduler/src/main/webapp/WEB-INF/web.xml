<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://java.sun.com/xml/ns/javaee" xmlns:web="http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
         xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-app_2_5.xsd"
         id="WebApp_ID" version="2.5">
    <display-name>kdzs-jxc-ai-scheduler</display-name>

    <context-param>
        <param-name>contextConfigLocation</param-name>
        <param-value>
            classpath*:/spring/spring-*.xml
        </param-value>
    </context-param>
    <context-param>
        <param-name>log4jConfigLocation</param-name>
        <param-value>classpath:log4j2.xml</param-value>
    </context-param>
    <context-param>
        <param-name>log4jRefreshInterval</param-name>
        <param-value>3000</param-value>
    </context-param>

    <context-param>
        <param-name>taobaosdk_app_key</param-name>
        <param-value>kdzs-task</param-value>
    </context-param>
    <context-param>
        <param-name>groupdataid</param-name>
        <param-value>12158997,23322766</param-value>
    </context-param>
    <context-param>
        <param-name>call_back_class_name</param-name>
        <param-value>com.kuaidizs.jxc.common.secret.CallBackSecret</param-value>
    </context-param>

    <context-param>
        <param-name>appId</param-name>
        <param-value>1024</param-value>
    </context-param>

    <context-param>
        <param-name>spring.profiles.active</param-name>
        <param-value>nacos-product</param-value>
    </context-param>

    <filter>
        <filter-name>encodingfilter</filter-name>
        <filter-class>org.springframework.web.filter.CharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>encodingfilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>


    <listener>
        <listener-class>org.springframework.web.util.Log4jConfigListener</listener-class>
    </listener>
    <listener>
        <listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
    </listener>

    <listener>
        <listener-class>com.raycloud.eagle.trace.http.InitAppIdListener</listener-class>
    </listener>

    <listener>
        <listener-class>com.raycloud.secretzk.client.ZkclientListener</listener-class>
    </listener>
    <context-param>
        <param-name>ocs_cache_xy</param-name>
        <param-value>kdzs-web.ocscacheVpc</param-value>
    </context-param>
    <listener>
        <listener-class>com.kuaidizs.spymemcached.extend.listener.InitOcsCacheListener</listener-class>
    </listener>


    <servlet>
        <servlet-name>Spring MVC Dispatcher Servlet</servlet-name>
        <servlet-class>org.springframework.web.servlet.DispatcherServlet</servlet-class>
        <init-param>
            <param-name>contextConfigLocation</param-name>
            <param-value>
                classpath*:/spring/spring-web-mvc.xml
            </param-value>
        </init-param>
        <load-on-startup>1</load-on-startup>
    </servlet>

    <servlet-mapping>
        <servlet-name>Spring MVC Dispatcher Servlet</servlet-name>
        <url-pattern>*.aspx</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>Spring MVC Dispatcher Servlet</servlet-name>
        <url-pattern>/</url-pattern>
    </servlet-mapping>

    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
        <welcome-file>/forward.jsp</welcome-file>
    </welcome-file-list>
</web-app>