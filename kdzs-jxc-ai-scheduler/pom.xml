<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <artifactId>kdzs-jxc</artifactId>
    <groupId>com.kuaidizs.print</groupId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <artifactId>kdzs-jxc-ai-scheduler</artifactId>
  <packaging>war</packaging>
  <name>kdzs-jxc-ai-scheduler</name>
  <version>1.0.0-SNAPSHOT</version>

  <properties>
    <!--xxjob-->
    <xxl.job.version>2.1.1-SNAPSHOT</xxl.job.version>
  </properties>

  <dependencies>

    <!--xxjob-->
    <dependency>
      <groupId>com.xuxueli</groupId>
      <artifactId>xxl-job-core</artifactId>
      <version>${xxl.job.version}</version>
    </dependency>

    <dependency>
      <artifactId>netty-all</artifactId>
      <groupId>io.netty</groupId>
      <version>4.1.58.Final</version>
    </dependency>
    <dependency>
      <groupId>org.redisson</groupId>
      <artifactId>redisson</artifactId>
    </dependency>


    <dependency>
      <groupId>com.raycloud.middle.datax</groupId>
      <artifactId>middle-datax-api</artifactId>
    </dependency>

    <dependency>
      <groupId>com.taobao.metamorphosis</groupId>
      <artifactId>metamorphosis-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.kuaidizs.print</groupId>
      <artifactId>kdzs-jxc-service</artifactId>
      <version>${project.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>jackson-core-asl</artifactId>
          <groupId>org.codehaus.jackson</groupId>
        </exclusion>
        <exclusion>
          <artifactId>jackson-mapper-asl</artifactId>
          <groupId>org.codehaus.jackson</groupId>
        </exclusion>
        <exclusion>
          <artifactId>netty-all</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <!-- spymemcached ocs缓存扩展 -->
    <dependency>
      <artifactId>kdzs-spymemcached-extend</artifactId>
      <groupId>com.kuaidizs.print</groupId>
      <version>1.0.0-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>org.quartz-scheduler</groupId>
      <artifactId>quartz-jobs</artifactId>
      <version>${version.quartz}</version>
    </dependency>
    <dependency>
      <groupId>org.quartz-scheduler</groupId>
      <artifactId>quartz</artifactId>
      <version>${version.quartz}</version>
    </dependency>
    <dependency>
      <groupId>com.raycloud</groupId>
      <artifactId>ray-session-web-support</artifactId>
    </dependency>
    <dependency>
      <groupId>com.aliyun.openservices</groupId>
      <artifactId>ons-client</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>netty-all</artifactId>
          <groupId>io.netty</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-csv</artifactId>
      <version>1.5</version>
    </dependency>

    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-compress</artifactId>
      <version>1.22</version>
    </dependency>

    <dependency>
      <groupId>com.raycloud.live.basic</groupId>
      <artifactId>live-basic-adapter</artifactId>
      <version>1.1.3-SNAPSHOT</version>
    </dependency>

  </dependencies>

  <profiles>
    <profile>
      <id>dev</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <properties>
        <configPath>src/main/resources/config/dev</configPath>
      </properties>
      <build>
        <filters>
          <filter>src/main/resources/profile/dev.properties</filter>
        </filters>
        <resources>
          <resource>
            <directory>src/main/resources</directory>
            <filtering>true</filtering>
          </resource>
          <resource>
            <directory>src/main/resources/config/dev</directory>
            <filtering>true</filtering>
          </resource>
        </resources>
      </build>
    </profile>

    <profile>
      <id>test</id>
      <properties>
        <activeProfile>nacos-test</activeProfile>
        <configPath>src/main/resources/config/test</configPath>
        <webXmlPath>src/main/resources/config/test/webxml</webXmlPath>
      </properties>
      <build>
        <filters>
          <filter>src/main/resources/profile/test.properties</filter>
        </filters>
        <resources>
          <resource>
            <directory>src/main/resources</directory>
            <filtering>true</filtering>
          </resource>
          <resource>
            <directory>src/main/resources/config/test</directory>
            <filtering>true</filtering>
          </resource>
        </resources>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-war-plugin</artifactId>
            <version>2.1.1</version>
            <configuration>
              <webResources>
                <resource>
                  <directory>${webXmlPath}</directory>
                  <targetPath>WEB-INF</targetPath>
                </resource>
              </webResources>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>production</id>
      <properties>
        <activeProfile>nacos-product</activeProfile>
        <configPath>src/main/resources/config/production</configPath>
      </properties>
      <build>
        <filters>
          <filter>src/main/resources/profile/production.properties</filter>
          <filter>src/main/resources/profile/sd/p1_sd.properties</filter>
        </filters>
        <resources>
          <resource>
            <directory>src/main/resources</directory>
            <filtering>true</filtering>
          </resource>
          <resource>
            <directory>src/main/resources/config/production</directory>
            <filtering>true</filtering>
          </resource>
        </resources>
      </build>
    </profile>
  </profiles>

  <build>
    <finalName>kdzs-ai-task</finalName>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-resources-plugin</artifactId>
        <version>2.6</version>
        <configuration>
          <encoding>UTF-8</encoding>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.tomcat.maven</groupId>
        <artifactId>tomcat7-maven-plugin</artifactId>
        <version>2.0</version>
        <configuration>
          <port>80</port>
          <path>/</path>
          <url>http://job.qileroro.com</url>
          <uriEncoding>utf-8</uriEncoding>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-war-plugin</artifactId>
        <version>2.1.1</version>
        <!--<configuration>-->
        <!--<webResources>-->
        <!--<resource>-->
        <!--<directory>${configPath}</directory>-->
        <!--<targetPath>WEB-INF/classes</targetPath>-->
        <!--</resource>-->
        <!--</webResources>-->
        <!--</configuration>-->
      </plugin>
      <!-- 单元测试插件:跳过单元测试 -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <version>2.7.2</version>
        <configuration>
          <skip>true</skip>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
