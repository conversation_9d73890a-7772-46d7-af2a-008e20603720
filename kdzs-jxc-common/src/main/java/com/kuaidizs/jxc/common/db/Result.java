package com.kuaidizs.jxc.common.db;

import java.util.ArrayList;
import java.util.List;
import java.io.Serializable;


/**
 * <AUTHOR> (<EMAIL>)
 */
public class Result<T> implements Serializable {
    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    private boolean isSuccess = true;
    private List<T> list;
    private int count;
    private String errorMsg;

    private Long pageSize;
    private Long pageNo;

    public Long getPageSize() {
        return pageSize;
    }

    public void setPageSize(Long pageSize) {
        this.pageSize = pageSize;
    }

    public Long getPageNo() {
        return pageNo;
    }

    public void setPageNo(Long pageNo) {
        this.pageNo = pageNo;
    }

    private List<String> invalidSessionShopNames=new ArrayList<>(16);

    public boolean isSuccess() {
        return isSuccess;
    }

    public void setSuccess(boolean isSuccess) {
        this.isSuccess = isSuccess;
    }

    public List<T> getList() {
        return list;
    }

    public void setList(List<T> list) {
        this.list = list;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = count;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public List<String> getInvalidSessionShopNames() {
        return invalidSessionShopNames;
    }

    public void setInvalidSessionShopNames(List<String> invalidSessionShopNames) {
        this.invalidSessionShopNames = invalidSessionShopNames;
    }
}
