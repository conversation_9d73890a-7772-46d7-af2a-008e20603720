package com.kuaidizs.jxc.common.context;

import com.alibaba.ttl.TransmittableThreadLocal;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Optional;

/**
 * 请求上下文持有对象
 */
public class ActionContextHolder {

    /**
     * 可子父线程继承ThreadLocal
     */
    private static final ThreadLocal<ActionContext> ACTION_CONTEXT_THREAD_LOCAL = new TransmittableThreadLocal<ActionContext>();


    public static void clear() {
        ACTION_CONTEXT_THREAD_LOCAL.remove();
    }

    public static void addActionContext(ActionContext actionContext) {
        ACTION_CONTEXT_THREAD_LOCAL.set(actionContext);
    }

    public static ActionContext.UserInfo getUserInfo() {
        return ACTION_CONTEXT_THREAD_LOCAL.get() == null ? null : ACTION_CONTEXT_THREAD_LOCAL.get().getUserInfo();
    }

    public static HttpServletRequest getServletRequest() {
        return ACTION_CONTEXT_THREAD_LOCAL.get() == null ? null : ACTION_CONTEXT_THREAD_LOCAL.get().getHttpServletRequest();
    }

    public static HttpServletResponse getServletResponse() {
        return ACTION_CONTEXT_THREAD_LOCAL.get() == null ? null : ACTION_CONTEXT_THREAD_LOCAL.get().getHttpServletResponse();
    }


    public static Optional<ActionContext.UserInfo> getUserInfoOptional() {
        return Optional.ofNullable(getUserInfo());
    }

    public static Long getTaobaoId() {
        return getUserInfoOptional().map(ActionContext.UserInfo::getTaobaoId).orElse(null);
    }

    public static String getUserNick() {
        return getUserInfoOptional().map(ActionContext.UserInfo::getUserNick).orElse(null);
    }

    public static String getAccessToken() {
        return getUserInfoOptional().map(ActionContext.UserInfo::getAccessToken).orElse(null);
    }

    public static String getSubUserNick() {
        return getUserInfoOptional().map(ActionContext.UserInfo::getSubUserNick).orElse(null);
    }

    public static Integer getUserLevel() {
        return getUserInfoOptional().map(ActionContext.UserInfo::getUserLevel).orElse(null);
    }

    public static String getShopType() {
        return getUserInfoOptional().map(ActionContext.UserInfo::getShopType).orElse(null);
    }

    public static Long getSubTaobaoId() {
        return getUserInfoOptional().map(ActionContext.UserInfo::getSubTaobaoId).orElse(null);
    }

    public static String getSourceIp() {
        return getUserInfoOptional().map(ActionContext.UserInfo::getSourceIp).orElse(null);
    }


    public static String getLoginEntrance() {
        return getUserInfoOptional().map(ActionContext.UserInfo::getLoginEntrance).orElse(null);
    }

    public static String getLoginDevice() {
        return getUserInfoOptional().map(ActionContext.UserInfo::getLoginDevice).orElse(null);
    }

    public static String getXcxVersion() {
        return getUserInfoOptional().map(ActionContext.UserInfo::getXcxVersion).orElse(null);
    }

    public static String getQnVersion() {
        return getUserInfoOptional().map(ActionContext.UserInfo::getQnVersion).orElse(null);
    }

    public static String getSystemType() {
        return getUserInfoOptional().map(ActionContext.UserInfo::getSystemType).orElse(null);
    }

    public static String getSystemVersion() {
        return getUserInfoOptional().map(ActionContext.UserInfo::getSystemVersion).orElse(null);
    }

    public static String getXUmt() {
        return getUserInfoOptional().map(ActionContext.UserInfo::getXUmt).orElse(null);
    }

    public static String getClientType() {
        return getUserInfoOptional().map(ActionContext.UserInfo::getClientType).orElse(null);
    }
}
