package com.kuaidizs.jxc.common.convert;

import com.kuaidizs.jxc.domain.agent.BillCenter;
import com.kuaidizs.jxc.vo.agent.billCenter.BillCenterDetailVo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @date 2022/12/14.
 * @time 20:57.
 */
@Mapper
public interface BillBizConvert {
    BillBizConvert INSTANCE =  Mappers.getMapper(BillBizConvert.class);


    BillCenterDetailVo BillCenter2BillDetailVo(BillCenter billCenter);

}
