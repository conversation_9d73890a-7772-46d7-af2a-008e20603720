package com.kuaidizs.jxc.common.diamond;

import static com.kuaidizs.jxc.common.diamond.CallMode.CallModeValue.CAINIAO_VALUE;
import static com.kuaidizs.jxc.common.diamond.CallMode.CallModeValue.WANGDIAN_VALUE;

/**
 * Description:
 * User:<PERSON><PERSON> (x<PERSON><PERSON><PERSON><PERSON>@raycloud.com)
 * Date: 2017/5/15
 * Time: 14:13
 * Version: 1.0
 **/
public enum CallMode {

    CAINIAO(CAINIAO_VALUE),
    WANGDIAN(WANGDIAN_VALUE);

    private int value;

    CallMode(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public class CallModeValue {
        /**
         * 菜鸟
         */
        public final static int CAINIAO_VALUE = 1;
        /**
         * 网点
         */
        public final static int WANGDIAN_VALUE = 0;
    }
}
