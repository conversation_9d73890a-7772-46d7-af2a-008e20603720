package com.kuaidizs.jxc.common.cache.support;


import com.kuaidizs.jxc.common.cache.ICache;

/**
 * 针对NoOption操作的缓存ICache实现处理
 * <p>
 * {@link org.springframework.cache.support.NoOpCacheManager}
 *
 * <AUTHOR>
 * @date 2018/6/1
 */
public class NoOptionCache implements ICache {

    @Override
    public String getName() {
        return null;
    }

    @Override
    public Object getNativeCache() {
        return null;
    }

    @Override
    public <T> T get(String key)  {
        return null;
    }

    @Override
    public Object getObject(String key)  {
        return null;
    }

    @Override
    public <T> T getAndTouch(String key, int exp)  {
        return null;
    }

    @Override
    public void incr(String key, long by)  {

    }

    @Override
    public void decr(String key, long by)  {

    }

    @Override
    public boolean add(String key, int exp, Object o)  {
        return true;
    }

    @Override
    public boolean set(String key, int exp, Object o)  {
        return true;
    }

    @Override
    public boolean set(String key, Object o)  {
        return true;
    }

    @Override
    public boolean delete(String key)  {
        return true;
    }
}
