package com.kuaidizs.jxc.common.cache.memcache;

import com.kuaidizs.jxc.common.cache.ICache;
import org.apache.log4j.Logger;
import org.springframework.cache.Cache;
import org.springframework.cache.support.SimpleValueWrapper;

import java.util.concurrent.Callable;

/**
 * spring的Cache和本地的ICache转化接口
 *
 * <AUTHOR>
 * @date 2018/5/28
 */
public class AdapterCache implements Cache {

    public static final Logger logger = Logger.getLogger(AdapterCache.class);

    private ICache cache;

    private Integer expire;

    public AdapterCache(ICache cache) {
        this.cache = cache;
    }

    public Integer getExpire() {
        return expire;
    }

    public void setExpire(Integer expire) {
        this.expire = expire;
    }

    @Override
    public String getName() {
        return cache.getName();
    }

    @Override
    public Object getNativeCache() {
        return cache.getNativeCache();
    }

    @Override
    public ValueWrapper get(Object key) {
        Object value = getOrigin(key);
        return null == value ? null : new SimpleValueWrapper(value);
    }

    @Override
    public <T> T get(Object o, Class<T> aClass) {
        return null;
    }

    @Override
    public <T> T get(Object o, Callable<T> callable) {
        return null;
    }

    @Override
    public void put(Object key, Object value) {
        if (key == null) {
            return;
        }
        if (value == null) {
            return;
        }
        try {
            if (expire == null) {
                cache.set(key.toString(), value);
            } else {
                cache.set(key.toString(), expire, value);
            }
        } catch (Exception e) {
            logger.error("put cache error,message:" + e.getMessage(), e);
        }
    }

    @Override
    public ValueWrapper putIfAbsent(Object o, Object o1) {
        return null;
    }


    @Override
    public void evict(Object key) {
        if (key == null) {
            return;
        }
        try {
            cache.delete(key.toString());
        } catch (Exception e) {
            logger.error("delete cache error,message:" + e.getMessage());
        }
    }

    @Override
    public void clear() {
        throw new UnsupportedOperationException("can not clear cache.");
    }

    private Object getOrigin(Object key) {
        if (null == key) {
            return null;
        }
        try {
            return cache.get(key.toString());
        } catch (Exception e) {
            logger.error("get cache error,message:" + e.getMessage(), e);
        }
        return null;
    }
}
