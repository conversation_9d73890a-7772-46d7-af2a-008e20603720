package com.kuaidizs.jxc.common.diamond;

import org.apache.commons.lang.StringUtils;

import java.io.Serializable;


public class CpDeliveryLimit implements Serializable {

    private static final long serialVersionUID = -1134471319809855892L;

    private String cpCode;

    private String prefix;

    private int length;

    public CpDeliveryLimit() {
    }

    public CpDeliveryLimit(String cpCode, String prefix, int length) {
        this.cpCode = cpCode;
        this.prefix = prefix;
        this.length = length;
    }

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        if (StringUtils.isNotBlank(cpCode)) {
            cpCode = cpCode.toUpperCase();
        }
        this.cpCode = cpCode;
    }

    public String getPrefix() {
        return prefix;
    }

    public void setPrefix(String prefix) {
        this.prefix = prefix;
    }

    public int getLength() {
        return length;
    }

    public void setLength(int length) {
        this.length = length;
    }
}
