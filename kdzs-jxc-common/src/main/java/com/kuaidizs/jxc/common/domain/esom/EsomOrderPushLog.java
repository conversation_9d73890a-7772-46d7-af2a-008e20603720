package com.kuaidizs.jxc.common.domain.esom;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * Date    2024-07-22
 */
public class EsomOrderPushLog implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 自增ID
     */
    private Long id;
	/**
     *  用户ID
     */
    private Long userId;
	/**
     * 店铺名称
     */
    private String shopName;
	/**
     * 订单ID
     */
    private String tid;
	/**
     * skuId
     */
    private String skuId;
    /**
     * sku
     */
    private String sku;
	/**
     * 快递单号
     */
    private String exNumber;
	/**
     * 快递公司
     */
    private String exName;
	/**
     * 操作人名称
     */
    private String operator;
	/**
     * 推送易甚账号
     */
    private String ysCode;
	/**
     * 操作结果 0失败 1成功
     */
    private Integer operationResult;
	/**
     * 创建时间
     */
    private Date created;
	/**
     * 修改时间
     */
    private Date modifyed;

    /**
     * 备注
     */
    private String remark;


    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
   /**
    * @return id 自增ID
    */
    public Long getId() {
       return id;
    }
   /**
    * @param id 自增ID
    */
    public void setId(Long id) {
       this.id = id;
    }
	
   /**
    * @return userId  用户ID
    */
    public Long getUserId() {
       return userId;
    }
   /**
    * @param userId  用户ID
    */
    public void setUserId(Long userId) {
       this.userId = userId;
    }
	
   /**
    * @return shopName 店铺名称
    */
    public String getShopName() {
       return shopName;
    }
   /**
    * @param shopName 店铺名称
    */
    public void setShopName(String shopName) {
       this.shopName = shopName;
    }
	
   /**
    * @return tid 订单ID
    */
    public String getTid() {
       return tid;
    }
   /**
    * @param tid 订单ID
    */
    public void setTid(String tid) {
       this.tid = tid;
    }
	
   /**
    * @return skuId skuId
    */
    public String getSkuId() {
       return skuId;
    }
   /**
    * @param skuId skuId
    */
    public void setSkuId(String skuId) {
       this.skuId = skuId;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    /**
    * @return exNumber 快递单号
    */
    public String getExNumber() {
       return exNumber;
    }
   /**
    * @param exNumber 快递单号
    */
    public void setExNumber(String exNumber) {
       this.exNumber = exNumber;
    }
	
   /**
    * @return exName 快递公司
    */
    public String getExName() {
       return exName;
    }
   /**
    * @param exName 快递公司
    */
    public void setExName(String exName) {
       this.exName = exName;
    }
	
   /**
    * @return operator 操作人名称
    */
    public String getOperator() {
       return operator;
    }
   /**
    * @param operator 操作人名称
    */
    public void setOperator(String operator) {
       this.operator = operator;
    }

    public String getYsCode() {
        return ysCode;
    }

    public void setYsCode(String ysCode) {
        this.ysCode = ysCode;
    }

    /**
    * @return operationResult 操作结果
    */
    public Integer getOperationResult() {
       return operationResult;
    }
   /**
    * @param operationResult 操作结果
    */
    public void setOperationResult(Integer operationResult) {
       this.operationResult = operationResult;
    }
	
   /**
    * @return created 创建时间
    */
    public Date getCreated() {
       return created;
    }
   /**
    * @param created 创建时间
    */
    public void setCreated(Date created) {
       this.created = created;
    }
	
   /**
    * @return modifyed 修改时间
    */
    public Date getModifyed() {
       return modifyed;
    }
   /**
    * @param modifyed 修改时间
    */
    public void setModifyed(Date modifyed) {
       this.modifyed = modifyed;
    }

}