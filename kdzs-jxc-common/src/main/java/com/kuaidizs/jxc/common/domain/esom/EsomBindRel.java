package com.kuaidizs.jxc.common.domain.esom;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR>
 * Date    2024-07-22
 */
public class EsomBindRel implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 主键
     */
    private Long id;
	/**
     * 助手用户id
     */
    private String userId;
	/**
     * 易甚账号
     */
    private String ysCode;
	/**
     * 创建时间
     */
    private Date created;
	/**
     * 更新时间
     */
    private Date modified;

	
   /**
    * @return id 主键
    */
    public Long getId() {
       return id;
    }
   /**
    * @param id 主键
    */
    public void setId(Long id) {
       this.id = id;
    }
	
   /**
    * @return userId 助手用户id
    */
    public String getUserId() {
       return userId;
    }
   /**
    * @param userId 助手用户id
    */
    public void setUserId(String userId) {
       this.userId = userId;
    }
	
   /**
    * @return ysCode 易甚账号
    */
    public String getYsCode() {
       return ysCode;
    }
   /**
    * @param ysCode 易甚账号
    */
    public void setYsCode(String ysCode) {
       this.ysCode = ysCode;
    }
	
   /**
    * @return created 创建时间
    */
    public Date getCreated() {
       return created;
    }
   /**
    * @param created 创建时间
    */
    public void setCreated(Date created) {
       this.created = created;
    }
	
   /**
    * @return modified 更新时间
    */
    public Date getModified() {
       return modified;
    }
   /**
    * @param modified 更新时间
    */
    public void setModified(Date modified) {
       this.modified = modified;
    }

}