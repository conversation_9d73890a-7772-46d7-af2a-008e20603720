package com.kuaidizs.jxc.common.context;


import lombok.Data;

import java.io.Serializable;

@Data
public class ResponseBody<T> implements Serializable {


    private static final long serialVersionUID = 1L;
    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 官方返回码
     */
    private Integer errorCode;

    /**
     * 返回消息
     */
    private String errorMessage;

    /**
     * 返回数据
     */
    private T data;

    /**
     * 请求的接口名
     */
    private String apiName;

    public static <T> ResponseBody OK(T data, String apiName) {
        ResponseBody responseBody = new ResponseBody();
        responseBody.setData(data);
        responseBody.setApiName(apiName);
        return responseBody;
    }

    public static <T> ResponseBody ERROR(String message, int result, String apiName) {
        ResponseBody responseBody = new ResponseBody();
        responseBody.setErrorCode(result);
        responseBody.setSuccess(Boolean.FALSE);
        responseBody.setErrorMessage(message);
        responseBody.setApiName(apiName);
        return responseBody;
    }

    public static <T> ResponseBody ERROR(String apiName) {
        ResponseBody responseBody = new ResponseBody();
        responseBody.setErrorCode(400);
        responseBody.setSuccess(Boolean.FALSE);
        responseBody.setErrorMessage("系统处理异常");
        responseBody.setApiName(apiName);
        return responseBody;
    }
}
