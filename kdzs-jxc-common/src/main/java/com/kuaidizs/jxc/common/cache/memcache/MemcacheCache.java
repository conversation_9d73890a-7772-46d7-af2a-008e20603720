package com.kuaidizs.jxc.common.cache.memcache;

import com.kuaidizs.jxc.common.cache.ICache;
import net.spy.memcached.CASValue;
import net.spy.memcached.MemcachedClient;
import org.apache.commons.lang.StringUtils;

/**
 * memcache 缓存实现
 * <p>
 * 注：缓存的添加删除等操作，需要处理返回结果OperationFuture<Boolean>，后期考虑处理，目前全部返回true
 *
 * <AUTHOR>
 * @date 2018/5/28
 */
public class MemcacheCache implements ICache {

    private static final String DEFAULT = "default";

    /**
     * 默认时间采取缓存存放的最大时间1个月
     */
    private static final int DEFAULT_EXPIRE = 24 * 3600 * 30;

    /**
     * 缓存名称
     */
    private String name;

    /**
     * 缓存
     */
    private MemcachedClient client;

    /**
     * 缓存key前缀
     */
    private String keyPrefix;

    public MemcacheCache(MemcachedClient client) {
        this(DEFAULT, client);
    }

    public MemcacheCache(String name, MemcachedClient client) {
        this.name = name;
        this.client = client;
    }

    public MemcachedClient getClient() {
        return client;
    }


    public String getKeyPrefix() {
        return keyPrefix;
    }

    public void setKeyPrefix(String keyPrefix) {
        this.keyPrefix = keyPrefix;
    }

    /**
     * 生成唯一的key
     *
     * @param key
     * @return
     */
    private String generatorKey(String key) {
        if (StringUtils.isBlank(keyPrefix)) {
            return key;
        } else {
            return keyPrefix + key;
        }
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public Object getNativeCache() {
        return client;
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T get(String key) {
        return (T) client.get(generatorKey(key));
    }

    @Override
    public Object getObject(String key) {
        return client.get(generatorKey(key));
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T getAndTouch(String key, int exp) {
        CASValue<Object> casValue = client.getAndTouch(generatorKey(key), exp);
        return (T) casValue.getValue();
    }

    @Override
    public void incr(String key, long by) {
        client.incr(generatorKey(key), by);
    }

    @Override
    public void decr(String key, long by) {
        client.decr(generatorKey(key), by);
    }

    @Override
    public boolean add(String key, int exp, Object o) {
        client.add(generatorKey(key), exp, o);
        return true;
    }

    @Override
    public boolean set(String key, int exp, Object o) {
        client.set(generatorKey(key), exp, o);
        return true;
    }

    @Override
    public boolean set(String key, Object o) {
        client.set(generatorKey(key), DEFAULT_EXPIRE, o);
        return true;
    }

    @Override
    public boolean delete(String key) {
        client.delete(generatorKey(key));
        return true;
    }
}
