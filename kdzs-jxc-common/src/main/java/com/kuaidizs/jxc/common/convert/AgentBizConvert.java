package com.kuaidizs.jxc.common.convert;


import com.kuaidizs.jxc.domain.agent.AgentBindRel;
import com.kuaidizs.jxc.domain.agent.AgentGoodsBindRel;
import com.kuaidizs.jxc.domain.agent.AgentGoodsSkuBindRel;
import com.kuaidizs.jxc.domain.agent.AgentOperationLog;
import com.kuaidizs.jxc.query.agent.AgentGoodsQuery;
import com.kuaidizs.jxc.vo.agent.AgentOperationLogInfo;
import com.kuaidizs.jxc.vo.agent.factory.AgentBindAndUnbindRequest;
import com.kuaidizs.jxc.vo.agent.factory.AgentFactoryInfo;
import com.kuaidizs.jxc.vo.agent.factory.ModifyAgentSenderInfoRequest;
import com.kuaidizs.jxc.vo.agent.goods.AgentGoodsInfo;
import com.kuaidizs.jxc.vo.agent.goods.AgentGoodsPageRequest;
import com.kuaidizs.jxc.vo.agent.goods.AgentGoodsSkuInfo;
import com.kuaidizs.jxc.vo.agent.mall.AgentMallInfo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 代理业务 厂家 分销商 商品转换类
 *
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AgentBizConvert.java
 * @createTime 2022年12月12日 13:41:00
 */
@Mapper
public interface AgentBizConvert {
	AgentBizConvert INSTANCE = Mappers.getMapper(AgentBizConvert.class);

	@Mappings({
			@Mapping(source = "factoryUserId",target = "factoryId"),
			@Mapping(source = "factoryUserName",target = "factoryName"),
	})
	AgentFactoryInfo agentBindRel2FactoryInfo(AgentBindRel bindRel);


	AgentBindRel agentAddressModify2AgentBind(ModifyAgentSenderInfoRequest factoryInfo);


	AgentBindRel bindAndUnbindReq2AgentBind(AgentBindAndUnbindRequest factoryInfo);

	@Mappings({
			@Mapping(source = "operationUserName",target = "operationUser")
	})
	AgentOperationLogInfo agentOperationLog2LogInfo(AgentOperationLog operationLog);

	@Mappings({
			@Mapping(source = "mallUserId",target = "mallId"),
			@Mapping(source = "mallUserName",target = "mallName"),
	})
	AgentMallInfo agentBindRel2DistributorInfo(AgentBindRel bindRel);

	AgentGoodsQuery agentGoodsPageReq2GoodsQuery(AgentGoodsPageRequest goodsPageRequest);

	AgentGoodsSkuInfo goodsSkuBindRel2SkuInfo(AgentGoodsSkuBindRel bindRel);

	AgentGoodsInfo goodsBindRel2GoodsInfo(AgentGoodsBindRel goodsBindRel);
}
