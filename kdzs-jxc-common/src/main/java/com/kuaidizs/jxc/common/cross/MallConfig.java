package com.kuaidizs.jxc.common.cross;

import com.kuaidizs.jw.domain.common.BasePojo;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> (x<PERSON><PERSON><PERSON><PERSON>@raycloud.com)
 * @date 2018-05-17
 */
@Data
public class MallConfig extends BasePojo implements Serializable {


    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;
    /**
     * user_id
     */
    private Long userId;
    /**
     * mode_id
     */
    private String modeId;
    /**
     * sender_name
     */
    private String senderName;
    /**
     * sender_tel
     */
    private String senderTel;
    /**
     * send_qm
     */
    private String sendQm;
    /**
     * send_zip
     */
    private String sendZip;
    /**
     * send_addr
     */
    private String sendAddr;
    /**
     * fhd_shop_name
     */
    private String fhdShopName;
    /**
     * fhd_title
     */
    private String fhdTitle;
    /**
     * fhd_name
     */
    private String fhdName;
    /**
     * fhd_tel
     */
    private String fhdTel;
    /**
     * fhd_info
     */
    private String fhdInfo;
    /**
     * tb_link_code
     */
    private String tbLinkCode;
    /**
     * sender_province
     */
    private String senderProvince;
    /**
     * sender_city
     */
    private String senderCity;
    /**
     * sender_county
     */
    private String senderCounty;

    /**
     * 是否是默认发件人 0否 1是
     */
    private Integer isDefault;


    /**
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return userId user_id
     */
    public Long getUserId() {
        return userId;
    }

    /**
     * @param userId user_id
     */
    public void setUserId(Long userId) {
        this.userId = userId;
    }

    /**
     * @return modeId mode_id
     */
    public String getModeId() {
        return modeId;
    }

    /**
     * @param modeId mode_id
     */
    public void setModeId(String modeId) {
        this.modeId = modeId;
    }

    /**
     * @return senderName sender_name
     */
    public String getSenderName() {
        return senderName;
    }

    /**
     * @param senderName sender_name
     */
    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    /**
     * @return senderTel sender_tel
     */
    public String getSenderTel() {
        return senderTel;
    }

    /**
     * @param senderTel sender_tel
     */
    public void setSenderTel(String senderTel) {
        this.senderTel = senderTel;
    }

    /**
     * @return sendQm send_qm
     */
    public String getSendQm() {
        return sendQm;
    }

    /**
     * @param sendQm send_qm
     */
    public void setSendQm(String sendQm) {
        this.sendQm = sendQm;
    }

    /**
     * @return sendZip send_zip
     */
    public String getSendZip() {
        return sendZip;
    }

    /**
     * @param sendZip send_zip
     */
    public void setSendZip(String sendZip) {
        this.sendZip = sendZip;
    }

    /**
     * @return sendAddr send_addr
     */
    public String getSendAddr() {
        return sendAddr;
    }

    /**
     * @param sendAddr send_addr
     */
    public void setSendAddr(String sendAddr) {
        this.sendAddr = sendAddr;
    }

    /**
     * @return fhdShopName fhd_shop_name
     */
    public String getFhdShopName() {
        return fhdShopName;
    }

    /**
     * @param fhdShopName fhd_shop_name
     */
    public void setFhdShopName(String fhdShopName) {
        this.fhdShopName = fhdShopName;
    }

    /**
     * @return fhdTitle fhd_title
     */
    public String getFhdTitle() {
        return fhdTitle;
    }

    /**
     * @param fhdTitle fhd_title
     */
    public void setFhdTitle(String fhdTitle) {
        this.fhdTitle = fhdTitle;
    }

    /**
     * @return fhdName fhd_name
     */
    public String getFhdName() {
        return fhdName;
    }

    /**
     * @param fhdName fhd_name
     */
    public void setFhdName(String fhdName) {
        this.fhdName = fhdName;
    }

    /**
     * @return fhdTel fhd_tel
     */
    public String getFhdTel() {
        return fhdTel;
    }

    /**
     * @param fhdTel fhd_tel
     */
    public void setFhdTel(String fhdTel) {
        this.fhdTel = fhdTel;
    }

    /**
     * @return fhdInfo fhd_info
     */
    public String getFhdInfo() {
        return fhdInfo;
    }

    /**
     * @param fhdInfo fhd_info
     */
    public void setFhdInfo(String fhdInfo) {
        this.fhdInfo = fhdInfo;
    }

    /**
     * @return tbLinkCode tb_link_code
     */
    public String getTbLinkCode() {
        return tbLinkCode;
    }

    /**
     * @param tbLinkCode tb_link_code
     */
    public void setTbLinkCode(String tbLinkCode) {
        this.tbLinkCode = tbLinkCode;
    }

    /**
     * @return senderProvince sender_province
     */
    public String getSenderProvince() {
        return senderProvince;
    }

    /**
     * @param senderProvince sender_province
     */
    public void setSenderProvince(String senderProvince) {
        this.senderProvince = senderProvince;
    }

    /**
     * @return senderCity sender_city
     */
    public String getSenderCity() {
        return senderCity;
    }

    /**
     * @param senderCity sender_city
     */
    public void setSenderCity(String senderCity) {
        this.senderCity = senderCity;
    }

    /**
     * @return senderCounty sender_county
     */
    public String getSenderCounty() {
        return senderCounty;
    }

    /**
     * @param senderCounty sender_county
     */
    public void setSenderCounty(String senderCounty) {
        this.senderCounty = senderCounty;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }
}