package com.kuaidizs.jxc.common.enums.ad;

import java.util.Arrays;

/**
 * 广告展示枚举类
 * <AUTHOR>
 * @date 2022/7/18.
 * @time 13:48.
 */
public enum AdvertisementEnum {

    /**
     * 异常订单检测
     */
    ABNORMAL_ORDER("2", "异常订单检测不再提醒"),


    /**
     * 物流升级-打印拦截不再提醒
     */
    LOGISTICS_UPGRADE_PRINT("3", "物流升级-打印拦截不再提醒"),

    /**
     * 物流升级-发货拦截不再提醒
     */
    LOGISTICS_UPGRADE_DELIVER("4", "物流升级-发货拦截不再提醒"),


    /**
     * 物流升级-手动合单-同旺旺、同收货地址、不同指定物流公司
     */
    LOGISTICS_UPGRADE_MERGE_DIFF_EX("5", "物流升级-手动合单不同物流公司"),

    /**
     * 物流升级-手动合单-同旺旺、同收货地址、同指定物流公司，不同服务的订单
     */
    LOGISTICS_UPGRADE_MERGE_DIFF_ADS("6", "物流升级-手动合单不同服务的订单"),

    /**
     * 在线下单不再提醒
     */
    DELIVERY_ONLINE("7", "在线下单发货不再提醒"),

    /**
     * 打印快递单耗费解密额度不再提醒
     */
    PRINT_KDD_EXPEND_DECRYPT_QUOTA("8", "打印快递单耗费解密额度不再提醒"),

    /**
     * 打印发货单耗费解密额度不再提醒
     */
    PRINT_FHD_EXPEND_DECRYPT_QUOTA("9", "打印发货单耗费解密额度不再提醒"),

    /**
     * 解密提示优化不再提示
     */
    DECRYPT_QUOTA_OPTIMIZATION("10", "解密提示优化不再提示"),

    /**
     * 快递区域立即前往不再提示
     */
    EXPRESS_ARRIVE("11", "快递区域立即前往不再提示"),

    /**
     * 删除手工单权限不再提醒
     */
    DELETE_HAND_ORDER_PERMISSION("12", "删除手工单权限不再提醒"),

    /**
     * 底单重量单位不再提示
     */
    BG_WEIGHT_UNIT("13", "底单重量单位不再提示"),

    /**
     * PC小程序新手引导不再提示
     */
    PC_USER_GUIDE("14", "PC小程序新手引导不再提示");




    private String code;

    private String name;

    AdvertisementEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据code获取
     * @param code
     * @return
     */
    public static AdvertisementEnum ofCode(String code) {
        return Arrays.stream(AdvertisementEnum.values())
                .filter(e -> e.getCode().equals(code)).findFirst().orElse(AdvertisementEnum.ABNORMAL_ORDER);
    }
}
