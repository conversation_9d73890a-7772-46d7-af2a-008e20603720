package com.kuaidizs.jxc.common.cache;

/**
 * 缓存的操作接口信息
 *
 * <AUTHOR>
 * @date 2018/5/28
 */
public interface ICache {

    /**
     * Return the cache name.
     */
    String getName();

    /**
     * 获取原始的缓存提供者
     * such as: return memcache,hashMap and so on.
     */
    Object getNativeCache();

    /**
     * 根据key获取缓存信息
     *
     * @param key 缓存key
     * @param <T> 返回结果
     * @return
     */
    <T> T get(String key);

    /**
     * 根据key获取原始的数据信息
     *
     * @param key 缓存key
     * @return
     */
    Object getObject(String key);

    /**
     * 获取key缓存，并且重置它的时间
     *
     * @param key the key to get
     * @param exp the new expiration for the key
     * @param <T>
     * @return
     */
    <T> T getAndTouch(String key, int exp);

    /**
     * 根据key延长缓存的时间
     *
     * @param key the key
     * @param by  the amount to increment
     */
    void incr(String key, long by);

    /**
     * 减少key缓存的时间
     *
     * @param key the key
     * @param by  the value
     */
    void decr(String key, long by);

    /**
     * 添加缓存
     * 不同的缓存实现有不同的含义
     *
     * @param key the key under which this object should be added.
     * @param exp the expiration of this object
     * @param o   the object to store
     * @return
     */
    boolean add(String key, int exp, Object o);

    /**
     * 设置缓存
     *
     * @param key the key under which this object should be added.
     * @param exp the expiration of this object
     * @param o   the object to store
     * @return
     */
    boolean set(String key, int exp, Object o);

    /**
     * 设置缓存
     * 采取默认过期时间
     *
     * @param key the key under which this object should be added.
     * @param o   the object to store
     * @return result
     */
    boolean set(String key, Object o);

    /**
     * 删除缓存key
     *
     * @param key the key to delete
     * @return result
     */
    boolean delete(String key);

}
