package com.kuaidizs.jxc.common.diamond;

public enum SegmentCodeEnum {

    EMS("EMS-RECEIVER-PAY", "EMS-到付"),

    SVC_F("SVC-FRESH", "生鲜件"),

    SVC_S("SVC-STAR", "星联"),

    SVC_I("SVC-INTERNATIONAL", "海外直送");


    SegmentCodeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    private String code;

    private String name;


    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

    public static String getNameByCode(String code) {
        for(SegmentCodeEnum segmentCodeEnum : SegmentCodeEnum.values()) {
            if(segmentCodeEnum.getCode().equals(code)) {
                return segmentCodeEnum.getName();
            }
        }
        return code;
    }
}
