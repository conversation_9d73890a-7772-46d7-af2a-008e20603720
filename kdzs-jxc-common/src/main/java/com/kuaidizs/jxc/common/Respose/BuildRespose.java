package com.kuaidizs.jxc.common.Respose;

public class BuildRespose {

    /**
     * 响应状态码
     */
    private int result = 100;

    /**
     * 对result的状态文字描述
     */
    private String message;

    private String apiName;

    /**
     * 实际传送的对象
     */
    private Object data;

    private boolean success = true;

    public BuildRespose(){

    }


    public int getResult() {
        return result;
    }

    public void setResult(int result) {
        this.result = result;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getApiName() {
        return apiName;
    }

    public void setApiName(String apiName) {
        this.apiName = apiName;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }
}
