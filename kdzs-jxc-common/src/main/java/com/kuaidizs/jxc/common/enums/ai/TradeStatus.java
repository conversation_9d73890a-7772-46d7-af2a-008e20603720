package com.kuaidizs.jxc.common.enums.ai;

import lombok.Getter;

@Getter
public enum TradeStatus {
    /**
     * 未发货
     */
    UN_DELIVERED(0, "未发货"),
    /**
     * 已发货
     */
    DELIVERED(1, "已发货"),
    /**
     * 发货失败
     */
    DELIVER_FAIL(2, "发货失败"),
    /**
     * 已添加至预发货
     */
    ADDED_TO_YFH(3, "已添加至预发货"),
    ;

    public static TradeStatus getByCode(Integer code) {
        for (TradeStatus value : TradeStatus.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    TradeStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;
    private String desc;
}
