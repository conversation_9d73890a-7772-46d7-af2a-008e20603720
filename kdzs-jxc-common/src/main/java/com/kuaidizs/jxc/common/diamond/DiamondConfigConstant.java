package com.kuaidizs.jxc.common.diamond;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kuaidizs.jxc.domain.rateLimit.RateLimitInfo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.kuaidizs.jxc.common.diamond.CallMode.WANGDIAN;

/**
 * Description:
 * User:<PERSON><PERSON> (xuejiah<PERSON>@raycloud.com)
 * Date: 2017/5/15
 * Time: 14:09
 * Version: 1.0
 **/
public class DiamondConfigConstant {

    /**
     * 所有域名通用的groupId
     */
    public final static String SwitchGroupId = "kdzs-web";
    /**
     * 可达网点菜鸟切换开关dataId
     */
    public final static String SwitchReacheDataId = "reache";

    /**
     * 接口限流dataId
     */
    public final static String RateLimitDataId = "ratelimit";

    /**
     * 线下网点所有域名通用的groupId
     */
    public final static String GroupId = "kdzs-web-express";
    /**
     * 自定义快递可达服务id
     */
    public final static String expressArriveDataId = "express_arrive_id";
    /**
     * 用户自定义可达服务 多平台 1、可用 0、不可用
     */
    public static String EXPRESS_ARRIVE;

    /**
     * 预留的开放功能较多的配置
     */
    public static final String GLOBAL_LIMIT_DATAID_GRAY = "globalLimitWithDomainGray";
    /**
     * 全局限流配置（目前应用 双11）dataId
     */
    public final static String GLOBAL_LIMIT_DATAID = "globalLimitWithDomain11";

    public static final String DELIVERY_LIMIT = "deliveryLimit";
    /**
     * 全局限流配置（目前应用 双11）dataId （测试环境）
     */
    public final static String GLOBAL_LIMIT_DATAID_DEV = "globalLimitWithDomainDev";
    /**
     * 全局底单、打印标记降级策略（目前应用 双11）dataId（正式环境）
     */
    public final static String GLOBAL_LIMIT_BG_DG = "globalLimitBgDg";
    /**
     * 单号分享名单设置
     */
    public final static String WAYBILL_SHARE_WHITE_LIST = "waybill_share_white_list";
    /**
     * 进销存plus黑名单
     */
    public final static String ERP_PLUS_BLACK_LIST = "erp_plus_black_list";
    /**
     * 大头笔切换开关dataId
     */
    public final static String SwitchBigMarkDataId = "bigMark";

    /**
     * 顺丰临时发货开关dataId
     */
    public final static String SwitchSfSendCodeDataId = "sf_send_code";

    /**
     * 旺旺主账号引流开关dataId
     */
    public final static String WangwangDataId = "wangwang";

    /**
     * ONS消息开关dataId
     */
    public final static String ONS_SWITCH_DATAID = "onsSwitch";

    /**
     * 顺丰网点是否可以使用开关dataId
     */
    public final static String SF_USE_SWITCH = "sf_use_switch";

    /**
     * 申通可达,默认网点
     */
    public static CallMode REACHEABLE_STO = WANGDIAN;
    /**
     * 汇通可达,默认网点
     */
    public static CallMode REACHEABLE_HTKY = WANGDIAN;
    /**
     * 韵达可达,默认网点
     */
    public static CallMode REACHEABLE_YUNDA = WANGDIAN;
    /**
     * 网点可达,默认网点
     */
    public static CallMode REACHEABLE_GTO = WANGDIAN;
    /**
     * 天天可达,默认网点
     */
    public static CallMode REACHEABLE_TTKDEX = WANGDIAN;
    /**
     * 全峰可达,默认网点
     */
    public static CallMode REACHEABLE_QFKD = WANGDIAN;
    /**
     * 顺丰可达,默认网点
     */
    public static CallMode REACHEABLE_SF = WANGDIAN;
    /**
     * 快捷可达,默认网点
     */
    public static CallMode REACHEABLE_FAST = WANGDIAN;

    /**
     * 菜鸟可达接口 是否可用  1 可用  0 禁用
     */
    public static int REACH_CAINIAO = 1;

    /**
     * 菜鸟可达新接口 是否可用  1 可用  0 禁用
     */
    public static int USE_REACHE_NEW = 1;

    /**
     * 顺丰模板自由下线 是否下线  1 开启  0 禁用
     */
    public static int USE_SF_ZY = 1;
    /**
     * 顺丰模板下线 是否可用  1 开启  0 禁用
     */
    public static int USE_SF_OLD_FM = 1;
    /**
     * 大头笔 中通,默认网点(即原逻辑)
     */
    public static CallMode BIGMARK_ZTO = WANGDIAN;
    /**
     * 大头笔 汇通,默认网点(即原逻辑)
     */
    public static CallMode BIGMARK_HTKY = WANGDIAN;
    /**
     * 大头笔 圆通,默认网点(即原逻辑)
     */
    public static CallMode BIGMARK_YTO = WANGDIAN;
    /**
     * 大头笔 韵达,默认网点(即原逻辑)
     */
    public static CallMode BIGMARK_YUNDA = WANGDIAN;
    /**
     * 大头笔 国通,默认网点(即原逻辑)
     */
    public static CallMode BIGMARK_GTO = WANGDIAN;
    /**
     * 大头笔 天天,默认网点(即原逻辑)
     */
    public static CallMode BIGMARK_TTKDEX = WANGDIAN;
    /**
     * 大头笔 全峰,默认网点(即原逻辑)
     */
    public static CallMode BIGMARK_QFKD = WANGDIAN;
    /**
     * 大头笔 顺丰,默认网点(即原逻辑)
     */
    public static CallMode BIGMARK_SF = WANGDIAN;

    public static CallMode BIGMARK_SUER = WANGDIAN;


    /**
     * 菜鸟大头笔 是否可用  1 可用  0 禁用
     */
    public static int BIGMARK_CAINIAO = 1;

    /**
     * 顺丰临时发货
     */
    public static boolean SF_SEND_CODE = false;

    /**
     * 旺旺主账号导流
     */
    public static boolean WANGWANG_MAIN_ACCOUNT = false;

    /**
     * 接口限流总开关,默认关闭
     */
    public static boolean RATE_LIMIT_STATUS = false;

    /**
     * 顺丰快递是否可以使用
     */
    public static boolean SF_USE_CODE = false;

    public static Map<String, RateLimitInfo> rateLimitMap = new HashMap<String, RateLimitInfo>();
    /**
     * 全局限流配置（目前应用 双11）
     */
    public static JSONObject GLOBAL_LIMIT_CONFIG = new JSONObject();
    /**
     * 全局底单、打印标记降级配置（目前应用 双11）
     */
    public static JSONObject GLOBAL_LIMIT_BG_DG_CONFIG = new JSONObject();
    /**
     * 单号分享专业版白名单
     */
    public static String GLOBAL_LIMIT_WAYBILL_SHARE = "";

    /**
     * 进销存plus黑名单
     */
    public static List<Long> ERP_PLUS_BLACK_LIST_SHARE = new ArrayList<>();

    public static Map<String, List<CpDeliveryLimit>> cpDeliveryLimitMap = new HashMap<>();

    public static String RateLimitGroupId;

    /**
     * ONS 开关 配置（目前应用 双11）o
     */
    public static JSONObject ONS_SWITCH_CONFIG = new JSONObject();

    public static final String IS_SYNC_TRADE_COUNT_LIMIT_DATA_ID = "syncTradeCountLimit";

    public static final String IS_SYNC_TRADE_COUNT_LIMIT_KEY = "is_sync_trade_count_limit";

    public static boolean IS_SYNC_TRADE_COUNT_LIMIT = false;

    /**
     * 快递对账配置 db  cache
     */
    public static final String ExPRESS_ACCOUNT = "expressAccount";

    /**
     * 批量导入发货 db  cache
     */
    public static final String BATCH_IMPORT_DELIVERY = "batchImportDelivery";

    /**
     * 批打降级校验开关
     */
    public static final String DEGRADED_SEARCH_CONFIG = "degradedSearch";

    /**
     * DB版本引导切换时间
     */
    public static final String DB_SYNC_CONFIG_ID = "dbSyncConfig";

    public static String DB_CONFIG_SWITCH_TIME = "30";

    public static Integer QUERY_NUMBER = 100000;

    /**
     * 自定义快递可达判断降级开关
     */
    public static final String EXPRESS_ARRIVE_CONFIG = "expressArrive";

    /**
     * 是否展示年度报告 0不展示 1 展示
     */
    public static final String USER_SUMMMARY_REPORT = "userSummaryReport";

    /**
     * 指定域名切换
     */
    public static final String APPOINT_DOMAIN_SWITCH = "appoint_domain_switch";

    public static JSONObject APPOINT_DOMAIN_CONFIG = new JSONObject();

    /**
     * 打印开关配置
     */
    public final static String PRINT_SWITCH = "print_switch";

    /**
     * 是否批量调用菜鸟batchRequest开关
     */
    public static Integer IS_BATCH_GET_SWITCH = 1;

    /**打印开关配置常数**/
    /**
     * 模板下线配置（生产）
     */
    public final static String OFFLINE_TEMPLATE_OF_SECOND = "offline_template_of_second";

    /**
     * 模板下线配置（开发）
     */
    public final static String OFFLINE_TEMPLATE_OF_SECOND_DEV = "offline_template_of_second_dev";

    /**
     * 快递单类型下线配置
     */
    public static JSONArray OFFLINE_TEMPLATE_OF_SECOND_KDDTYPE_CONFIG = new JSONArray();

    /**
     * 快递公司下线配置
     */
    public static JSONArray OFFLINE_TEMPLATE_OF_SECOND_EXCODE_CONFIG = new JSONArray();

    /**
     * 启用网点电子面单用户白名单配置
     */
    public static JSONArray OFFLINE_TEMPLATE_OF_ENABLE_WD_CONFIG = new JSONArray();

    /**
     * 退款调用api或者rds开关dataId,线上环境
     */
    public final static String REFUND_API_OR_RDS_SWITCH_DATAID = "refund-api-rds";

    /**
     * 退款调用api或者rds开关dataId,开发环境
     */
    public final static String REFUND_API_OR_RDS_SWITCH_DATAID_DEV = "refund-api-rds-dev";

    /**
     * 退款调用接口走API或走RDS，true:rds,false:api
     */
    public static JSONObject REFUND_API_OR_RDS_SWITCH_CONFIG = new JSONObject();

    /**
     * 退款调用接口走API或走RDS 开关 1：rds 0：api
     */
    public final static String REFUND_API_OR_RDS_SWITCH = "apiOrRdsSwitch";

    /**
     * 退款调用接口RDS
     */
    public static final Integer REFUND_RDS_SWITCH = 1;


}
