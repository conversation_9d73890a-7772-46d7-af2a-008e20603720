package com.kuaidizs.jxc.common.Respose;

import com.kuaidizs.jxc.domain.rds.RdsHitResult;
import com.taobao.api.domain.Trade;

/**
 * 批量查询接口，判断接口是否正常
 */
public class BatchTradeResponse {

    private Long tid;

    /**
     * 接口是否调用成功
     */
    private Boolean isSuccess;

    /**
     * 如果失败，失败的错误信息
     */
    private String errorMsg;

    /**
     * 如果成功，存入订单引用
     */
    private Trade trade;


    public Long getTid() {
        return tid;
    }

    public void setTid(Long tid) {
        this.tid = tid;
    }

    public Boolean getSuccess() {
        return isSuccess;
    }

    public void setSuccess(Boolean success) {
        isSuccess = success;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public Trade getTrade() {
        return trade;
    }

    public void setTrade(Trade trade) {
        this.trade = trade;
    }

}
