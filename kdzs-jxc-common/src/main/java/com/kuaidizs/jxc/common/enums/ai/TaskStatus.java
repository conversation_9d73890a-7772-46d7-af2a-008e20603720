package com.kuaidizs.jxc.common.enums.ai;

import lombok.Getter;

@Getter
public enum TaskStatus {
    //    INIT(0, "待执行"),
    /**
     * 无任务（非数据库状态，仅供返回前端使用）
     */
    NONE(0, "无任务"),
    /**
     * 执行中
     */
    RUNNING(2, "执行中"),
    /**
     * 由任务创建者正常关闭
     */
    CLOSED(3, "由任务创建者正常关闭"),
    /**
     * 由非任务创建者正常关闭
     */
    CLOSE_BY_OHTER_USER(4, "由非任务创建者正常关闭"),
    /**
     * 异常关闭
     */
    EXCEPTION(5, "异常关闭"),
    ;

    public static TaskStatus getByCode(Integer code) {
        for (TaskStatus value : TaskStatus.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    TaskStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;
    private String desc;
}
