package com.kuaidizs.jxc.common.cache.memcache;

import com.kuaidizs.jxc.common.cache.ICache;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 通用的缓存管理
 *
 * <AUTHOR>
 * @date 2018/5/28
 */
public class MemcacheCacheManager implements CacheManager, InitializingBean {

    /**
     * 过期时间分隔符
     */
    private static final char SEPARATE_CHAR = '#';

    /**
     * 多个缓存放置
     */
    private ConcurrentMap<String, ICache> cacheMap = new ConcurrentHashMap<>(5);

    /**
     * 缓存名称集合
     */
    private volatile Set<String> cacheNames = Collections.emptySet();

    private List<ICache> caches;

    public MemcacheCacheManager(List<ICache> caches) {
        if (CollectionUtils.isEmpty(caches)) {
            throw new IllegalArgumentException("create manager error,cache not null!");
        }
        this.caches = caches;
    }

    @Override
    public Collection<String> getCacheNames() {
        return this.cacheNames;
    }

    @Override
    public void afterPropertiesSet() {
        initializeCaches();
    }

    /**
     * 初始化缓存信息
     */
    public void initializeCaches() {
        this.cacheNames = Collections.emptySet();
        this.cacheMap.clear();
        Set<String> cacheNames = new HashSet<>(caches.size());
        for (ICache cache : caches) {
            cacheMap.put(cache.getName(), cache);
            cacheNames.add(cache.getName());
        }
        this.cacheNames = Collections.unmodifiableSet(cacheNames);
    }

    @Override
    public Cache getCache(String name) {
        int separateIndex = name.lastIndexOf(SEPARATE_CHAR);
        String cacheName;
        Integer expire = null;
        try {
            if (separateIndex == -1) {
                cacheName = name;
            } else {
                cacheName = name.substring(0, separateIndex);
                String expireTime = name.substring(separateIndex + 1).trim();
                ExpressionParser parser = new SpelExpressionParser();
                Expression expression = parser.parseExpression(expireTime);
                expire = expression.getValue(Integer.class);
            }
        } catch (Exception e) {
            throw new RuntimeException("缓存标签解析异常，异常信息:" + e.getMessage());
        }
        ICache cache = cacheMap.get(cacheName);
        AdapterCache adapterCache = new AdapterCache(cache);
        adapterCache.setExpire(expire);
        return adapterCache;
    }

}
