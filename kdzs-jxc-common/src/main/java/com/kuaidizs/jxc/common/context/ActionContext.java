package com.kuaidizs.jxc.common.context; /**
 * raycloud.com Inc.
 * Copyright (c) 2013-2020 All Rights Reserved.
 */


import lombok.Data;
import lombok.experimental.Accessors;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Data
@Accessors(chain = true)
public class ActionContext {

    private UserInfo userInfo;

    private HttpServletRequest httpServletRequest;

    private HttpServletResponse httpServletResponse;


    @Data
    @Accessors(chain = true)
    public static class UserInfo {
        /**
         * 卖家淘宝id
         */
        private Long taobaoId;

        /**
         * 卖家淘宝昵称
         */
        private String userNick;

        /**
         * 子账户名
         */
        private String subUserNick;

        /**
         * 子账户淘宝Id
         */
        private Long subTaobaoId;

        /**
         * 用户等级
         */
        private Integer userLevel;

        /**
         * 店铺类型 B 天猫  C 普通店铺
         */
        private String shopType;

        /**
         * 当前登录用户的授权token
         */
        private String accessToken;

        /**
         * 商家应用的appKey
         */
        private String appKey;

        /**
         * 当前登录用户的openId
         */
        private String openId;

        /**
         * 当前小程序的id
         */
        private String miniAppId;

        /**
         * 客户端ip
         */
        private String sourceIp;

        /**
         * {@code loginEntrance} 登录入口
         */
        private String loginEntrance;

        /**
         * {@code loginDevice} 登录设备
         */
        private String loginDevice;

        /**
         * {@code xcxVersion} 小程序版本号
         */
        private String xcxVersion;

        /**
         * {@code qnVersion} 千牛版本号
         */
        private String qnVersion;

        /**
         * {@code systemType} 系统类型 ios,android
         */
        private String systemType;

        /**
         * {@code systemVersion} 系统版本
         */
        private String systemVersion;

        /**
         * 孔明锁
         */
        private String xUmt;

        /**
         * 客户端类型 mobile、pc、pcww
         */
        private String clientType;
    }

}
