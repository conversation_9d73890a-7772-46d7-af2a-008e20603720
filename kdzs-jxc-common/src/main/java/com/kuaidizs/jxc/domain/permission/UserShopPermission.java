package com.kuaidizs.jxc.domain.permission;

import java.util.*;
import java.io.Serializable;

/**
 *
 * <AUTHOR> @date    2016-08-26
 */
public class UserShopPermission implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 自增列ID
     */
    private Long id;
	/**
     * 用户关联店铺主账号ID
     */
    private Long taobaoId;
	/**
     * 店铺ID（店铺主账号ID)
     */
    private Long shopId;
	/**
     * 添加时间
     */
    private Date created;
	/**
     * 更新时间
     */
    private Date modified;
	/**
     * 逻辑删除
     */
    private Integer enableStatus;

	
   /**
    * @return id 自增列ID
    */
    public Long getId() {
       return id;
    }
   /**
    * @param id 自增列ID
    */
    public void setId(Long id) {
       this.id = id;
    }
	
   /**
    * @return taobaoId 用户关联店铺主账号ID
    */
    public Long getTaobaoId() {
       return taobaoId;
    }
   /**
    * @param taobaoId 用户关联店铺主账号ID
    */
    public void setTaobaoId(Long taobaoId) {
       this.taobaoId = taobaoId;
    }
	
   /**
    * @return shopId 店铺ID（店铺主账号ID)
    */
    public Long getShopId() {
       return shopId;
    }
   /**
    * @param shopId 店铺ID（店铺主账号ID)
    */
    public void setShopId(Long shopId) {
       this.shopId = shopId;
    }
	
   /**
    * @return created 添加时间
    */
    public Date getCreated() {
       return created;
    }
   /**
    * @param created 添加时间
    */
    public void setCreated(Date created) {
       this.created = created;
    }
	
   /**
    * @return modified 更新时间
    */
    public Date getModified() {
       return modified;
    }
   /**
    * @param modified 更新时间
    */
    public void setModified(Date modified) {
       this.modified = modified;
    }
	
   /**
    * @return enableStatus 逻辑删除
    */
    public Integer getEnableStatus() {
       return enableStatus;
    }
   /**
    * @param enableStatus 逻辑删除
    */
    public void setEnableStatus(Integer enableStatus) {
       this.enableStatus = enableStatus;
    }

}