package com.kuaidizs.jxc.domain.print.template;

import com.alibaba.fastjson.annotation.JSONField;
import com.kuaidizs.jxc.domain.print.ModeListshow;
import com.kuaidizs.jxc.domain.print.ModeSet;

import java.io.Serializable;
import java.util.List;

/**
 * Created by Administrator on 2016/7/25.
 */
public class GlobalSettingResponseVO implements Serializable {

    private static final long serialVersionUID = 3085084085701265893L;

    //全局设置
    @JSONField(name="ModeSet")
    private ModeSet modeSet;
    //获取模版列表
    @JSONField(name="ModeListShows")
    private List<ModeListshow> modelListshowsList;
    //默认模板ID
    @JSONField(name="ModeListShowId")
    private Integer modeListshowId;
    //获取系统字体
    @JSONField(name="SystemFonts")
    private List<String> systemFontList;

    public ModeSet getModeSet() {
        return modeSet;
    }

    public void setModeSet(ModeSet modeSet) {
        this.modeSet = modeSet;
    }

    public List<ModeListshow> getModelListshowsList() {
        return modelListshowsList;
    }

    public void setModelListshowsList(List<ModeListshow> modelListshowsList) {
        this.modelListshowsList = modelListshowsList;
    }

    public Integer getModeListshowId() {
        return modeListshowId;
    }

    public void setModeListshowId(Integer modeListshowId) {
        this.modeListshowId = modeListshowId;
    }

    public List<String> getSystemFontList() {
        return systemFontList;
    }

    public void setSystemFontList(List<String> systemFontList) {
        this.systemFontList = systemFontList;
    }
}
