package com.kuaidizs.jxc.domain.item;

import com.kuaidizs.jxc.domain.User;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022-04-24
 * 同步商品结果
 */
@Data
public class SyncItemResult implements Serializable {

    private static final long serialVersionUID = -8466926345987817777L;

    private Long id;

    /**
     * 淘宝id
     */
    private Long taobaoId;

    /**
     * 淘宝id
     */
    private String taobaoNick;

    /**
     * 上次同步结果
     */
    private String syncResult;

    /**
     * 上次同步失败原因
     */
    private String syncErrorMessage;

    /**
     * 上次同步商品时间
     */
    private Date syncDate;

    private Date created;

    private Date modified;

    public SyncItemResult(){
    }

    public SyncItemResult(User user){
        this.taobaoId = user.getTaobaoId();
        this.taobaoNick = user.getTaobaoNick();
    }

}
