package com.kuaidizs.jxc.domain.ai;

import com.kuaidizs.jxc.common.enums.ai.AiSortTradeCategoryEnum;
import com.kuaidizs.jxc.common.enums.ai.TaskStatus;
import java.util.Date;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

@Data
public class AiPrintRecordStatistics {
    /**
     * 已打印数量
     */
    private Integer printedNum;
    /**
     * 打印失败数量
     */
    private Integer printFailNum;
    /**
     * 已发货数量
     */
    private Integer deliveredNum;
    /**
     * 发货失败数量
     */
    private Integer deliverFailNum;
    /**
     * 已打印未发货数量
     */
    private Integer printedAndUnDeliveredNum;
    /**
     * 已添加到预发货数量
     */
    private Integer addedToYFHNum;
    /**
     * 任务开始时间
     */
    private Date taskTimeStart;
    /**
     * 任务结束时间
     */
    private Date taskTimeEnd;
    /**
     * 任务状态
     */
    private TaskStatus taskStatus;
    /**
     * 订单分类
     */
    private AiSortTradeCategoryEnum category;
    /**
     * 订单所属分类中文描述
     */
    private String categoryDesc;

    private String categoryCode;

    public void initFieldsForResult() {
        if (StringUtils.isNotBlank(this.categoryCode)) {
            this.category = AiSortTradeCategoryEnum.getByName(this.categoryCode);
        }
    }

    public static AiPrintRecordStatistics emptyObj() {
        AiPrintRecordStatistics statistics = new AiPrintRecordStatistics();
        statistics.setPrintedNum(0);
        statistics.setPrintFailNum(0);
        statistics.setDeliveredNum(0);
        statistics.setDeliverFailNum(0);
        statistics.setPrintedAndUnDeliveredNum(0);
        statistics.setTaskStatus(TaskStatus.NONE);
        return statistics;
    }
}
