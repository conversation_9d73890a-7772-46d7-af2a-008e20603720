package com.kuaidizs.jxc.domain.data;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.Date;

@Setter
@Getter
public class UserOperateRule implements Serializable {

    private Long id; // 主键id
    private Long taobaoId; // 用户id
    private String queryRule; // 查询条件组合规则,json字符串
    private String queryRuleDesc; // 查询条件组合说明,例如"时间范围-时间类型-订单状态-店铺名称"
    private String queryRuleMd5; // 用户查询组合md5值
    private String sortRule; // 排序组合，json字符串
    private String sortRuleMd5; // 排序组合md5值
    private Long kdTemplateId; // 快递模板id
    private String kdTemplateName; // 快递模板名称
    private String checkRule; // 审单规则,json字符串
    private String checkRuleMd5; // 审单规则md5值
    private Long queryTotalCount; // 当前查询条件查出订单总数
    private Date queryStartTime; // 开始查询时间
    private Date queryCompleteTime; // 查询完成时间
    private Date printTime; // 打印时间
    private Integer selectedTradeStatus; // 1:勾选过订单
    private Integer cancelTradeStatus; // 1:取消过勾选订单
    private Long logId; // 用户上报日志表operation_link_log的id
    private Date created; // 记录添加时间
    private Date modified; // 记录修改时间
}