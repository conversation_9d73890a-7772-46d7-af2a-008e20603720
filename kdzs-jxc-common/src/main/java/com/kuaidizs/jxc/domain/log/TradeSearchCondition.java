package com.kuaidizs.jxc.domain.log;

import java.io.Serializable;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;

/**
 * 交易搜索条件实体类
 */
public class TradeSearchCondition implements Serializable {
    private static final long serialVersionUID = 1L;

    private String buyerNick;
    private String buyerOpenUid;
    private Boolean fuzzySearch;
    private Boolean filterByTrade;
    private String goodsTotalNum;
    private String goodsTypeNum;
    private String tradeNum;
    private String tradeWeight;
    private List<Object> goodsRuleList;
    private String printStatus;
    private String refundStatus;
    private String flagValue;
    private String msgMemoValue;
    private String buyerMessage;
    private String sellerMemo;
    private List<ReceiverAddress> receiverAddressList;
    private String abortTime;
    private String abortTimeDay;
    private String exceedTime;
    private String startTime;
    private String endTime;
    private String payment;
    private String type;
    private String status;
    private String market;
    private String stall;
    private String kdName;
    private String addressType;

    /**
     * 收货地址信息
     */
    public static class ReceiverAddress implements Serializable {
        private String state;
        private String city;

        public String getState() {
            return state;
        }

        public void setState(String state) {
            this.state = state;
        }

        public String getCity() {
            return city;
        }

        public void setCity(String city) {
            this.city = city;
        }
    }

    // Getter and Setter methods
    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public String getBuyerOpenUid() {
        return buyerOpenUid;
    }

    public void setBuyerOpenUid(String buyerOpenUid) {
        this.buyerOpenUid = buyerOpenUid;
    }

    public Boolean getFuzzySearch() {
        return fuzzySearch;
    }

    public void setFuzzySearch(Boolean fuzzySearch) {
        this.fuzzySearch = fuzzySearch;
    }

    public Boolean getFilterByTrade() {
        return filterByTrade;
    }

    public void setFilterByTrade(Boolean filterByTrade) {
        this.filterByTrade = filterByTrade;
    }

    public String getGoodsTotalNum() {
        return goodsTotalNum;
    }

    public void setGoodsTotalNum(String goodsTotalNum) {
        this.goodsTotalNum = goodsTotalNum;
    }

    public String getGoodsTypeNum() {
        return goodsTypeNum;
    }

    public void setGoodsTypeNum(String goodsTypeNum) {
        this.goodsTypeNum = goodsTypeNum;
    }

    public String getTradeNum() {
        return tradeNum;
    }

    public void setTradeNum(String tradeNum) {
        this.tradeNum = tradeNum;
    }

    public String getTradeWeight() {
        return tradeWeight;
    }

    public void setTradeWeight(String tradeWeight) {
        this.tradeWeight = tradeWeight;
    }

    public List<Object> getGoodsRuleList() {
        return goodsRuleList;
    }

    public void setGoodsRuleList(List<Object> goodsRuleList) {
        this.goodsRuleList = goodsRuleList;
    }

    public String getPrintStatus() {
        return printStatus;
    }

    public void setPrintStatus(String printStatus) {
        this.printStatus = printStatus;
    }

    public String getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }

    public String getFlagValue() {
        return flagValue;
    }

    public void setFlagValue(String flagValue) {
        this.flagValue = flagValue;
    }

    public String getMsgMemoValue() {
        return msgMemoValue;
    }

    public void setMsgMemoValue(String msgMemoValue) {
        this.msgMemoValue = msgMemoValue;
    }

    public String getBuyerMessage() {
        return buyerMessage;
    }

    public void setBuyerMessage(String buyerMessage) {
        this.buyerMessage = buyerMessage;
    }

    public String getSellerMemo() {
        return sellerMemo;
    }

    public void setSellerMemo(String sellerMemo) {
        this.sellerMemo = sellerMemo;
    }

    public List<ReceiverAddress> getReceiverAddressList() {
        return receiverAddressList;
    }

    public void setReceiverAddressList(List<ReceiverAddress> receiverAddressList) {
        this.receiverAddressList = receiverAddressList;
    }

    public String getAbortTime() {
        return abortTime;
    }

    public void setAbortTime(String abortTime) {
        this.abortTime = abortTime;
    }

    public String getAbortTimeDay() {
        return abortTimeDay;
    }

    public void setAbortTimeDay(String abortTimeDay) {
        this.abortTimeDay = abortTimeDay;
    }

    public String getExceedTime() {
        return exceedTime;
    }

    public void setExceedTime(String exceedTime) {
        this.exceedTime = exceedTime;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getPayment() {
        return payment;
    }

    public void setPayment(String payment) {
        this.payment = payment;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getStall() {
        return stall;
    }

    public void setStall(String stall) {
        this.stall = stall;
    }

    public String getKdName() {
        return kdName;
    }

    public void setKdName(String kdName) {
        this.kdName = kdName;
    }

    public String getAddressType() {
        return addressType;
    }

    public void setAddressType(String addressType) {
        this.addressType = addressType;
    }

    /**
     * 将receiverAddressList转换为addressRule格式
     * @return AddressRule对象
     */
    public TradeQueryCondition.AddressRule convertToAddressRule() {
        if (receiverAddressList == null || receiverAddressList.isEmpty()) {
            return null;
        }

        TradeQueryCondition.AddressRule addressRule = new TradeQueryCondition.AddressRule();
        addressRule.setAddressType(0);
        addressRule.setIncludeType(0);
        
        // 按省份分组
        Map<String, List<String>> stateMap = new HashMap<>();
        for (ReceiverAddress address : receiverAddressList) {
            String state = address.getState();
            String city = address.getCity();
            stateMap.computeIfAbsent(state, k -> new ArrayList<>()).add(city);
        }

        // 构建selectRule
        List<TradeQueryCondition.AddressNode> selectRule = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : stateMap.entrySet()) {
            String state = entry.getKey();
            List<String> cities = entry.getValue();

            TradeQueryCondition.AddressNode stateNode = new TradeQueryCondition.AddressNode();
            stateNode.setName(state.replace("省", "")); // 移除"省"字，以匹配目标格式

            // 创建城市节点列表
            List<TradeQueryCondition.AddressNode> cityNodes = new ArrayList<>();
            for (String city : cities) {
                TradeQueryCondition.AddressNode cityNode = new TradeQueryCondition.AddressNode();
                cityNode.setName(city);
                cityNode.setChildList(new ArrayList<>()); // 设置空的子节点列表
                cityNodes.add(cityNode);
            }
            
            stateNode.setChildList(cityNodes);
            selectRule.add(stateNode);
        }

        addressRule.setSelectRule(selectRule);
        addressRule.setKeywordRule(new ArrayList<>()); // 设置空的关键词规则列表

        return addressRule;
    }
} 