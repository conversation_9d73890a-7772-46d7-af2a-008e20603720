package com.kuaidizs.jxc.domain.download;

import java.io.Serializable;
import java.util.Date;


/**
 * 下载中心下载任务相关实体类
 *
 * <AUTHOR> Longjun
 * Date    2018-05-08
 */
public class DownTask implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * 逻辑删除状态0表示删除 1表示未删除
     */
    private Integer enableStatus;
    /**
     * 淘宝id
     */
    private Long taobaoId;
    /**
     * 淘宝nick
     */
    private String taobaoNick;
    /**
     * 查询条件
     */
    private String exportCondition;
    /**
     * 所属模块 TB  BG  HAND
     */
    private String modules;
    /**
     * 导出订单数
     */
    private Integer num;
    /**
     * 导出结果，0等待导出，1导出中，2导出成功，3导出失败
     */
    private Integer exportResult;
    /**
     * 导出结果,导出失败时存储导出失败原因，导出成功时存储文件OSS地址
     */
    private String exportMsg;
    /**
     * 导出操作人
     */
    private String exportor;
    /**
     * 导出维度 1 订单维护   2商品维度
     */
    private Integer exportType;
    /**
     * 导出字段配置
     */
    private String exportConfig;
    /**
     * 导出耗时
     */
    private Long tooks;
    /**
     * 下载次数
     */
    private Long dowloads;
    /**
     * 逻辑缓存标识 解决每次重启可能导致任务缓存队列缓存中死锁的问题
     */
    private String batchTimes;

    public String getBatchTimes() {
        return batchTimes;
    }

    public void setBatchTimes(String batchTimes) {
        this.batchTimes = batchTimes;
    }

    public Long getTooks() {
        return tooks;
    }

    public void setTooks(Long tooks) {
        this.tooks = tooks;
    }

    public Long getDowloads() {
        return dowloads;
    }

    public void setDowloads(Long dowloads) {
        this.dowloads = dowloads;
    }

    /**
     * @return id 自增id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id 自增id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return enableStatus 逻辑删除状态0表示删除 1表示未删除
     */
    public Integer getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 逻辑删除状态0表示删除 1表示未删除
     */
    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return taobaoId 淘宝id
     */
    public Long getTaobaoId() {
        return taobaoId;
    }

    /**
     * @param taobaoId 淘宝id
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return taobaoNick 淘宝nick
     */
    public String getTaobaoNick() {
        return taobaoNick;
    }

    /**
     * @param taobaoNick 淘宝nick
     */
    public void setTaobaoNick(String taobaoNick) {
        this.taobaoNick = taobaoNick;
    }

    /**
     * @return exportCondition 查询条件
     */
    public String getExportCondition() {
        return exportCondition;
    }

    /**
     * @param exportCondition 查询条件
     */
    public void setExportCondition(String exportCondition) {
        this.exportCondition = exportCondition;
    }

    /**
     * @return modules 所属模块 TB  BG  HAND
     */
    public String getModules() {
        return modules;
    }

    /**
     * @param modules 所属模块 TB  BG  HAND
     */
    public void setModules(String modules) {
        this.modules = modules;
    }

    /**
     * @return num 导出订单数
     */
    public Integer getNum() {
        return num;
    }

    /**
     * @param num 导出订单数
     */
    public void setNum(Integer num) {
        this.num = num;
    }

    /**
     * @return exportResult 导出结果，0等待导出，1导出中，2导出成功，3导出失败
     */
    public Integer getExportResult() {
        return exportResult;
    }

    /**
     * @param exportResult 导出结果，0等待导出，1导出中，2导出成功，3导出失败
     */
    public void setExportResult(Integer exportResult) {
        this.exportResult = exportResult;
    }

    /**
     * @return exportMsg 导出结果,导出失败时存储导出失败原因，导出成功时存储文件OSS地址
     */
    public String getExportMsg() {
        return exportMsg;
    }

    /**
     * @param exportMsg 导出结果,导出失败时存储导出失败原因，导出成功时存储文件OSS地址
     */
    public void setExportMsg(String exportMsg) {
        this.exportMsg = exportMsg;
    }

    /**
     * @return exportor 导出操作人
     */
    public String getExportor() {
        return exportor;
    }

    /**
     * @param exportor 导出操作人
     */
    public void setExportor(String exportor) {
        this.exportor = exportor;
    }

    /**
     * @return exportType 导出维度 1 订单维护   2商品维度
     */
    public Integer getExportType() {
        return exportType;
    }

    /**
     * @param exportType 导出维度 1 订单维护   2商品维度
     */
    public void setExportType(Integer exportType) {
        this.exportType = exportType;
    }

    /**
     * @return exportConfig 导出字段配置
     */
    public String getExportConfig() {
        return exportConfig;
    }

    /**
     * @param exportConfig 导出字段配置
     */
    public void setExportConfig(String exportConfig) {
        this.exportConfig = exportConfig;
    }

}