package com.kuaidizs.jxc.domain.download.dataCenter;


import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.metadata.Sheet;
import com.alibaba.excel.metadata.Table;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.kuaidizs.jxc.common.util.DateUtil;
import com.kuaidizs.jxc.domain.share.WaybillUseLog;
import com.kuaidizs.jxc.domain.trade.TradeExportConfig;
import com.raycloud.bizlogger.Logger;
import org.apache.commons.collections.CollectionUtils;

import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;

/**
 * @auther xudaomeng
 * @since 2020-07-09 15:33
 */
public class ExcelFile<T> {

    private Integer fileSize = 0;

    private String fileName;

    private ExcelWriter writer;

    private Sheet sheet;

    private Class clazz;

    private ExcelFile(String fileName,Class clazz) throws FileNotFoundException {
        this.fileName = fileName;
        OutputStream out = new FileOutputStream(fileName);
        this.writer = new ExcelWriter(out, ExcelTypeEnum.XLSX, true);
        sheet = new Sheet(1, 0);
        sheet.setSheetName("sheet1");
        this.clazz = clazz;
        writeHeader();
    }

    private void writeHeader() {
        List<TradeExportConfig.ExportConfig> tradeExportConfigList = TradeExportConfig.ExportConfig.getExportConfig(clazz, 1);
        Collections.sort(tradeExportConfigList);
        Table table = new Table(1);

        List<List<String>> headList = new ArrayList<>();
        for (TradeExportConfig.ExportConfig exportConfig : tradeExportConfigList) {
            headList.add(Arrays.asList(exportConfig.getName()));
        }

        List<List<String>> data = new ArrayList<>();
        table.setHead(headList);
        writer.write0(data, sheet, table);
    }

    public void writeData(List<T> waybillUseLogList, List<TradeExportConfig.ExportConfig> exportConfigList) throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {

        if (CollectionUtils.isEmpty(waybillUseLogList)) {
            return;
        }

        fileSize += waybillUseLogList.size();

        List<List<String>> data = new ArrayList<>();
        //根据order 排序
        Collections.sort(exportConfigList);

        for (int i = 0; i < waybillUseLogList.size(); i++) {

            T waybillUseLog = waybillUseLogList.get(i);

            List<String> row = new ArrayList();
            for (TradeExportConfig.ExportConfig exportConfig : exportConfigList) {
                // 获取属性的名字
                Field field = TradeExportConfig.ExportConfig.getDeclaredField(waybillUseLog, exportConfig.getKey());
                if (field != null) {
                    Method method = waybillUseLog.getClass().getMethod("get" + field.getName().substring(0, 1).toUpperCase() + field.getName().substring(1));
                    Object value = method.invoke(waybillUseLog);
                    if (value == null) {
                        row.add("");
                    } else if (value instanceof String) {
                        row.add((String) value);
                    } else if (value instanceof Date) {
                        row.add(DateUtil.convertToStr((Date) value));
                    } else if (value instanceof BigDecimal) {
                        BigDecimal bigDecimal = (BigDecimal) value;
                        row.add(bigDecimal.toString());
                    } else if (value instanceof Integer) {
                        row.add(value.toString());
                    }
                }
            }
            data.add(row);
        }
        writer.write0(data, sheet);
    }


    public static ExcelFile createNewFile(String fileName, Class clazz) throws FileNotFoundException {
        return new ExcelFile(fileName, clazz);
    }

    public void closeFile() {
        writer.finish();
    }

    public Integer getFileSize() {
        return fileSize;
    }
}
