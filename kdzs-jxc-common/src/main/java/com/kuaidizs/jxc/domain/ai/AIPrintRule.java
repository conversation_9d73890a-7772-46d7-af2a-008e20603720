package com.kuaidizs.jxc.domain.ai;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class AIPrintRule implements Serializable {

    private Long id;

    private Long taobaoId;

    /**
     * 打印规则排序
     */
    private String printRule;

    /**
     * 打印规则设置标志：
     * 0：未设置
     * 1：用户重新设置过
     */
    private int setFlag;

    /**
     * 相似商品开关 - 用户级别设置
     * 0：不是
     * 1：是
     */
    private int isSimilarItemUser;

    /**
     * 相似商品白名单：0：不是；1：是
     */
    private int isSimilarItem;

    private Date created;

    private Date modified;


}
