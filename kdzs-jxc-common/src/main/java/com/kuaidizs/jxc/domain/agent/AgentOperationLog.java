package com.kuaidizs.jxc.domain.agent;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 分销代理操作日志
 *
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AgentOperationLog.java
 * @createTime 2022年12月12日 14:42:00
 */
@Data
public class AgentOperationLog implements Serializable {
	/**
	 * 记录id
	 */
	private Long id;

	/**
	 * 日志所属用户
	 */
	private String logUserId;

	/**
	 * 创建时间
	 */
	private Date created;

	/**
	 * 操作人id
	 */
	private String operationUserId;

	/**
	 * 操作人名
	 */
	private String operationUserName;

	/**
	 * 商家id
	 */
	private String mallUserId;

	/**
	 * 商家名
	 */
	private String mallUserName;

	/**
	 * 厂家id
	 */
	private String factoryUserId;

	/**
	 * 厂家名称
	 */
	private String factoryUserName;

	/**
	 * 动作类型
	 */
	private String actionType;

	/**
	 * 操作类型
	 */
	private String operationType;

	/**
	 * 操作内容
	 */
	private String operationContent;

	/**
	 * 用户ip
	 */
	private String userIp;

	/**
	 * 操作备注/操作原因
	 */
	private String remark;

}
