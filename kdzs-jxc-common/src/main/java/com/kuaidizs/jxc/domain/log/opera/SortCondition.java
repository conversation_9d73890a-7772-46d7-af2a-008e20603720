package com.kuaidizs.jxc.domain.log.opera;

import lombok.Data;

/**
 * 排序条件DTO
 */
@Data
public class SortCondition {
    /**
     * 排序纬度
     */
    private Integer sortLatitude;

    /**
     * 排序模式
     */
    private SortMode sortMode;

    @Data
    public static class SortMode {
        /**
         * 类型
         */
        private Integer type;

        /**
         * 赠品
         */
        private String gifts;

        /**
         * 市场档口排序
         */
        private Integer marketStallSort;

        /**
         * 商品排序
         */
        private Integer itemSort;

        /**
         * SKU排序
         */
        private Integer skuSort;

        /**
         * 属性排序
         */
        private Integer attributeSort;
    }
}