package com.kuaidizs.jxc.domain.download;

import com.alibaba.fastjson.JSON;
import com.kuaidizs.jxc.domain.trade.TradeExportConfig.ExportConfig;
import com.kuaidizs.jxc.domain.trade.TradeExportConfig.ExportField;
import org.apache.commons.beanutils.BeanUtils;

import java.io.Serializable;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * ItemExportBean.
 *
 * <AUTHOR>
 */
public class ItemExportBean extends TradeExportBean implements Serializable {

    /**
     * 商家编码
     */
    @ExportField(name = "商家编码", order = 11)
    private String outerId;

    /**
     * 宝贝简称
     */
    @ExportField(name = "宝贝简称", order = 12)
    private String shortTitle;

    /**
     * 规格名称
     */
    @ExportField(name = "规格名称", value = 1, order = 13)
    private String props;

    /**
     * 数量
     */
    @ExportField(name = "数量", value = 1, order = 14)
    private String nums;

    /**
     * 重量
     */
    @ExportField(name = "重量", order = 15)
    private String weight;
    /**
     * 规格别名
     */
    @ExportField(name = "规格别名", order = 24)
    private String skuAlias;
    /**
     * 规格商家编码
     */
    @ExportField(name = "规格商家编码", order = 25)
    private String outerSkuId;

    private String numId;
    private String skuId;

    /**
     * 成本价
     */
    @ExportField(name = "成本价", order = 26)
    private String cost;

    /**
     * 市场
     */
    @ExportField(name = "市场", order = 27)
    private String market;

    /**
     * 档口
     */
    @ExportField(name = "档口", order = 28)
    private String stall;


    public String getOuterSkuId() {
        return outerSkuId;
    }

    public void setOuterSkuId(String outerSkuId) {
        this.outerSkuId = outerSkuId;
    }

    public String getSkuAlias() {
        return skuAlias;
    }

    public void setSkuAlias(String skuAlias) {
        this.skuAlias = skuAlias;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    public String getShortTitle() {
        return shortTitle;
    }

    public void setShortTitle(String shortTitle) {
        this.shortTitle = shortTitle;
    }

    public String getProps() {
        return props;
    }

    public void setProps(String props) {
        this.props = props;
    }

    public String getNums() {
        return nums;
    }

    public void setNums(String nums) {
        this.nums = nums;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getNumId() {
        return numId;
    }

    public void setNumId(String numId) {
        this.numId = numId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getCost() {
        return cost;
    }

    public void setCost(String cost) {
        this.cost = cost;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getStall() {
        return stall;
    }

    public void setStall(String stall) {
        this.stall = stall;
    }

    public static void main(String[] args) throws IllegalAccessException, InvocationTargetException, NoSuchMethodException {
        //System.out.println(JSON.toJSONString(new TradeExportBean(), SerializerFeature.WriteMapNullValue));
        Map<String, String> map = BeanUtils.describe(new ItemExportBean());

        List<ExportConfig> exportConfigList = new ArrayList<>();
        int order = 0;
        for (Map.Entry<String, String> entry : map.entrySet()) {
            if ("class,totalFees,numId,skuId".contains(entry.getKey())) {
                continue;
            }
            ExportConfig exportConfig = new ExportConfig();
            exportConfig.setKey(entry.getKey());
            exportConfig.setName(entry.getKey());
            exportConfig.setValue(1);
            exportConfig.setOrder(order);
            order++;

            exportConfigList.add(exportConfig);
        }
        System.out.println(JSON.toJSONString(exportConfigList));
    }
}
