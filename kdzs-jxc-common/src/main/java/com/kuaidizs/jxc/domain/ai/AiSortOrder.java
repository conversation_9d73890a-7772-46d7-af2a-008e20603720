package com.kuaidizs.jxc.domain.ai;

import com.kuaidizs.jxc.common.util.StringUtil;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@Data
public class AiSortOrder extends AiBaseDomain {
    /**
     * 订单同步的唯一标识
     */
    private String syncKey;
    /**
     * 主单号
     */
    private String masterTid;
    /**
     * 子订单号
     */
    private List<String> oids;
    /**
     * 商品ID
     */
    private String itemId;
    /**
     * 商品名称
     */
    private String itemName;
    /**
     * 商品商家编码
     */
    private String itemOuterId;
    /**
     * 商品图片
     */
    private String itemPicUrl;
    /**
     * 规格Id
     */
    private String skuId;
    /**
     * 规格名称
     */
    private String skuName;
    /**
     * 规格图片
     */
    private String skuPicUrl;
    /**
     * 商品数量
     */
    private Integer num;
    /**
     * 商品实付金额
     */
    private BigDecimal payment;
    /**
     * 数据版本
     */
    private Long version;


    //============
    private String oidsStr;

    //排序字段=====================
    private String skuColor;
    private Integer skuSize;

    public void initFieldsBeforeInsert() {
        if (CollectionUtils.isNotEmpty(this.oids)) {
            this.oidsStr = StringUtils.join(this.oids, ",");
        }
    }

    public void initFieldsForResult() {
        if (StringUtils.isNotBlank(this.oidsStr)) {
            this.oids = new ArrayList<>();
            for (String oid : this.oidsStr.split(",")) {
                this.oids.add(oid);
            }
        }
        //一些不是很重要的字段，按数据库分配的长度截断一下
        this.itemName = StringUtil.frontSubstring(this.itemName, 256);
        this.skuName = StringUtil.frontSubstring(this.skuName, 256);
    }
}
