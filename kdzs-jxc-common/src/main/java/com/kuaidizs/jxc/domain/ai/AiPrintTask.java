package com.kuaidizs.jxc.domain.ai;

import com.kuaidizs.jxc.common.enums.ai.TaskStatus;
import com.kuaidizs.jxc.common.util.StringUtil;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@Data
public class AiPrintTask {
    /**
     * 待生成的任务ID,即还在校验是否允许生成任务的阶段
     */
    public static final Long TASK_ID_WAIT_TO_ADD = -1L;
    /**
     * 任务ID
     */
    private Long id;
    /**
     * 创建任务的用户ID
     */
    private Long createUserId;
    /**
     * 创建任务的用户名
     */
    private String createUserName;
    /**
     * 实际参与任务的用户ID
     */
    private List<Long> userIds;
    /**
     * 任务状态
     */
    private TaskStatus taskStatus;

    /**
     * 任务开始时间
     */
    private Date taskTimeStart;

    /**
     * 任务结束时间
     */
    private Date taskTimeEnd;

    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;


    private String userIdsStr;
    private Integer taskStatusCode;

    public void initFieldsBeforeInsert() {
        if (CollectionUtils.isNotEmpty(this.userIds)) {
            this.userIdsStr = StringUtils.join(this.userIds, ",");
        }
        if (this.taskStatus != null) {
            this.taskStatusCode = this.taskStatus.getCode();
        }
    }

    public void initFieldsForResult() {
        if (StringUtils.isNotBlank(this.userIdsStr)) {
            this.userIds = StringUtil.splitToLongList(this.userIdsStr, ",");
        }
        if (this.taskStatusCode != null) {
            this.taskStatus = TaskStatus.getByCode(this.taskStatusCode);
        }
    }

    public AiPrintTaskCacheData toAiPrintTaskCacheData() {
        AiPrintTaskCacheData data = new AiPrintTaskCacheData();
        data.setTaskId(this.id);
        data.setCreateUserId(this.createUserId);
        data.setUserIds(this.userIds);
        return data;
    }
}
