package com.kuaidizs.jxc.domain.agent;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/12/12.
 * @time 19:33.
 */
public class BillCenter implements Serializable {
    /**
     *序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 更新时间
     */
    private Date modified;
    /**
     * 出账方id
     */
    private String billFromId;
    /**
     * 出账方名称
     */
    private String billFromName;
    /**
     * 结算方
     */
    private String billToId;
    /**
     * 结算方名称
     */
    private String billToName;
    /**
     * 订单状态
     */
    private String orderStatus;
    /**
     * 退款状态
     */
    private String refundStatus;
    /**
     * 1: 按订单发货时间
     */
    private Integer timeType;
    /**
     * 出账开始时间
     */
    private Date startTime;
    /**
     * 出账结束时间
     */
    private Date endTime;
    /**
     * 结算金额
     */
    private String settlementTotal;
    /**
     * 账单状态 0:出账中 1:待对方确认 2:已确认
     */
    private Integer billStatus;
    /**
     * 备注
     */
    private String remarks;

    /**
     * 导出结果,导出失败时存储导出失败原因，导出成功时存储文件OSS地址
     */
    private String exportMsg;

    public String getExportMsg() {
        return exportMsg;
    }

    public void setExportMsg(String exportMsg) {
        this.exportMsg = exportMsg;
    }

    /**
     * @return id 自增id
     */
    public Long getId() {
        return id;
    }
    /**
     * @param id 自增id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }
    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 更新时间
     */
    public Date getModified() {
        return modified;
    }
    /**
     * @param modified 更新时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return billFromId 出账方id
     */
    public String getBillFromId() {
        return billFromId;
    }
    /**
     * @param billFromId 出账方id
     */
    public void setBillFromId(String billFromId) {
        this.billFromId = billFromId;
    }

    /**
     * @return billFromName 出账方名称
     */
    public String getBillFromName() {
        return billFromName;
    }
    /**
     * @param billFromName 出账方名称
     */
    public void setBillFromName(String billFromName) {
        this.billFromName = billFromName;
    }

    /**
     * @return billToId 结算方
     */
    public String getBillToId() {
        return billToId;
    }
    /**
     * @param billToId 结算方
     */
    public void setBillToId(String billToId) {
        this.billToId = billToId;
    }

    /**
     * @return billToName 结算方名称
     */
    public String getBillToName() {
        return billToName;
    }
    /**
     * @param billToName 结算方名称
     */
    public void setBillToName(String billToName) {
        this.billToName = billToName;
    }

    /**
     * @return orderStatus 订单状态
     */
    public String getOrderStatus() {
        return orderStatus;
    }
    /**
     * @param orderStatus 订单状态
     */
    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    /**
     * @return refundStatus 退款状态
     */
    public String getRefundStatus() {
        return refundStatus;
    }
    /**
     * @param refundStatus 退款状态
     */
    public void setRefundStatus(String refundStatus) {
        this.refundStatus = refundStatus;
    }

    /**
     * @return timeType 1: 按订单发货时间
     */
    public Integer getTimeType() {
        return timeType;
    }
    /**
     * @param timeType 1: 按订单发货时间
     */
    public void setTimeType(Integer timeType) {
        this.timeType = timeType;
    }

    /**
     * @return startTime 出账开始时间
     */
    public Date getStartTime() {
        return startTime;
    }
    /**
     * @param startTime 出账开始时间
     */
    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    /**
     * @return endTime 出账结束时间
     */
    public Date getEndTime() {
        return endTime;
    }
    /**
     * @param endTime 出账结束时间
     */
    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    /**
     * @return settlementTotal 结算金额
     */
    public String getSettlementTotal() {
        return settlementTotal;
    }
    /**
     * @param settlementTotal 结算金额
     */
    public void setSettlementTotal(String settlementTotal) {
        this.settlementTotal = settlementTotal;
    }

    /**
     * @return billStatus 账单状态 0:出账中 1:待对方确认 2:已确认
     */
    public Integer getBillStatus() {
        return billStatus;
    }
    /**
     * @param billStatus 账单状态 0:出账中 1:待对方确认 2:已确认
     */
    public void setBillStatus(Integer billStatus) {
        this.billStatus = billStatus;
    }

    /**
     * @return remarks 备注
     */
    public String getRemarks() {
        return remarks;
    }
    /**
     * @param remarks 备注
     */
    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

}
