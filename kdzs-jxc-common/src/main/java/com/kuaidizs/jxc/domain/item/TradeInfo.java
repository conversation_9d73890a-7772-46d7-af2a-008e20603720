package com.kuaidizs.jxc.domain.item;

import com.kuaidizs.jxc.common.util.Constant;
import com.kuaidizs.jxc.common.util.DesUtil;

import java.io.Serializable;
import java.util.List;

/**
 * 订单商品信息
 * <AUTHOR>
 * @since 2018/08/14
 */
public class TradeInfo implements Serializable {

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public List<ItemSkuInfo> getOrders() {
        return orders;
    }

    public void setOrders(List<ItemSkuInfo> orders) {
        this.orders = orders;
    }

    public List<ItemSkuInfo> getGifts() {
        return gifts;
    }

    public void setGifts(List<ItemSkuInfo> gifts) {
        this.gifts = gifts;
    }

    /**
     * 订单号
     */
    private String tid;
    /**
     * 平台商品信息
     */
    private List<ItemSkuInfo> orders;

    /**
     * 赠品商品信息
     */
    private List<ItemSkuInfo> gifts;

    public static void main(String[] args) {
        String userId = "D39C797EAD7D61E7";
        String decrypt = DesUtil.decrypt(userId + "", Constant.TOKEN_DES_KEY);
        System.out.println(decrypt);
    }
}
