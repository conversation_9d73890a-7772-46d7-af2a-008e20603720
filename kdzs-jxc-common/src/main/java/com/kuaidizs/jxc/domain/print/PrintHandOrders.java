package com.kuaidizs.jxc.domain.print;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;

import java.util.*;
import java.math.BigDecimal;
import java.io.Serializable;

/**
 *
 * <AUTHOR> @date    2016-08-19
 */
public class PrintHandOrders implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 主键自增id
     */
    private Long id;
	/**
     * 用户id
     */
    private Long taobaoId;
	/**
     * tid流水号
     */
    @JSONField(name="tid", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String tid;

    /**
     * 隐藏id
     */
    @JSONField(name="realtid", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String realTid;

	/**
     * 创建日期
     */
    @JSONField(name="creatTime", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Date created;
	/**
     * 收件人
     */
    @JSONField(name="receiverName", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String recipient;
	/**
     * 固话
     */
    @JSONField(name="receiverTel", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String receiverTel;
	/**
     * 手机
     */
    @JSONField(name="receiverPhone", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String receiverPhone;
	/**
     * 快递单号信息
     */
    @JSONField(name="exNumber", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String exnumber;
	/**
     * 省
     */
    @JSONField(name="receiverProvince", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String receiverProvince;
	/**
     * 市
     */
    @JSONField(name="receiverCity", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String receiverCity;
	/**
     * 区
     */
    @JSONField(name="receiverArea", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String receiverCounty;
	/**
     * 收件地址
     */
    @JSONField(name="receiverDetail", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String receiverAddress;
	/**
     * 发件信息
     */
    @JSONField(name="shipInfo", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String sendinfo;
	/**
     * 是否打印(0)/打印次数
     */
    @JSONField(name="isKddPrinted", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer isPrint;
	/**
     * 是否删除
     */
    private Boolean isDel;
	/**
     * 发件人   //以下套打新加字段
     */
    @JSONField(name="senderName", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String senderName;
	/**
     * 发件人电话
     */
    @JSONField(name="senderTel", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String senderTel;

    /**
     * 发件人手机
     */
    @JSONField(name="senderPhone", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String senderPhone;
	/**
     * 发件人地址
     */
    @JSONField(name="senderAddress", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String senderAddress;
	/**
     * 加单编号
     */
    private String addExNumber;
	/**
     * 加单件数
     */
    private String addExCount;
	/**
     * 加单业务员
     */
    private String addExName;
	/**
     * 内件名
     */
    @JSONField(name="internalName", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String internalName;
	/**
     * 0手打 1套打
     */
    @JSONField(name="type", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer type;
	/**
     * 快递单号
     */
    @JSONField(name="trueExNumber", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String trueExNumber;
	/**
     * 是否可修改发件人信息，0否1是
     */
    @JSONField(name="isModifySender", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer isModifySender = 0;
	/**
     * 是否发货 0未发货 1已发货
     */
    @JSONField(name="isFh", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer isFh;
	/**
     * 是否为云栈电子面单打印，0不是 1是
     */
    @JSONField(name="isYunzhan",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer isYunzhan;
	/**
     * 订单号是否回收（0没回收 1已回收）
     */
    @JSONField(name="exIsHuishou", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer exIsHuishou;
	/**
     * 是否为淘宝发货订单，0不是 1是
     */
    @JSONField(name="isTbSend", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer isTbSend;
	/**
     * 备注
     */
    @JSONField(name="sellerMemo", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String remark;
	/**
     * 代收金额
     */
    @JSONField(name="collectionMoney", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private BigDecimal collectionMoney;
    /**
     * 代收货款字符串
     */
    @JSONField(serialize = false,deserialize = false)
    private String collectionMoneyStr;
	/**
     * 保价金额
     */
    @JSONField(name="declarationValue", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private BigDecimal declarationValue;
    /**
     * 保价金额字符串
     */
    @JSONField(serialize = false,deserialize = false)
    private String declarationValueStr;
	/**
     * 业务类型
     */
    @JSONField(name="businessType", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String businessType;
	/**
     * 回收来源 默认0：正常订单 1：已打印订单编辑  2：重打非当前模板
     */
    @JSONField(name="recyclingSources", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer recyclingSources;
	/**
     * modified
     */
    private Date modified;
	/**
     * enable_status
     */
    private Boolean enableStatus;

    /**
     * 发件人信息ID 对应ModeSetFjrKdd中的ID
     */
    @JSONField(name="senderId", serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Long senderId;


   /**
    * @return id 主键自增id
    */
    public Long getId() {
       return id;
    }
   /**
    * @param id 主键自增id
    */
    public void setId(Long id) {
       this.id = id;
    }
	
   /**
    * @return taobaoId 用户id
    */
    public Long getTaobaoId() {
       return taobaoId;
    }
   /**
    * @param taobaoId 用户id
    */
    public void setTaobaoId(Long taobaoId) {
       this.taobaoId = taobaoId;
    }
	
   /**
    * @return tid tid流水号
    */
    public String getTid() {
       return tid;
    }
   /**
    * @param tid tid流水号
    */
    public void setTid(String tid) {
       this.tid = tid;
    }

    /**
     * @return realTid 隐藏id
    */
    public String getRealTid() {
        return realTid;
    }

    /**
     * @param realTid 隐藏id
     */
    public void setRealTid(String realTid) {
        this.realTid = realTid;
    }


   /**
    * @return created 创建日期
    */
    public Date getCreated() {
       return created;
    }
   /**
    * @param created 创建日期
    */
    public void setCreated(Date created) {
       this.created = created;
    }
	
   /**
    * @return recipient 收件人
    */
    public String getRecipient() {
       return recipient;
    }
   /**
    * @param recipient 收件人
    */
    public void setRecipient(String recipient) {
       this.recipient = recipient;
    }
	
   /**
    * @return receiverPhone 固话
    */
    public String getReceiverPhone() {
       return receiverPhone;
    }
   /**
    * @param receiverPhone 固话
    */
    public void setReceiverPhone(String receiverPhone) {
       this.receiverPhone = receiverPhone;
    }
	
   /**
    * @return receiverTel 手机
    */
    public String getReceiverTel() {
       return receiverTel;
    }
   /**
    * @param receiverTel 手机
    */
    public void setReceiverTel(String receiverTel) {
       this.receiverTel = receiverTel;
    }
	
   /**
    * @return exnumber 快递单号信息
    */
    public String getExnumber() {
       return exnumber;
    }
   /**
    * @param exnumber 快递单号信息
    */
    public void setExnumber(String exnumber) {
       this.exnumber = exnumber;
    }
	
   /**
    * @return receiverProvince 省
    */
    public String getReceiverProvince() {
       return receiverProvince;
    }
   /**
    * @param receiverProvince 省
    */
    public void setReceiverProvince(String receiverProvince) {
       this.receiverProvince = receiverProvince;
    }
	
   /**
    * @return receiverCity 市
    */
    public String getReceiverCity() {
       return receiverCity;
    }
   /**
    * @param receiverCity 市
    */
    public void setReceiverCity(String receiverCity) {
       this.receiverCity = receiverCity;
    }
	
   /**
    * @return receiverDistrict 区
    */
    public String getReceiverCounty() {
       return receiverCounty;
    }
   /**
    * @param receiverCounty 区
    */
    public void setReceiverCounty(String receiverCounty) {
       this.receiverCounty = receiverCounty;
    }
	
   /**
    * @return receiverAddress 收件地址
    */
    public String getReceiverAddress() {
       return receiverAddress;
    }
   /**
    * @param receiverAddress 收件地址
    */
    public void setReceiverAddress(String receiverAddress) {
       this.receiverAddress = receiverAddress;
    }
	
   /**
    * @return sendinfo 发件信息
    */
    public String getSendinfo() {
       return sendinfo;
    }
   /**
    * @param sendinfo 发件信息
    */
    public void setSendinfo(String sendinfo) {
       this.sendinfo = sendinfo;
    }
	
   /**
    * @return isPrint 是否打印(0)/打印次数
    */
    public Integer getIsPrint() {
       return isPrint;
    }
   /**
    * @param isPrint 是否打印(0)/打印次数
    */
    public void setIsPrint(Integer isPrint) {
       this.isPrint = isPrint;
    }
	
   /**
    * @return isDel 是否删除
    */
    public Boolean getIsDel() {
       return isDel;
    }
   /**
    * @param isDel 是否删除
    */
    public void setIsDel(Boolean isDel) {
       this.isDel = isDel;
    }
	
   /**
    * @return senderName 发件人   //以下套打新加字段
    */
    public String getSenderName() {
       return senderName;
    }
   /**
    * @param senderName 发件人   //以下套打新加字段
    */
    public void setSenderName(String senderName) {
       this.senderName = senderName;
    }
   /**
    * @return senderTel 发件人手机
    */
    public String getSenderTel() {
       return senderTel;
    }

    /**
     * @param senderTel 发件人手机
     */
    public void setSenderTel(String senderTel) {
        this.senderTel = senderTel;
    }
    /**
     * @param senderPhone 发件人电话
     */
    public void setSenderPhone(String senderPhone) {
        this.senderPhone = senderPhone;
    }

    /**
     * @return senderPhone 发件人电话
     */
    public String getSenderPhone() {
        return senderPhone;
    }
   /**
    * @return senderAddress 发件人地址
    */
    public String getSenderAddress() {
       return senderAddress;
    }
   /**
    * @param senderAddress 发件人地址
    */
    public void setSenderAddress(String senderAddress) {
       this.senderAddress = senderAddress;
    }
	
   /**
    * @return addExNumber 加单编号
    */
    public String getAddExNumber() {
       return addExNumber;
    }
   /**
    * @param addExNumber 加单编号
    */
    public void setAddExNumber(String addExNumber) {
       this.addExNumber = addExNumber;
    }
	
   /**
    * @return addExCount 加单件数
    */
    public String getAddExCount() {
       return addExCount;
    }
   /**
    * @param addExCount 加单件数
    */
    public void setAddExCount(String addExCount) {
       this.addExCount = addExCount;
    }
	
   /**
    * @return addExName 加单业务员
    */
    public String getAddExName() {
       return addExName;
    }
   /**
    * @param addExName 加单业务员
    */
    public void setAddExName(String addExName) {
       this.addExName = addExName;
    }
	
   /**
    * @return internalName 内件名
    */
    public String getInternalName() {
       return internalName;
    }
   /**
    * @param internalName 内件名
    */
    public void setInternalName(String internalName) {
       this.internalName = internalName;
    }
	
   /**
    * @return type 0手打 1套打
    */
    public Integer getType() {
       return type;
    }
   /**
    * @param type 0手打 1套打
    */
    public void setType(Integer type) {
       this.type = type;
    }
	
   /**
    * @return trueExNumber 快递单号
    */
    public String getTrueExNumber() {
       return trueExNumber;
    }
   /**
    * @param trueExNumber 快递单号
    */
    public void setTrueExNumber(String trueExNumber) {
       this.trueExNumber = trueExNumber;
    }
	
   /**
    * @return IsModifySender 是否可修改发件人信息，0否1是
    */
    public Integer getIsModifySender() {
        return isModifySender;
    }
   /**
    * @param isModifySender 是否可修改发件人信息，0否1是
    */
   public void setIsModifySender(Integer isModifySender) {
       this.isModifySender = isModifySender;
   }
   /**
    * @return IsFh 是否发货 0未发货 1已发货
    */
   public Integer getIsFh() {
       return isFh;
   }
   /**
    * @param isFh 是否发货 0未发货 1已发货
    */
   public void setIsFh(Integer isFh) {
       this.isFh = isFh;
   }
   /**
    * @return isYunzhan 是否为云栈电子面单打印，0不是 1是
    */
    public Integer getIsYunzhan() {
       return isYunzhan;
    }
   /**
    * @param isYunzhan 是否为云栈电子面单打印，0不是 1是
    */
    public void setIsYunzhan(Integer isYunzhan) {
       this.isYunzhan = isYunzhan;
    }
	
   /**
    * @return exIsHuishou 订单号是否回收（0没回收 1已回收）
    */
    public Integer getExIsHuishou() {
       return exIsHuishou;
    }
   /**
    * @param exIsHuishou 订单号是否回收（0没回收 1已回收）
    */
    public void setExIsHuishou(Integer exIsHuishou) {
       this.exIsHuishou = exIsHuishou;
    }
	
   /**
    * @return isTbSend 是否为淘宝发货订单，0不是 1是
    */
    public Integer getIsTbSend() {
       return isTbSend;
    }
   /**
    * @param isTbSend 是否为淘宝发货订单，0不是 1是
    */
    public void setIsTbSend(Integer isTbSend) {
       this.isTbSend = isTbSend;
    }
	
   /**
    * @return remark 备注
    */
    public String getRemark() {
       return remark;
    }
   /**
    * @param remark 备注
    */
    public void setRemark(String remark) {
       this.remark = remark;
    }
	
   /**
    * @return collectionMoney 代收金额
    */
    public BigDecimal getCollectionMoney() {
       return collectionMoney;
    }
   /**
    * @param collectionMoney 代收金额
    */
    public void setCollectionMoney(BigDecimal collectionMoney) {
       this.collectionMoney = collectionMoney;
    }
	
   /**
    * @return declarationValue 保价金额
    */
    public BigDecimal getDeclarationValue() {
       return declarationValue;
    }
   /**
    * @param declarationValue 保价金额
    */
    public void setDeclarationValue(BigDecimal declarationValue) {
       this.declarationValue = declarationValue;
    }
	
   /**
    * @return businessType 业务类型
    */
    public String getBusinessType() {
       return businessType;
    }
   /**
    * @param businessType 业务类型
    */
    public void setBusinessType(String businessType) {
       this.businessType = businessType;
    }
	
   /**
    * @return recyclingSources 回收来源 默认0：正常订单 1：已打印订单编辑  2：重打非当前模板
    */
    public Integer getRecyclingSources() {
       return recyclingSources;
    }
   /**
    * @param recyclingSources 回收来源 默认0：正常订单 1：已打印订单编辑  2：重打非当前模板
    */
    public void setRecyclingSources(Integer recyclingSources) {
       this.recyclingSources = recyclingSources;
    }
	
   /**
    * @return modified modified
    */
    public Date getModified() {
       return modified;
    }
   /**
    * @param modified modified
    */
    public void setModified(Date modified) {
       this.modified = modified;
    }
	
   /**
    * @return enableStatus enable_status
    */
    public Boolean getEnableStatus() {
       return enableStatus;
    }
   /**
    * @param enableStatus enable_status
    */
    public void setEnableStatus(Boolean enableStatus) {
       this.enableStatus = enableStatus;
    }

    public Long getSenderId() {
        return senderId;
    }

    public void setSenderId(Long senderId) {
        this.senderId = senderId;
    }

    public String exHuishou = (this.exIsHuishou != null && this.exIsHuishou == 1) ? "(已回收)":null;//回收字符串

    public String getExHuishou() {
        if(this.exIsHuishou == 1) return "(已回收)";
        return exHuishou;
    }

    public void setExHuishou(String exHuishou) {
        this.exHuishou = exHuishou;
    }

    public int index;

    public int getIndex() {
        return index;
    }

    public void setIndex(int index) {
        this.index = index;
    }

    public String getCollectionMoneyStr() {
        return collectionMoneyStr;
    }

    public void setCollectionMoneyStr(String collectionMoneyStr) {
        this.collectionMoneyStr = collectionMoneyStr;
    }

    public String getDeclarationValueStr() {
        return declarationValueStr;
    }

    public void setDeclarationValueStr(String declarationValueStr) {
        this.declarationValueStr = declarationValueStr;
    }

    private String tableName;
    private String fkId;

    public String getFkId() {
        return fkId;
    }

    public void setFkId(String fkId) {
        this.fkId = fkId;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof PrintHandOrders)) return false;
        PrintHandOrders printHandOrders = (PrintHandOrders) o;
        return tid != null ? tid.equals(printHandOrders.getTid()) : false;

    }

    @Override
    public int hashCode() {
        int result = 1;
        result = 31 * result + (tid != null ? tid.hashCode() : 0);
        return result;
    }
}