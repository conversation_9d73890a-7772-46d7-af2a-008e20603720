package com.kuaidizs.jxc.domain.log;

import lombok.Data;

import java.util.*;
import java.io.Serializable;

/**
 *
 * <AUTHOR>
 * Date    2025-03-17
 */
@Data
public class OperationLinkLog implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 主键id
     */
    private Long id;
	/**
     * 登录账号id
     */
    private Long userId;
	/**
     * 登录账号名称
     */
    private String userName;
	/**
     * 查询条件, json格式
     */
    private String queryCondition;
	/**
     * 取消勾选的订单和商品信息
     */
    private String cancelTrades;
	/**
     * 选中订单数量
     */
    private Integer selectedOrderCount;

    /**
     * 查询订单量
     */
    private Integer totalOrderCount;
	/**
     * 排序条件,json格式
     */
    private String sortCondition;
    /**
     * 选中的订单
     */
    private String selectedTrades;
	/**
     * 页码, 只需第一页
     */
    private Integer pageNo;
	/**
     * 每页大小
     */
    private Integer pageSize;
	/**
     * 快递模板id
     */
    private Long kdTemplateId;
	/**
     * 快递模板名称
     */
    private String kdTemplateName;
	/**
     * 1:可用,0:不可用
     */
    private Integer enableStatus;
	/**
     * 点击查询的时间
     */
    private Date queryTime;
	/**
     * 查询完成时间
     */
    private Date queryCompleteTime;
	/**
     * 点击打印的时间
     */
    private Date printTime;
	/**
     * 1:自动合单, 0:不自动合单
     */
    private Integer autoMerge;
	/**
     * 自动合单数量
     */
    private Integer mergeNum;
	/**
     * 1:包含预售,0:不包含预售
     */
    private Integer includePresell;
	/**
     * 添加时间
     */
    private Date created;

    private String checkRule;


   /**
    * @return id 主键id
    */
    public Long getId() {
       return id;
    }
   /**
    * @param id 主键id
    */
    public void setId(Long id) {
       this.id = id;
    }

   /**
    * @return userId 登录账号id
    */
    public Long getUserId() {
       return userId;
    }
   /**
    * @param userId 登录账号id
    */
    public void setUserId(Long userId) {
       this.userId = userId;
    }

   /**
    * @return userName 登录账号名称
    */
    public String getUserName() {
       return userName;
    }
   /**
    * @param userName 登录账号名称
    */
    public void setUserName(String userName) {
       this.userName = userName;
    }

   /**
    * @return queryCondition 查询条件, json格式
    */
    public String getQueryCondition() {
       return queryCondition;
    }
   /**
    * @param queryCondition 查询条件, json格式
    */
    public void setQueryCondition(String queryCondition) {
       this.queryCondition = queryCondition;
    }

   /**
    * @return cancelTrades 取消勾选的订单和商品信息
    */
    public String getCancelTrades() {
       return cancelTrades;
    }
   /**
    * @param cancelTrades 取消勾选的订单和商品信息
    */
    public void setCancelTrades(String cancelTrades) {
       this.cancelTrades = cancelTrades;
    }

   /**
    * @return selectedOrderCount 选中订单数量
    */
    public Integer getSelectedOrderCount() {
       return selectedOrderCount;
    }
   /**
    * @param selectedOrderCount 选中订单数量
    */
    public void setSelectedOrderCount(Integer selectedOrderCount) {
       this.selectedOrderCount = selectedOrderCount;
    }

   /**
    * @return sortCondition 排序条件,json格式
    */
    public String getSortCondition() {
       return sortCondition;
    }
   /**
    * @param sortCondition 排序条件,json格式
    */
    public void setSortCondition(String sortCondition) {
       this.sortCondition = sortCondition;
    }

   /**
    * @return pageNo 页码, 只需第一页
    */
    public Integer getPageNo() {
       return pageNo;
    }
   /**
    * @param pageNo 页码, 只需第一页
    */
    public void setPageNo(Integer pageNo) {
       this.pageNo = pageNo;
    }

   /**
    * @return pageSize 每页大小
    */
    public Integer getPageSize() {
       return pageSize;
    }
   /**
    * @param pageSize 每页大小
    */
    public void setPageSize(Integer pageSize) {
       this.pageSize = pageSize;
    }

   /**
    * @return kdTemplateId 快递模板id
    */
    public Long getKdTemplateId() {
       return kdTemplateId;
    }
   /**
    * @param kdTemplateId 快递模板id
    */
    public void setKdTemplateId(Long kdTemplateId) {
       this.kdTemplateId = kdTemplateId;
    }

   /**
    * @return kdTemplateName 快递模板名称
    */
    public String getKdTemplateName() {
       return kdTemplateName;
    }
   /**
    * @param kdTemplateName 快递模板名称
    */
    public void setKdTemplateName(String kdTemplateName) {
       this.kdTemplateName = kdTemplateName;
    }

   /**
    * @return enableStatus 1:可用,0:不可用
    */
    public Integer getEnableStatus() {
       return enableStatus;
    }
   /**
    * @param enableStatus 1:可用,0:不可用
    */
    public void setEnableStatus(Integer enableStatus) {
       this.enableStatus = enableStatus;
    }

   /**
    * @return queryTime 点击查询的时间
    */
    public Date getQueryTime() {
       return queryTime;
    }
   /**
    * @param queryTime 点击查询的时间
    */
    public void setQueryTime(Date queryTime) {
       this.queryTime = queryTime;
    }

   /**
    * @return queryCompleteTime 查询完成时间
    */
    public Date getQueryCompleteTime() {
       return queryCompleteTime;
    }
   /**
    * @param queryCompleteTime 查询完成时间
    */
    public void setQueryCompleteTime(Date queryCompleteTime) {
       this.queryCompleteTime = queryCompleteTime;
    }

   /**
    * @return printTime 点击打印的时间
    */
    public Date getPrintTime() {
       return printTime;
    }
   /**
    * @param printTime 点击打印的时间
    */
    public void setPrintTime(Date printTime) {
       this.printTime = printTime;
    }

   /**
    * @return autoMerge 1:自动合单, 0:不自动合单
    */
    public Integer getAutoMerge() {
       return autoMerge;
    }
   /**
    * @param autoMerge 1:自动合单, 0:不自动合单
    */
    public void setAutoMerge(Integer autoMerge) {
       this.autoMerge = autoMerge;
    }

   /**
    * @return mergeNum 自动合单数量
    */
    public Integer getMergeNum() {
       return mergeNum;
    }
   /**
    * @param mergeNum 自动合单数量
    */
    public void setMergeNum(Integer mergeNum) {
       this.mergeNum = mergeNum;
    }

   /**
    * @return includePresell 1:包含预售,0:不包含预售
    */
    public Integer getIncludePresell() {
       return includePresell;
    }
   /**
    * @param includePresell 1:包含预售,0:不包含预售
    */
    public void setIncludePresell(Integer includePresell) {
       this.includePresell = includePresell;
    }

   /**
    * @return created 添加时间
    */
    public Date getCreated() {
       return created;
    }
   /**
    * @param created 添加时间
    */
    public void setCreated(Date created) {
       this.created = created;
    }

    public Integer getTotalOrderCount() {
        return totalOrderCount;
    }

    public void setTotalOrderCount(Integer totalOrderCount) {
        this.totalOrderCount = totalOrderCount;
    }
}