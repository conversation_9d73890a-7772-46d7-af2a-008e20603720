package com.kuaidizs.jxc.domain.cnprivacy;

import java.io.Serializable;
import java.util.List;

/***
 * program: kdzs-jxc
 * description: 
 * author: <PERSON><PERSON><PERSON><PERSON>@raycloud.com
 * create: 2020-08-06 14:05
 **/
public class CnPrivacyMarkRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long taobaoId;

    private List<String> tids;

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public List<String> getTids() {
        return tids;
    }

    public void setTids(List<String> tids) {
        this.tids = tids;
    }

}
