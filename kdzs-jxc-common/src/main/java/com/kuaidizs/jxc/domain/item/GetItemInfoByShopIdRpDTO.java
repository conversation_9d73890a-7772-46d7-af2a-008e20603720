package com.kuaidizs.jxc.domain.item;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/9/5 10:20
 */
@Data
public class GetItemInfoByShopIdRpDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private String shopId; //店铺ID
    //private String shopName; //
    private String numIid; //商品ID
    //private String title; //商品标题
    private String shortTitle; //商品简称
    private String stall; //档口
    private String price; //成本价

    private String market; //市场
    private List<ItemSku> itemSkus;

    @Data
    public static class ItemSku implements Serializable{
        private static final long serialVersionUID = 1L;
        private String skuId; //规格ID
        private String skuAlias; //规格别名
        private String weight; //重量
        private String skuPrice; //sku价格
    }
}
