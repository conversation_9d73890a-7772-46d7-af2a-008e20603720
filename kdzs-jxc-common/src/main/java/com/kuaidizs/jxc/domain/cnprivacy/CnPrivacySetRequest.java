package com.kuaidizs.jxc.domain.cnprivacy;

import java.io.Serializable;

/***
 * program: kdzs-jxc
 * description: 
 * author: <PERSON><PERSON><PERSON><PERSON>@raycloud.com
 * create: 2020-08-05 23:59
 **/
public class CnPrivacySetRequest implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 是否开启菜鸟隐私面单：1:开启0:关闭 默认0
     */
    private Integer privacySwitch;

    /**
     * 是否允许其他店铺使用改帐号打印隐私面单，1:开启，0:关闭 ，默认0
     */
    private Integer otherSwitch;

    /**
     * 匹配规则 0:没选中任何一项 ，1:淘宝隐私开启，2:手工单隐私开启，3不选 默认 0
     */
    private Integer matchRule;

    /**
     * 保护收件人姓名 1：保护 0：不保护
     */
    private Integer protectReceiverName;
    /**
     * 保护收件人手机号
     */
    private Integer protectReceiverPhone;
    /**
     * 保护发件人姓名
     */
    private Integer protectSenderName;
    /**
     * 保护发件人手机号
     */
    private Integer protectSenderPhone;

    /**
     * 宝贝匹配规则是否开启 1：开启 0：不开启
     */
    private Integer goodRule;
    /**
     * 留言备注匹配规则是否开启 1：开启 0：不开启
     */
    private Integer remarkRule;


    public Integer getPrivacySwitch() {
        return privacySwitch;
    }

    public void setPrivacySwitch(Integer privacySwitch) {
        this.privacySwitch = privacySwitch;
    }

    public Integer getOtherSwitch() {
        return otherSwitch;
    }

    public void setOtherSwitch(Integer otherSwitch) {
        this.otherSwitch = otherSwitch;
    }

    public Integer getMatchRule() {
        return matchRule;
    }

    public void setMatchRule(Integer matchRule) {
        this.matchRule = matchRule;
    }

    public Integer getProtectReceiverName() {
        return protectReceiverName;
    }

    public void setProtectReceiverName(Integer protectReceiverName) {
        this.protectReceiverName = protectReceiverName;
    }

    public Integer getProtectReceiverPhone() {
        return protectReceiverPhone;
    }

    public void setProtectReceiverPhone(Integer protectReceiverPhone) {
        this.protectReceiverPhone = protectReceiverPhone;
    }

    public Integer getProtectSenderName() {
        return protectSenderName;
    }

    public void setProtectSenderName(Integer protectSenderName) {
        this.protectSenderName = protectSenderName;
    }

    public Integer getProtectSenderPhone() {
        return protectSenderPhone;
    }

    public void setProtectSenderPhone(Integer protectSenderPhone) {
        this.protectSenderPhone = protectSenderPhone;
    }

    public Integer getGoodRule() {
        return goodRule;
    }

    public void setGoodRule(Integer goodRule) {
        this.goodRule = goodRule;
    }

    public Integer getRemarkRule() {
        return remarkRule;
    }

    public void setRemarkRule(Integer remarkRule) {
        this.remarkRule = remarkRule;
    }
}
