package com.kuaidizs.jxc.domain.print.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.kuaidizs.jxc.domain.print.ModeListshow;

import java.io.Serializable;
import java.util.List;

/**
 * Created by liumandong on 2020/7/20
 */
public class ThdTemplateListVo implements Serializable {

    /**
     * 默认模板ID
     */
    @JSONField(name = "ModeListShowId")
    private Integer defaultModeListshowId;
    /**
     * 模板列表
     */
    @JSONField(name = "ModeListShows")
    private List<ModeListshow> modeListshows;

    public Integer getDefaultModeListshowId() {
        return defaultModeListshowId;
    }

    public void setDefaultModeListshowId(Integer defaultModeListshowId) {
        this.defaultModeListshowId = defaultModeListshowId;
    }

    public List<ModeListshow> getModeListshows() {
        return modeListshows;
    }

    public void setModeListshows(List<ModeListshow> modeListshows) {
        this.modeListshows = modeListshows;
    }
}
