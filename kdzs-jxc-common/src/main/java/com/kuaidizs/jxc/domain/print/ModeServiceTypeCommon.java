package com.kuaidizs.jxc.domain.print;

import java.io.Serializable;

/**
 *
 * <AUTHOR> @date    2016-12-05
 */
public class ModeServiceTypeCommon implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * id
     */
    private Integer id;
	/**
     * code
     */
    private String code;
	/**
     * name
     */
    private String name;
	/**
     * type_id
     */
    private Integer typeId;
	/**
     * type_key
     */
    private String typeKey;
	/**
     * type_name
     */
    private String typeName;
	/**
     * type_value
     */
    private String typeValue;
	/**
     * type_tips
     */
    private String typeTips;
	/**
     * is_default
     */
    private Integer isDefault;
    /**
     * 类型：2-网点，3-菜鸟
     */
    private Integer kddType;
	
   /**
    * @return id id
    */
    public Integer getId() {
       return id;
    }
   /**
    * @param id id
    */
    public void setId(Integer id) {
       this.id = id;
    }
	
   /**
    * @return code code
    */
    public String getCode() {
       return code;
    }
   /**
    * @param code code
    */
    public void setCode(String code) {
       this.code = code;
    }
	
   /**
    * @return name name
    */
    public String getName() {
       return name;
    }
   /**
    * @param name name
    */
    public void setName(String name) {
       this.name = name;
    }
	
   /**
    * @return typeId type_id
    */
    public Integer getTypeId() {
       return typeId;
    }
   /**
    * @param typeId type_id
    */
    public void setTypeId(Integer typeId) {
       this.typeId = typeId;
    }
	
   /**
    * @return typeKey type_key
    */
    public String getTypeKey() {
       return typeKey;
    }
   /**
    * @param typeKey type_key
    */
    public void setTypeKey(String typeKey) {
       this.typeKey = typeKey;
    }
	
   /**
    * @return typeName type_name
    */
    public String getTypeName() {
       return typeName;
    }
   /**
    * @param typeName type_name
    */
    public void setTypeName(String typeName) {
       this.typeName = typeName;
    }
	
   /**
    * @return typeValue type_value
    */
    public String getTypeValue() {
       return typeValue;
    }
   /**
    * @param typeValue type_value
    */
    public void setTypeValue(String typeValue) {
       this.typeValue = typeValue;
    }
	
   /**
    * @return typeTips type_tips
    */
    public String getTypeTips() {
       return typeTips;
    }
   /**
    * @param typeTips type_tips
    */
    public void setTypeTips(String typeTips) {
       this.typeTips = typeTips;
    }
	
   /**
    * @return isDefault is_default
    */
    public Integer getIsDefault() {
       return isDefault;
    }
   /**
    * @param isDefault is_default
    */
    public void setIsDefault(Integer isDefault) {
       this.isDefault = isDefault;
    }

    public Integer getKddType() {
        return kddType;
    }

    public void setKddType(Integer kddType) {
        this.kddType = kddType;
    }
}