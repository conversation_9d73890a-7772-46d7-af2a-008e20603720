package com.kuaidizs.jxc.domain.cnprivacy;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.Date;

/***
 * program: kdzs-jxc
 * description: 菜鸟隐私统计
 * author: <PERSON><PERSON><PERSON><PERSON>@raycloud.com
 * create: 2020-08-05 19:34
 **/
public class CnPrivacyCount implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 商家ID
     */
    private String sellerId;

    /**
     * 隐私次数
     */
    private Long privacyCount;

    /**
     * 日期
     */
    @JSONField(format = "yyyy-MM-dd")
    private Date orderDate;

    public String getSellerId() {
        return sellerId;
    }

    public void setSellerId(String sellerId) {
        this.sellerId = sellerId;
    }

    public Long getPrivacyCount() {
        return privacyCount;
    }

    public void setPrivacyCount(Long privacyCount) {
        this.privacyCount = privacyCount;
    }

    public Date getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Date orderDate) {
        this.orderDate = orderDate;
    }
}
