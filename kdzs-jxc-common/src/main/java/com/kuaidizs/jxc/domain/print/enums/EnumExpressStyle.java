package com.kuaidizs.jxc.domain.print.enums;

/**
 * 面单样式
 *
 * <AUTHOR>
 * @date 2017/7/11
 */
public enum EnumExpressStyle {

    FREE(1, "自由"),

    RECOMMEND(2, "推荐"),

    FENG_HUA(3, "奉化"),

    LARGE(4, "大"),

    MODDLE(5, "中"),

    SMALL(6, "小"),

    TEN(7, "10孔"),

    ELEVEN(8, "11孔"),

    TWELVE(9, "12孔"),

    STANDARD(10, "标准"),

    NEW(11, "新"),

    VERTICAL(12, "竖版"),

    THREE_BLOCK(14, "三联"),

    OFFICIAL_THREE_BLOCK(15, "官方三联"),

    TWO_BLOCK(16, "二联"),

    BUSINESS(17, "商务"),

    TIMING(18, "计时达"),

    NEW_TWO_BLOCK(19, "新二联"),

    NEW_THREE_BLOCK(20, "新三联"),

    EXPRESS_PORTABLE(21, "快递便携式"),

    EXPRESS_STANDARD(22, "快运标准"),

    EXPRESS_THREE_BLOCK(23, "快运三联"),

    EXPRESS_ONE(45, "一联单"),

    EXPRESS_TWO_YZD(46, "圆准达"),

    WD_EXPRESS_ONE(47, "网点一联单"),

    /**
     * 蓝牙便携机一联单
     */
    BX_EXPRESS_ONE(50, "一联单"),
    /**
     * 蓝牙便携机三联单(实际为100*180，与小平台统一，未去修改名称)
     */
    BX_EXPRESS_THREE(51, "三联单");

    private int id;
    private String name;

    EnumExpressStyle(int id, String name) {
        this.id = id;
        this.name = name;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
