package com.kuaidizs.jxc.domain.item;

import java.io.Serializable;

/**
 * 商品，sku，字单信息
 *
 * <AUTHOR>
 * @since 2018/08/13
 */
public class ItemSkuInfo implements Serializable {
    private static final long serialVersionUID = 6883494124840821002L;
    /**
     * 商品id
     */
    private String numIid;
    /**
     * 规格id
     */
    private String skuId;

    /**
     * 子单号
     */
    private String oid;

    /**
     * 库存扣除数量
     */
    private Long num;

    /**
     * 卖家旗帜
     */
    private Long flag;

    /**
     * true:系统商品,false:淘宝平台商品
     */
    private Boolean sysItem;

    public String getNumIid() {
        return numIid;
    }

    public void setNumIid(String numIid) {
        this.numIid = numIid;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }


    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }


    public Long getNum() {
        return num;
    }

    public void setNum(Long num) {
        this.num = num;
    }

    public Long getFlag() {
        return flag;
    }

    public void setFlag(Long flag) {
        this.flag = flag;
    }

    public Boolean getSysItem() {
        return sysItem;
    }

    public void setSysItem(Boolean sysItem) {
        this.sysItem = sysItem;
    }
}
