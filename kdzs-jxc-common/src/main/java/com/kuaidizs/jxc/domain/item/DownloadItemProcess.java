package com.kuaidizs.jxc.domain.item;

import java.io.Serializable;

/**
 * Created by yuanying on 2016/8/17.
 */
public class DownloadItemProcess implements Serializable{
    /**
     * 总共的需要处理的商品的个数
     */
    private int totalDealItem;
    /**
     * 已经处理的商品个数
     */
    private int remainDealItem;
    /**
     * 处理的状态
     */
    private boolean status = true;
    /**
     * 信息
     */
    private String msg;

    public int getTotalDealItem() {
        return totalDealItem;
    }

    public void setTotalDealItem(int totalDealItem) {
        this.totalDealItem = totalDealItem;
    }

    public int getRemainDealItem() {
        return remainDealItem;
    }

    public void setRemainDealItem(int remainDealItem) {
        this.remainDealItem = remainDealItem;
    }

    public boolean isStatus() {
        return status;
    }

    public void setStatus(boolean status) {
        this.status = status;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }
}
