package com.kuaidizs.jxc.domain.log;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/26.
 * @time 10:36.
 */
@Data
public class QueryCostStatistics implements Serializable {

    /**
     *序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
    private String apiInterface;
    private Long taobaoId;
    private Long costTime;
    private String costTimeDetail;
    private Boolean rdsSearch;
    private String queryRequest;
    private Integer tradeCount;
    private String attrJson;
    private Date created;

}
