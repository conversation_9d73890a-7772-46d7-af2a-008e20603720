package com.kuaidizs.jxc.domain.agent;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 代理商品绑定关系
 *
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AgentGoodsBindRel.java
 * @createTime 2022年12月13日 10:56:00
 */
@Data
public class AgentGoodsBindRel implements Serializable {
	/**
	 * 主键
	 */
	private Long id;

	/**
	 * 创建时间
	 */
	private Date created;

	/**
	 * 修改时间
	 */
	private Date modified;

	/**
	 * 用户id
	 */
	private String userId;

	/**
	 * 商品id
	 */
	private String goodsId;

	/**
	 * 商品名称
	 */
	private String goodsName;

	/**
	 * 上架状态（根据sku是否有销售转态决定 0 下架 1 上架）
	 */
	private Integer saleStatus;

	/**
	 * 商品图片地址
	 */
	private String imgUrl;
}
