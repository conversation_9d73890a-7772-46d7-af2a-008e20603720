package com.kuaidizs.jxc.domain.cjdf;

import com.kuaidizs.jxc.request.cjdf.AdvancedSetRequest;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * Date    2022-03-02
 */
@Data
public class CjdfAdvancedSet implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long taobaoId;
    /**
     * 主键
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date modifyTime;
    /**
     * 逻辑删除 1：可用 0 不可用
     */
    private Integer enableStatus;
    /**
     * 隐藏运单号回传订单 1：隐藏 0：不隐藏
     */
    private Integer hideReturnTrade;
    /**
     * 每页显示设置
     */
    private Integer pageSizeSet;
    /**
     * 订单排序 1：按订单付款时间，先付款的在前边 2：按订单付款时间，后付款的在前边 3：将相同商品挨在一起，数量由多到少
     */
    private Integer tradeSortType;

    /**
     * AdvancedSetRequest 转 CjdfAdvancedSet
     *
     * <AUTHOR>
     * @date 2022/3/2 8:25 下午
     * @param req
     * @param taobaoId
     * @return com.kuaidizs.jxc.domain.cjdf.CjdfAdvancedSet
     */
    public static CjdfAdvancedSet req2Po(AdvancedSetRequest req, Long taobaoId) {
        CjdfAdvancedSet advancedSet = new CjdfAdvancedSet();
        advancedSet.setTaobaoId(taobaoId);
        if (req == null) {
            return advancedSet;
        }
        if (req.getHideReturnTrade() != null) {
            advancedSet.setHideReturnTrade(req.getHideReturnTrade() ? 1 : 0);
        }
        advancedSet.setPageSizeSet(req.getPageSizeSet());
        advancedSet.setTradeSortType(req.getTradeSortType());
        return advancedSet;
    }

}