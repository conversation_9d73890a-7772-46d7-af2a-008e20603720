package com.kuaidizs.jxc.domain.print;

import java.util.*;
import java.io.Serializable;

/**
 *
 * <AUTHOR> @date    2016-07-26
 */
public class ModeInputproCommon implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 自增ID
     */
    private Integer id;
	/**
     * 创建时间
     */
    private Date created;
	/**
     * 修改时间
     */
    private Date modified;
	/**
     * 逻辑状态
     */
    private Boolean enableStatus;
	/**
     * Mode_input的主键ID
     */
    private Integer inputId;
	/**
     * kdd/fhd
     */
    private String inputType;
	/**
     * mode_listshow表的主键
     */
    private Integer modeListShowKddId;
	/**
     * mode_listshow表的主键
     */
    private Integer modeListShowFhdId;
	/**
     * data标签
     */
    private String dataName;
	/**
     * 前文字
     */
    private String wordLeft;
	/**
     * 后文字
     */
    private String wordRight;
	/**
     * 数据项,串 例如:tit＝宝贝，rgW＝490，dq＝1
     */
    private String proValue;
	/**
     * 状态
     */
    private Integer status;

	
   /**
    * @return id 自增ID
    */
    public Integer getId() {
       return id;
    }
   /**
    * @param id 自增ID
    */
    public void setId(Integer id) {
       this.id = id;
    }
	
   /**
    * @return created 创建时间
    */
    public Date getCreated() {
       return created;
    }
   /**
    * @param created 创建时间
    */
    public void setCreated(Date created) {
       this.created = created;
    }
	
   /**
    * @return modified 修改时间
    */
    public Date getModified() {
       return modified;
    }
   /**
    * @param modified 修改时间
    */
    public void setModified(Date modified) {
       this.modified = modified;
    }
	
   /**
    * @return enableStatus 逻辑状态
    */
    public Boolean getEnableStatus() {
       return enableStatus;
    }
   /**
    * @param enableStatus 逻辑状态
    */
    public void setEnableStatus(Boolean enableStatus) {
       this.enableStatus = enableStatus;
    }
	
   /**
    * @return inputId Mode_input的主键ID
    */
    public Integer getInputId() {
       return inputId;
    }
   /**
    * @param inputId Mode_input的主键ID
    */
    public void setInputId(Integer inputId) {
       this.inputId = inputId;
    }
	
   /**
    * @return inputType kdd/fhd
    */
    public String getInputType() {
       return inputType;
    }
   /**
    * @param inputType kdd/fhd
    */
    public void setInputType(String inputType) {
       this.inputType = inputType;
    }
	
   /**
    * @return modeListShowKddId mode_listshow表的主键
    */
    public Integer getModeListShowKddId() {
       return modeListShowKddId;
    }
   /**
    * @param modeListShowKddId mode_listshow表的主键
    */
    public void setModeListShowKddId(Integer modeListShowKddId) {
       this.modeListShowKddId = modeListShowKddId;
    }
	
   /**
    * @return modeListShowFhdId mode_listshow表的主键
    */
    public Integer getModeListShowFhdId() {
       return modeListShowFhdId;
    }
   /**
    * @param modeListShowFhdId mode_listshow表的主键
    */
    public void setModeListShowFhdId(Integer modeListShowFhdId) {
       this.modeListShowFhdId = modeListShowFhdId;
    }
	
   /**
    * @return dataName data标签
    */
    public String getDataName() {
       return dataName;
    }
   /**
    * @param dataName data标签
    */
    public void setDataName(String dataName) {
       this.dataName = dataName;
    }
	
   /**
    * @return wordLeft 前文字
    */
    public String getWordLeft() {
       return wordLeft;
    }
   /**
    * @param wordLeft 前文字
    */
    public void setWordLeft(String wordLeft) {
       this.wordLeft = wordLeft;
    }
	
   /**
    * @return wordRight 后文字
    */
    public String getWordRight() {
       return wordRight;
    }
   /**
    * @param wordRight 后文字
    */
    public void setWordRight(String wordRight) {
       this.wordRight = wordRight;
    }
	
   /**
    * @return proValue 数据项,串 例如:tit＝宝贝，rgW＝490，dq＝1
    */
    public String getProValue() {
       return proValue;
    }
   /**
    * @param proValue 数据项,串 例如:tit＝宝贝，rgW＝490，dq＝1
    */
    public void setProValue(String proValue) {
       this.proValue = proValue;
    }
	
   /**
    * @return status 状态
    */
    public Integer getStatus() {
       return status;
    }
   /**
    * @param status 状态
    */
    public void setStatus(Integer status) {
       this.status = status;
    }

}