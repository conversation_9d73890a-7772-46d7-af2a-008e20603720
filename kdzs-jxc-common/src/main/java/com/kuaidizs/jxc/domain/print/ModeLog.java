package com.kuaidizs.jxc.domain.print;

import java.util.*;
import java.io.Serializable;

/**
 *
 * <AUTHOR> @date    2016-09-22
 */
public class ModeLog implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 模版日志表主键
     */
    private Long id;
	/**
     * 主帐号id
     */
    private Long taobaoId;
	/**
     * 子帐号id
     */
    private Long subTaobaoId;
	/**
     * 请求报文
     */
    private String requestBody;
	/**
     * 响应报文
     */
    private String responseBody;
	/**
     * 日志的标识，比如方法名
     */
    private String logType;
	/**
     * 冗余文本字段
     */
    private String redunFieldText;
	/**
     * 冗余字符串字段
     */
    private String redunFieldString;
	/**
     * 添加时间
     */
    private Date created;

	
   /**
    * @return id 模版日志表主键
    */
    public Long getId() {
       return id;
    }
   /**
    * @param id 模版日志表主键
    */
    public void setId(Long id) {
       this.id = id;
    }
	
   /**
    * @return taobaoId 主帐号id
    */
    public Long getTaobaoId() {
       return taobaoId;
    }
   /**
    * @param taobaoId 主帐号id
    */
    public void setTaobaoId(Long taobaoId) {
       this.taobaoId = taobaoId;
    }
	
   /**
    * @return subTaobaoId 子帐号id
    */
    public Long getSubTaobaoId() {
       return subTaobaoId;
    }
   /**
    * @param subTaobaoId 子帐号id
    */
    public void setSubTaobaoId(Long subTaobaoId) {
       this.subTaobaoId = subTaobaoId;
    }
	
   /**
    * @return requestBody 请求报文
    */
    public String getRequestBody() {
       return requestBody;
    }
   /**
    * @param requestBody 请求报文
    */
    public void setRequestBody(String requestBody) {
       this.requestBody = requestBody;
    }
	
   /**
    * @return responseBody 响应报文
    */
    public String getResponseBody() {
       return responseBody;
    }
   /**
    * @param responseBody 响应报文
    */
    public void setResponseBody(String responseBody) {
       this.responseBody = responseBody;
    }
	
   /**
    * @return logType 日志的标识，比如方法名
    */
    public String getLogType() {
       return logType;
    }
   /**
    * @param logType 日志的标识，比如方法名
    */
    public void setLogType(String logType) {
       this.logType = logType;
    }
	
   /**
    * @return redunFieldText 冗余文本字段
    */
    public String getRedunFieldText() {
       return redunFieldText;
    }
   /**
    * @param redunFieldText 冗余文本字段
    */
    public void setRedunFieldText(String redunFieldText) {
       this.redunFieldText = redunFieldText;
    }
	
   /**
    * @return redunFieldString 冗余字符串字段
    */
    public String getRedunFieldString() {
       return redunFieldString;
    }
   /**
    * @param redunFieldString 冗余字符串字段
    */
    public void setRedunFieldString(String redunFieldString) {
       this.redunFieldString = redunFieldString;
    }
	
   /**
    * @return created 添加时间
    */
    public Date getCreated() {
       return created;
    }
   /**
    * @param created 添加时间
    */
    public void setCreated(Date created) {
       this.created = created;
    }

}