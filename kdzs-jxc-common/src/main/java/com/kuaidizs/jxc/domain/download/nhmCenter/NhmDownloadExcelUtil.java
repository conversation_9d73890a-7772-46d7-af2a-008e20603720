package com.kuaidizs.jxc.domain.download.nhmCenter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/4/11.
 * @time 13:57.
 */
public class NhmDownloadExcelUtil {
    /**
     * 拿货码下载
     * @param maps
     * @param columns
     * @param hasXuhao 是否包含序号
     * @return
     * @throws Exception
     */
    public static byte[] exportNhmSheet(LinkedList<LinkedHashMap<String, String>> maps, String[] columns, Boolean hasXuhao) throws Exception {
        List<String[]> columnNames = new ArrayList<String[]>();
        // 自定义列名
        columnNames.add(columns);
        //List<String[]> fieldNames = new ArrayList<String[]>();
        // 列名对应的字段名
        //fieldNames.add(fields);
        LinkedHashMap<String, LinkedList<LinkedHashMap<String, String>>> dataMap = new LinkedHashMap<>();
        dataMap.put("拿货码模版", maps);
        ExcelExport.ExcelExportData setInfo = new ExcelExport.ExcelExportData();
        setInfo.setDataMapByMap(dataMap);
        //setInfo.setFieldNames(fieldNames);
        setInfo.setColumnNames(columnNames);
        setInfo.setHasXuhao(hasXuhao);
        return ExcelExport.export2FileBytesByMap(setInfo);
    }
}
