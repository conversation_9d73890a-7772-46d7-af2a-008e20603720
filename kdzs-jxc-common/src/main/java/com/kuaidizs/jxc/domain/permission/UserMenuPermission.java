package com.kuaidizs.jxc.domain.permission;

import java.util.*;
import java.io.Serializable;

/**
 *
 * <AUTHOR> @date    2016-08-26
 */
public class UserMenuPermission implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 自增ID
     */
    private Long id;
	/**
     * 用户Id
     */
    private Long taobaoId;
	/**
     * 菜单ID
     */
    private Long menuId;
	/**
     * menu_key
     */
    private String menuKey;
	/**
     * 添加时间
     */
    private Date created;
	/**
     * 更新时间
     */
    private Date modified;
	/**
     * 逻辑删除
     */
    private Integer enableStatus;

	
   /**
    * @return id 自增ID
    */
    public Long getId() {
       return id;
    }
   /**
    * @param id 自增ID
    */
    public void setId(Long id) {
       this.id = id;
    }
	
   /**
    * @return taobaoId 用户Id
    */
    public Long getTaobaoId() {
       return taobaoId;
    }
   /**
    * @param taobaoId 用户Id
    */
    public void setTaobaoId(Long taobaoId) {
       this.taobaoId = taobaoId;
    }
	
   /**
    * @return menuId 菜单ID
    */
    public Long getMenuId() {
       return menuId;
    }
   /**
    * @param menuId 菜单ID
    */
    public void setMenuId(Long menuId) {
       this.menuId = menuId;
    }
	
   /**
    * @return menuKey menu_key
    */
    public String getMenuKey() {
       return menuKey;
    }
   /**
    * @param menuKey menu_key
    */
    public void setMenuKey(String menuKey) {
       this.menuKey = menuKey;
    }
	
   /**
    * @return created 添加时间
    */
    public Date getCreated() {
       return created;
    }
   /**
    * @param created 添加时间
    */
    public void setCreated(Date created) {
       this.created = created;
    }
	
   /**
    * @return modified 更新时间
    */
    public Date getModified() {
       return modified;
    }
   /**
    * @param modified 更新时间
    */
    public void setModified(Date modified) {
       this.modified = modified;
    }
	
   /**
    * @return enableStatus 逻辑删除
    */
    public Integer getEnableStatus() {
       return enableStatus;
    }
   /**
    * @param enableStatus 逻辑删除
    */
    public void setEnableStatus(Integer enableStatus) {
       this.enableStatus = enableStatus;
    }

}