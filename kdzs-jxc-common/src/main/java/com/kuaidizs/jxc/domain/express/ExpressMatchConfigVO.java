package com.kuaidizs.jxc.domain.express;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @auther xudaomeng
 * @since 2020-11-11 20:23
 */
public class ExpressMatchConfigVO implements Serializable {

    private static final long serialVersionUID = 4376631918183411955L;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 默认的快递code
     */
    private String defaultExCode;

    /**
     * 默认的快递名称
     */
    private String defaultExName;

    /**
     * 开关和优先级配置("type":"1"：按卖家备注推荐快递 2：按订单旗分配快递 3：按买家留言分配快递 4：按订单中宝贝分配快递 5：按收件地区分配快递 7：根据地址可达推荐快递  8：根据订单预估配送时间推荐快递
     * "switch":true,false
     * "level":1,2,3,4,5,7,8)
     */
    private String config;

    /**
     * 根据备注中的快递名称匹配快递
     */
    private Integer matchByExpressName;

    /**
     * 根据备注中的关键字匹配快递
     */
    private Integer matchByKeyWord;

    /**
     * 关键字对应的快递code
     */
    private String wordExCode;

    /**
     * 关键字对应的快递mingc
     */
    private String wordExName;

    /**
     * 旗帜及其对应的快递
     */
    private String flag;

    /**
     * 根据地址可达推荐快递
     * [{"exCode":"YUNDA","exName":"韵达快递","level":1,"exId":"123"}]
     */
    private String kdReachableSuggest;

    /**
     * 宝贝及其对应的快递
     */
    private List<GoodsExpressVO> goods;

    /**
     * 地址及其对应的快递
     */
    private List<AddressExpressVO> addresses;

    /**
     * 宝贝重量及其对应的快递
     */
    private List<GoodsWeightExpresssVO> weights;

    /**
     * 实付金额及其对应的快递
     */
    private List<PaymentExpressVO> payments;

    /**
     * 智选物流开关
     */
    private Integer expressSwitch;

    /**
     * 智选物流开启关闭时间
     */
    private Date expressOnOffTime;

    /**
     * 平台类型
     */
    private String ptType;

    private Date created;

    private Date modified;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getDefaultExCode() {
        return defaultExCode;
    }

    public void setDefaultExCode(String defaultExCode) {
        this.defaultExCode = defaultExCode;
    }

    public String getDefaultExName() {
        return defaultExName;
    }

    public void setDefaultExName(String defaultExName) {
        this.defaultExName = defaultExName;
    }

    public String getConfig() {
        return config;
    }

    public void setConfig(String config) {
        this.config = config;
    }

    public Integer getMatchByExpressName() {
        return matchByExpressName;
    }

    public void setMatchByExpressName(Integer matchByExpressName) {
        this.matchByExpressName = matchByExpressName;
    }

    public Integer getMatchByKeyWord() {
        return matchByKeyWord;
    }

    public void setMatchByKeyWord(Integer matchByKeyWord) {
        this.matchByKeyWord = matchByKeyWord;
    }

    public String getWordExCode() {
        return wordExCode;
    }

    public void setWordExCode(String wordExCode) {
        this.wordExCode = wordExCode;
    }

    public String getWordExName() {
        return wordExName;
    }

    public void setWordExName(String wordExName) {
        this.wordExName = wordExName;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public String getKdReachableSuggest() {
        return kdReachableSuggest;
    }

    public void setKdReachableSuggest(String kdReachableSuggest) {
        this.kdReachableSuggest = kdReachableSuggest;
    }

    public List<GoodsExpressVO> getGoods() {
        return goods;
    }

    public void setGoods(List<GoodsExpressVO> goods) {
        this.goods = goods;
    }

    public List<AddressExpressVO> getAddresses() {
        return addresses;
    }

    public void setAddresses(List<AddressExpressVO> addresses) {
        this.addresses = addresses;
    }

    public String getPtType() {
        return ptType;
    }

    public void setPtType(String ptType) {
        this.ptType = ptType;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public Integer getExpressSwitch() {
        return expressSwitch;
    }

    public void setExpressSwitch(Integer expressSwitch) {
        this.expressSwitch = expressSwitch;
    }

    public Date getExpressOnOffTime() {
        return expressOnOffTime;
    }

    public void setExpressOnOffTime(Date expressOnOffTime) {
        this.expressOnOffTime = expressOnOffTime;
    }

    public List<GoodsWeightExpresssVO> getWeights() {
        return weights;
    }

    public void setWeights(List<GoodsWeightExpresssVO> weights) {
        this.weights = weights;
    }

    public List<PaymentExpressVO> getPayments() {
        return payments;
    }

    public void setPayments(List<PaymentExpressVO> payments) {
        this.payments = payments;
    }
}
