package com.kuaidizs.jxc.domain.print;

import java.io.Serializable;
import java.util.Date;

/**
 * 模版备份表信息
 *
 * <AUTHOR>    Date:2016/11/28  16:08
 */
public class ModeBakCode implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * 逻辑删除状态0表示删除 1表示未删除
     */
    private Boolean enableStatus;
    /**
     * 淘宝id
     */
    private Long taobaoId;
    /**
     * 快递单还是发货单 kdd/fhd
     */
    private String modeId;
    /**
     * 备份code码
     */
    private String code;


    /**
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return enableStatus 逻辑删除状态0表示删除 1表示未删除
     */
    public Boolean getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 逻辑删除状态0表示删除 1表示未删除
     */
    public void setEnableStatus(Boolean enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return taobaoId 淘宝id
     */
    public Long getTaobaoId() {
        return taobaoId;
    }

    /**
     * @param taobaoId 淘宝id
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return modeId 快递单还是发货单 kdd/fhd
     */
    public String getModeId() {
        return modeId;
    }

    /**
     * @param modeId 快递单还是发货单 kdd/fhd
     */
    public void setModeId(String modeId) {
        this.modeId = modeId;
    }

    /**
     * @return code 备份code码
     */
    public String getCode() {
        return code;
    }

    /**
     * @param code 备份code码
     */
    public void setCode(String code) {
        this.code = code;
    }

}
