package com.kuaidizs.jxc.domain.item;

import com.kuaidizs.jxc.common.enums.ItemVersionEnum;

import java.io.Serializable;

/***
 * program: kdzs-jxc
 * description: 
 * author: z<PERSON><PERSON><PERSON>@raycloud.com
 * create: 2021-08-31 13:52
 **/
public class ItemVersionInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    private ItemVersionEnum itemVersionEnum;

    private Boolean sync=Boolean.FALSE;

    public ItemVersionEnum getItemVersionEnum() {
        return itemVersionEnum;
    }

    public void setItemVersionEnum(ItemVersionEnum itemVersionEnum) {
        this.itemVersionEnum = itemVersionEnum;
    }

    public Boolean getSync() {
        return sync;
    }

    public void setSync(Boolean sync) {
        this.sync = sync;
    }
}
