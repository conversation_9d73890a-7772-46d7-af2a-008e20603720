package com.kuaidizs.jxc.domain.log.opera;

import com.alibaba.fastjson.JSONObject;
import com.kuaidizs.jxc.domain.log.OperationLinkLog;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class QueryCondition {
//    private String root;
    private String tradeRuleId; // 高级查询规则id ok
    private String customType; // ok
    private String startTime;
    private String endTime;
    private String timeType;
    private String status; // ok
//    private String taobaoIds;
    private String addrRule;
    private String stockType; // 库存类型
//    private String pageNo;
//    private String pageSize;
//    private String random;
    private String buyerMessage;  // ok
    private String buyerNick; // ok
    private String buyerOpenUid;    // ok
//    private String buyerPhone;
//    private String colorIncluding;
//    private String colorNotIncluding;
    private String flagValue; // ok
    private String goodsTotalNum; // ok
    private String goodsTypeNum;    // ok
    private String payment;
    private String printStatus; // ok
    private String receiverName;
    private String refundStatus; // ok
    private String sellerMemo;
//    private String shortNameIncluding;
//    private String shortNameNotIncluding;
//    private String itemShortTitleIncluding;
//    private String itemShortTitleNotIncluding;
//    private String sid;
//    private String skuIncluding;
//    private String skuNotIncluding;
    private String tid;
    private String tradeNum; // ok
    private String type; // ok
    private String tradeWeight; // ok
//    private String weightRange;
//    private String fuzzySearch;
//    private String partSearch;
    private String market;
    private String stall;
//    private String apiName;
//    private String pageRand;
    private String queryTime;
    private String abortTime; // 载单时间
    private String exceedTime; // 付款时间
    private List<String> userIds;
    private String tradeStatus;
    private Map<String, Object> addressRule;
    private String addrName;
    private MessageOrMemo messageOrMemo;
    private String tradeRuleString;
//    private String showDaifaTrade;
    private String daiFaFactoryName;
//    private String esomOrderPushStatus;
//    private String numIid;
//    private String numIidMergeOuterIid;
//    private String groupFlag;
    private String labelId;
//    private String sellAttribute;
//    private String surplusSendTimeType;
//    private String remainingTimeValue;
//    private String sellerMemoNotInclude;
//    private String tradeFrom;
//    private String flagValueStr; // ?
    private String systemTimeStamp;
    private String itemFields;
    private String tradeSource;
    private String memberLabel;
    private String remainDeliveryTime;
    private String daifaQueyType;
    private String yishenPushStatus;

    @Data
    public static class MessageOrMemo {
        private Integer includeType;
        private String itemKeyword;
        private String shortTitle;
        private String skuKeyword;
        private String color;
        private List<String> flagList;
    }

    @Data
    public static class ItemInfo {
        private Integer includeType;
        private String itemKeyword;
        private String shortTitle;
        private String skuKeyword;
        private String color;
    }

    private ItemInfo itemInfo;
    private String itemCategory;
    private String itemNum;
    private String buyerMobile;
    private String tradeAmount;
    private String kdInfo;
    private List<String> receiverNames;
    private String tradeType;
    private String preciseQueryStatus;
    private Integer quickQueryLimitStatus;
    private String quickQuery; // 快捷查询
    private String salesAttribute; // 销售属性

    public static void main(String[] args) {
        // 配置FastJSON保留null值字段
        JSONObject.DEFFAULT_DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";
        JSONObject.DEFAULT_GENERATE_FEATURE |= com.alibaba.fastjson.serializer.SerializerFeature.WriteMapNullValue.getMask();

        String operationLinkLog = "{\"userId\":1893301994,\"userName\":\"sanchephoto\",\"queryCondition\":\"{\\\"root\\\":\\\"printBatch\\\",\\\"customType\\\":\\\"tradeRuleString\\\",\\\"tradeRuleString\\\":{\\\"buyerNick\\\":\\\"\\\",\\\"buyerOpenUid\\\":\\\"\\\",\\\"fuzzySearch\\\":true,\\\"filterByTrade\\\":true,\\\"goodsTotalNum\\\":\\\"\\\",\\\"goodsTypeNum\\\":\\\"\\\",\\\"tradeNum\\\":\\\"\\\",\\\"tradeWeight\\\":\\\"\\\",\\\"goodsRuleList\\\":[{\\\"color\\\":\\\"颜色\\\",\\\"shortName\\\":\\\"宝贝名称\\\",\\\"includeType\\\":\\\"1\\\",\\\"itemShortTitle\\\":\\\"简称\\\",\\\"sku\\\":\\\"规格型号\\\"},{\\\"color\\\":\\\"颜色\\\",\\\"shortName\\\":\\\"宝贝名称2\\\",\\\"includeType\\\":\\\"1\\\",\\\"itemShortTitle\\\":\\\"川普\\\",\\\"sku\\\":\\\"规格型号2\\\"}],\\\"printStatus\\\":\\\"\\\",\\\"refundStatus\\\":\\\"\\\",\\\"flagValue\\\":\\\"\\\",\\\"msgMemoValue\\\":\\\"\\\",\\\"buyerMessage\\\":\\\"\\\",\\\"sellerMemo\\\":\\\"\\\",\\\"receiverAddressList\\\":[{\\\"state\\\":\\\"浙江省\\\",\\\"city\\\":\\\"杭州市\\\"},{\\\"state\\\":\\\"浙江省\\\",\\\"city\\\":\\\"宁波市\\\"},{\\\"state\\\":\\\"浙江省\\\",\\\"city\\\":\\\"温州市\\\"},{\\\"state\\\":\\\"浙江省\\\",\\\"city\\\":\\\"嘉兴市\\\"},{\\\"state\\\":\\\"浙江省\\\",\\\"city\\\":\\\"湖州市\\\"},{\\\"state\\\":\\\"浙江省\\\",\\\"city\\\":\\\"绍兴市\\\"},{\\\"state\\\":\\\"浙江省\\\",\\\"city\\\":\\\"金华市\\\"},{\\\"state\\\":\\\"浙江省\\\",\\\"city\\\":\\\"衢州市\\\"},{\\\"state\\\":\\\"浙江省\\\",\\\"city\\\":\\\"舟山市\\\"},{\\\"state\\\":\\\"浙江省\\\",\\\"city\\\":\\\"台州市\\\"},{\\\"state\\\":\\\"浙江省\\\",\\\"city\\\":\\\"丽水市\\\"}],\\\"abortTime\\\":\\\"03:04:05\\\",\\\"abortTimeDay\\\":\\\"0\\\",\\\"exceedTime\\\":\\\"\\\",\\\"startTime\\\":\\\"2025-02-21 00:00:00\\\",\\\"endTime\\\":\\\"2025-03-22 23:59:59\\\",\\\"payment\\\":\\\"\\\",\\\"type\\\":\\\"\\\",\\\"status\\\":\\\"all\\\",\\\"market\\\":\\\"\\\",\\\"stall\\\":\\\"\\\",\\\"kdName\\\":\\\"\\\",\\\"addressType\\\":\\\"1\\\",\\\"areaJson\\\":\\\"[{\\\\\\\"name\\\\\\\":\\\\\\\"浙江省\\\\\\\",\\\\\\\"childList\\\\\\\":[{\\\\\\\"name\\\\\\\":\\\\\\\"杭州市\\\\\\\",\\\\\\\"childList\\\\\\\":[{\\\\\\\"name\\\\\\\":\\\\\\\"滨江区\\\\\\\"}]}]}]\\\",\\\"areaContain\\\":\\\"1\\\",\\\"addrRule\\\":\\\"0_contains_滨江区\\\",\\\"addrRuleId\\\":\\\"4922588\\\"}}\",\"selectedOrderCount\":0,\"sortCondition\":\"{\\\"sortLatitude\\\":0,\\\"sortMode\\\":{\\\"relationShopOrderSortType\\\":0,\\\"useCustomSort\\\":0,\\\"giftNumId\\\":\\\"111,222\\\",\\\"fhdSortType\\\":\\\"3\\\",\\\"customSortJson\\\":\\\"{\\\\\\\"attributeDimension\\\\\\\":1,\\\\\\\"marketStallDimension\\\\\\\":0,\\\\\\\"productDimension\\\\\\\":2,\\\\\\\"skuDimension\\\\\\\":1}\\\",\\\"customSortRemark\\\":\\\"相同商家编码-相同规格ID-数量多的在前\\\",\\\"kddSortType\\\":\\\"2\\\",\\\"isUserFilter\\\":1,\\\"orderSortType\\\":21,\\\"_lastModified\\\":\\\"2025/03/22 13:13:04\\\",\\\"marketStallSort\\\":0,\\\"itemSort\\\":2,\\\"skuSort\\\":1,\\\"attributeSort\\\":1,\\\"type\\\":21,\\\"gifts\\\":\\\"111,222\\\"}}\"}";

        // 先解析外层JSON
        OperationLinkLog log = JSONObject.parseObject(operationLinkLog, OperationLinkLog.class);
        
        /*// 再解析queryCondition字段，使用JSONObject而不是直接解析为QueryCondition
        JSONObject queryConditionObj = JSONObject.parseObject(log.getQueryCondition());
        
        // 获取tradeRuleString对象
        Object tradeRuleObj = queryConditionObj.get("tradeRuleString");
        if (tradeRuleObj != null) {
            // 将tradeRuleString转换为TradeRuleString对象
            TradeRuleString tradeRule = JSONObject.parseObject(tradeRuleObj.toString(), TradeRuleString.class);
            queryConditionObj.put("tradeRuleString", tradeRule);
        }*/

        QueryCondition tradeRule = JSONObject.parseObject(log.getQueryCondition(), QueryCondition.class);
        TradeRuleString tradeRuleString = JSONObject.parseObject(tradeRule.getTradeRuleString(), TradeRuleString.class);
        System.out.println("json前condition:" + JSONObject.toJSONString(tradeRuleString));


        // 最后将整个对象转换为QueryCondition
//        QueryCondition queryCondition = JSONObject.parseObject(queryConditionObj.toJSONString(), QueryCondition.class);
        
//        System.out.println("json前condition:" + JSONObject.toJSONString(queryCondition));
    }
}

