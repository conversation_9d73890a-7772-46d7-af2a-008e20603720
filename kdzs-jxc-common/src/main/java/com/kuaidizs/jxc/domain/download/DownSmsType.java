package com.kuaidizs.jxc.domain.download;

import com.kuaidizs.jxc.common.util.Constant;

import java.util.HashMap;
import java.util.Map;

/**
 * 短信发送类型
 */
public class DownSmsType {
    /**
     * 批打界面下载
     */
    public static final int BATCH_DOWNLOAD_TYPE = 1;
    /**
     * 下载中心下载
     */
    public static final int CENTER_DOWNLOAD_TYPE = 2;
    /**
     * 底单下载
     */
    public static final int BG_DOWNLOAD_TYPE = 3;
    /**
     * 打印记录
     */
    public static final int PRINT_LOG_DOWNLOAD_TYPE = 4;
    /**
     * 发货记录
     */
    public static final int FH_DOWNLOAD_TYPE = 5;
    /**
     * 回收记录
     */
    public static final int CANCLE_DOWNLOAD_TYPE = 6;
    /**
     * 简称下载
     */
    public static final int ITEM_DOWNLOAD_TYPE = 7;
    /**
     * 校验是否需要下载短信校验
     */
    public static final int DOWNLOAD_SWITCH_TYPE = 8;
    /**
     * 单号分享下载(目前为了系统内短信验证码统一可用，底单、打印、发货、简称、备货单(windows)、宝贝简称、手工单、快递对账)
     */
    public static final int DOWNLOAD_WAY_BILL_TYPE = 9;
    /**
     * 备货单下载
     */
    public static final int BHD_TYPE = 10;
    /**
     * 手工单下载
     */
    public static final int HAND_TYPE = 11;
    /**
     * 统一下载
     */
    public static final int DOWN_TYPE = 12;

    /**
     * 单号被分享下载
     */
    public static final int DOWNLOAD_TYPE_KD_ACCOUNT = 9;

    /**
     * 短信缓存前缀
     */
    public static final String DOWN_SMS_CACHE_PRE = Constant.APP_KEY + "_" + "DOWN_SMS_CACHE_PRE_";
    /**
     * 每日每用户下载发送短信上限
     */
    public static final int DOWN_SMS_TOTAL = 50;
    /**
     * 总次数缓存时间
     */
    public static final int TOTAL_TIME = 60 * 60 * 12;
    /**
     * 验证码缓存时间
     */
    public static final int CODE_TIME = 60 * 5;

    /**
     * 单号分享验证码缓存24小时
     */
    public static final int WAY_BILL_CODE_TIME = 60 * 60 * 24;

    public static final Map<Integer, String> types = new HashMap<>();

    static {
        types.put(BATCH_DOWNLOAD_TYPE, "订单记录");
        types.put(CENTER_DOWNLOAD_TYPE, "订单记录");
        types.put(BG_DOWNLOAD_TYPE, "底单记录");
        types.put(PRINT_LOG_DOWNLOAD_TYPE, "打印记录");
        types.put(FH_DOWNLOAD_TYPE, "发货记录");
        types.put(CANCLE_DOWNLOAD_TYPE, "回收记录");
        types.put(ITEM_DOWNLOAD_TYPE, "简称下载");
        types.put(DOWNLOAD_SWITCH_TYPE, "下载开关");
        types.put(BHD_TYPE, "备货单下载");
        types.put(HAND_TYPE, "手工单下载");
        types.put(DOWN_TYPE, "统一下载");
        types.put(DOWNLOAD_TYPE_KD_ACCOUNT, "统一下载");
    }
}
