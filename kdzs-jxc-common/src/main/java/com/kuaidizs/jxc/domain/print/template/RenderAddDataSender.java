package com.kuaidizs.jxc.domain.print.template;


import java.io.Serializable;

/**
 * 菜鸟指令渲染时的AddData数据结构 中的发件人信息
 */
public class RenderAddDataSender implements Serializable {

    private static final long serialVersionUID = -5625087023769103124L;

    /**
     * 发件人姓名
     */
    private String name;
    /**
     * 发件人手机号
     */
    private String mobile;
    /**
     * 发件人固定电话
     */
    private String phone;

    private RenderAddDataAddress address;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public RenderAddDataAddress getAddress() {
        return address;
    }

    public void setAddress(RenderAddDataAddress address) {
        this.address = address;
    }
}
