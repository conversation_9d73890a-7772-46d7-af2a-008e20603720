package com.kuaidizs.jxc.domain.print.enums;

/**
 * 快递类型
 * Created by lml on 2017/7/11.
 */
public enum EnumExpressType {

    /**
     * 普通面单
     */
    COMMON(1),

    /**
     * 网点面单
     */
    SITE(2),

    /**
     * 菜鸟面单
     */
    CAINIAO(3),

    /**
     * 发货单
     */
    DELIVER(4),

    /**
     * 京东面单
     */
    JD(5);

    EnumExpressType(int value) {
        this.value = value;
    }

    private int value;

    public int getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }

    public static EnumExpressType getByValue(int value) {
        for (EnumExpressType expressType : EnumExpressType.values()) {
            if (expressType.getValue() == value) {
                return expressType;
            }
        }
        return null;
    }
}
