package com.kuaidizs.jxc.domain.express;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 实付金额及其对应的快递
 *
 * <AUTHOR>
 * @date 2023/8/1 10:30 上午
 */
@Data
public class PaymentExpressVO implements Serializable {

    private static final long serialVersionUID = 4330240652678318883L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 实付金额小值
     */
    private BigDecimal paymentMin;

    /**
     * 实付金额大值
     */
    private BigDecimal paymentMax;

    /**
     * 快递code
     */
    private String paymentExName;

    /**
     * 快递名称
     */
    private String paymentExCode;

    /**
     * 指定区域对应快递
     */
    private List<PaymentRegionExpressVO> regionExpressVOS;
}
