package com.kuaidizs.jxc.domain.ai;

import com.kuaidizs.jxc.domain.sys.Page;
import java.util.List;
import lombok.Data;

@Data
public class AiPrintRecordQueryResult extends Page<AiPrintRecord> {

    /**
     * 按订单分类的统计数据
     */
    private List<AiPrintRecordStatistics> statistics;

    public static AiPrintRecordQueryResult emptyObj() {
        AiPrintRecordQueryResult result = new AiPrintRecordQueryResult();
        result.setTotal(0l);
        return result;
    }
}
