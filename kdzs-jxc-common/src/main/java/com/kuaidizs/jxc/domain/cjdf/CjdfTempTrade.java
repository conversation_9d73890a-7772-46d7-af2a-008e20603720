package com.kuaidizs.jxc.domain.cjdf;

import com.kuaidizs.jxc.common.util.DateUtil;
import com.kuaidizs.jxc.vo.cjdf.TempTradeVO;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * Date    2022-03-06
 */
@Data
public class CjdfTempTrade implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date modifyTime;
    /**
     * 逻辑删除 1：可用 0 不可用
     */
    private Integer enableStatus;
    /**
     * 用户ID
     */
    private Long taobaoId;
    /**
     * 随机数key
     */
    private String randomKey;
    /**
     * 订单编号
     */
    private String tid;
    /**
     * 支付时间
     */
    private Date payTime;
    /**
     * 回传状态 1：已回传 2：未回传
     */
    private Integer returnStatus;
    /**
     * 商品ID
     */
    private String auctionId;
    /**
     * 商品数量
     */
    private Integer quality;

    /**
     * TempTradeVO 转 CjdfTempTrade
     *
     * <AUTHOR>
     * @date 2022/3/6 10:17 上午
     * @param taobaoId
     * @param randomKey
     * @param vo
     * @return com.kuaidizs.jxc.domain.cjdf.CjdfTempTrade
     */
    public static CjdfTempTrade vo2Po(Long taobaoId, String randomKey, TempTradeVO vo) throws Exception {
        CjdfTempTrade trade = new CjdfTempTrade();
        if (taobaoId == null || StringUtils.isBlank(randomKey) || vo == null) {
            return trade;
        }
        Date now = new Date();
        trade.setCreateTime(now);
        trade.setModifyTime(now);
        trade.setEnableStatus(1);
        trade.setTaobaoId(taobaoId);
        trade.setRandomKey(randomKey);
        trade.setTid(vo.getTid());
        trade.setPayTime(DateUtil.convertToDate(vo.getPayTime()));
        trade.setReturnStatus(vo.getReturnStatus());
        trade.setAuctionId(vo.getAuctionId());
        trade.setQuality(vo.getQuality());
        return trade;
    }

    /**
     * List<TempTradeVO> 转 List<CjdfTempTrade>
     *
     * <AUTHOR>
     * @date 2022/3/6 10:22 上午
     * @param taobaoId
     * @param randomKey
     * @param vos
     * @return java.util.List<com.kuaidizs.jxc.domain.cjdf.CjdfTempTrade>
     */
    public static List<CjdfTempTrade> vo2Pos(Long taobaoId, String randomKey, List<TempTradeVO> vos) throws Exception {
        List<CjdfTempTrade> trades = new ArrayList<>();
        if (taobaoId == null || StringUtils.isBlank(randomKey) || CollectionUtils.isEmpty(vos)) {
            return trades;
        }
        for (TempTradeVO vo : vos) {
            trades.add(vo2Po(taobaoId, randomKey, vo));
        }
        return trades;
    }
}