package com.kuaidizs.jxc.domain.ai;

import com.kuaidizs.jxc.common.enums.ai.AiSortTradeCategoryEnum;
import java.util.List;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class AiSortTradeStatisticsOfCategory {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 订单同步的唯一标识
     */
    private String syncKey;
    /**
     * 分类
     */
    private AiSortTradeCategoryEnum category;
    /**
     * 数量
     */
    private Integer num;

    /**
     * 分类code
     */
    private String categoryCode;

    public void initFieldsBeforeInsert() {
        if (this.category != null) {
            this.categoryCode = this.category.name();
        }
    }

    public void initFieldsForResult() {
        if (StringUtils.isNotBlank(this.categoryCode)) {
            this.category = AiSortTradeCategoryEnum.getByName(this.categoryCode);
        }
    }
}
