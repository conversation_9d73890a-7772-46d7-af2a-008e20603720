package com.kuaidizs.jxc.domain.cjdf;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 厂家代发订单标识
 *
 * <AUTHOR>
 * @date 2022/5/19 5:53 下午
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CjdfOidSign implements Serializable {

    private static final long serialVersionUID = -7189051746512028687L;

    /**
     * 订单编号
     */
    private String oid;

    /**
     * 分配状态 2：已分配 3：取消分配 4：分配失败(不展示，enable_status置为0) 5：取消分配失败(不展示，enable_status置为0)
     */
    private String distributeStatus;
}
