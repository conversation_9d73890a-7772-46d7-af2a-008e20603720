package com.kuaidizs.jxc.domain.oaid;

import java.io.Serializable;
import java.util.Date;

/***
 * program:
 * description: 
 * author: <PERSON><PERSON><PERSON><PERSON>@raycloud.com
 * create: 2021-03-04 14:14
 **/
public class OaidDecryptResponse implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 说手机号
     */
    private String mobile;

    /**
     * 发件人
     */
    private String name;

    /**
     * 电话
     */
    private String phone;

    /**
     * 单纯的oaid
     */
    private String oaid;

    /**
     * 收货人国籍
     */
    private String country;

    /**
     * 收货人的所在省份
     */
    private String state;

    /**
     *收货人的所在城市
     */
    private String city;

    /**
     * 收货人的所在地区
     */
    private String district;

    /**
     * 收货人街道地址
     */
    private String town;

    /**
     * 详细地址
     */
    private String addressDetail;

    /**
     * oaid是否和tid当前的oaid匹配。true：匹配，false：不匹配。当不匹配时，建议通过taobao.trade.fullinfo.get获取最新的oaid。
     */
    private Boolean matched;

    /**
     * 交易编号
     */
    private String tid;

    private String requestId;

    /**
     * 标记订单是否为隐私保护订单，为true时，mobile为隐私号
     */
    private Boolean privacyProtection;

    /**
     * 隐私号过期时间，privacy_protection=true时有值
     */
    private Date secretNoExpireTime;

    /**
     * 淘宝id
     */
    private Long taobaoId;

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        this.oaid = oaid;
    }

    public String getAddressDetail() {
        return addressDetail;
    }

    public void setAddressDetail(String addressDetail) {
        this.addressDetail = addressDetail;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public Boolean getMatched() {
        return matched;
    }

    public void setMatched(Boolean matched) {
        this.matched = matched;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public Boolean getPrivacyProtection() {
        return privacyProtection;
    }

    public void setPrivacyProtection(Boolean privacyProtection) {
        this.privacyProtection = privacyProtection;
    }

    public Date getSecretNoExpireTime() {
        return secretNoExpireTime;
    }

    public void setSecretNoExpireTime(Date secretNoExpireTime) {
        this.secretNoExpireTime = secretNoExpireTime;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }
}
