package com.kuaidizs.jxc.domain.cnprivacy;

import java.util.*;
import java.io.Serializable;

/**
 * 菜鸟隐私面单的申请日志
 *
 * <AUTHOR>
 * Date    2020-08-05
 */
public class CnPrivacyLog implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    private Long id;
    /**
     * 订单所属的用户id
     */
    private Long taobaoId;
    /**
     * 快递id
     */
    private Integer kdId;
    /**
     * 快递code
     */
    private String kdCode;
    /**
     * 单号的归属者（单号是哪个帐号申请的）
     */
    private Long useTaobaoId;
    /**
     * 网点code
     */
    private String branchCode;
    /**
     * 订单号（包含子订单）
     */
    private String tids;
    /**
     * 申请隐私面单的订单号
     */
    private String useTid;
    /**
     * 快递单号
     */
    private String ydNo;
    /**
     * 包裹号
     */
    private Integer packageId;
    /**
     * 状态 0回收 1正常
     */
    private Integer status;
    /**
     * 平台类型
     */
    private Integer ptType;
    /**
     * 操作人对应的淘宝ID
     */
    private Long operatorId;
    /**
     * 操作人淘宝昵称，精确到子账号
     */
    private String operatorNick;
    /**
     * 逻辑删除0:删除，1:有效
     */
    private Integer enableStatus;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 更新时间
     */
    private Date modified;

    private String tableName;

    private String fkId;

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getFkId() {
        return fkId;
    }

    public void setFkId(String fkId) {
        this.fkId = fkId;
    }

    /**
     * @return id 自增id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id 自增id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return taobaoId 订单所属的用户id
     */
    public Long getTaobaoId() {
        return taobaoId;
    }

    /**
     * @param taobaoId 订单所属的用户id
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return kdId 快递id
     */
    public Integer getKdId() {
        return kdId;
    }

    /**
     * @param kdId 快递id
     */
    public void setKdId(Integer kdId) {
        this.kdId = kdId;
    }

    /**
     * @return kdCode 快递code
     */
    public String getKdCode() {
        return kdCode;
    }

    /**
     * @param kdCode 快递code
     */
    public void setKdCode(String kdCode) {
        this.kdCode = kdCode;
    }

    /**
     * @return useTaobaoId 单号的归属者（单号是哪个帐号申请的）
     */
    public Long getUseTaobaoId() {
        return useTaobaoId;
    }

    /**
     * @param useTaobaoId 单号的归属者（单号是哪个帐号申请的）
     */
    public void setUseTaobaoId(Long useTaobaoId) {
        this.useTaobaoId = useTaobaoId;
    }

    /**
     * @return branchCode 网点code
     */
    public String getBranchCode() {
        return branchCode;
    }

    /**
     * @param branchCode 网点code
     */
    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    /**
     * @return tids 订单号（包含子订单）
     */
    public String getTids() {
        return tids;
    }

    /**
     * @param tids 订单号（包含子订单）
     */
    public void setTids(String tids) {
        this.tids = tids;
    }

    /**
     * @return useTid 申请隐私面单的订单号
     */
    public String getUseTid() {
        return useTid;
    }

    /**
     * @param useTid 申请隐私面单的订单号
     */
    public void setUseTid(String useTid) {
        this.useTid = useTid;
    }

    /**
     * @return ydNo 快递单号
     */
    public String getYdNo() {
        return ydNo;
    }

    /**
     * @param ydNo 快递单号
     */
    public void setYdNo(String ydNo) {
        this.ydNo = ydNo;
    }

    /**
     * @return packageId 包裹号
     */
    public Integer getPackageId() {
        return packageId;
    }

    /**
     * @param packageId 包裹号
     */
    public void setPackageId(Integer packageId) {
        this.packageId = packageId;
    }

    /**
     * @return status 状态 0回收 1正常
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * @param status 状态 0回收 1正常
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * @return ptType 平台类型
     */
    public Integer getPtType() {
        return ptType;
    }

    /**
     * @param ptType 平台类型
     */
    public void setPtType(Integer ptType) {
        this.ptType = ptType;
    }

    /**
     * @return operatorId 操作人对应的淘宝ID
     */
    public Long getOperatorId() {
        return operatorId;
    }

    /**
     * @param operatorId 操作人对应的淘宝ID
     */
    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    /**
     * @return operatorNick 操作人淘宝昵称，精确到子账号
     */
    public String getOperatorNick() {
        return operatorNick;
    }

    /**
     * @param operatorNick 操作人淘宝昵称，精确到子账号
     */
    public void setOperatorNick(String operatorNick) {
        this.operatorNick = operatorNick;
    }

    /**
     * @return enableStatus 逻辑删除0:删除，1:有效
     */
    public Integer getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 逻辑删除0:删除，1:有效
     */
    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 更新时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 更新时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

}