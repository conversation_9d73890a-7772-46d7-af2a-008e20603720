package com.kuaidizs.jxc.domain.invoiceApply;

import java.io.Serializable;
import java.util.Date;

public class InvoiceApply implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;


    private String tableName;

    private String fkId;

    /**
     * id
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 订单编号
     */
    private String tid;

    /**
     * 买家抬头
     */
    private String payerName;

    /**
     * 发票种类 0 电子发票 1 纸质发票 2 专票
     */
    private String invoiceKind;

    /**
     * 发票类型0=个人，1=企业
     */
    private String invoiceType;

    /**
     * 买家税号
     */
    private String payerRegisterNo;

    private Date created;

    private Date modified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getPayerName() {
        return payerName;
    }

    public void setPayerName(String payerName) {
        this.payerName = payerName;
    }

    public String getInvoiceKind() {
        return invoiceKind;
    }

    public void setInvoiceKind(String invoiceKind) {
        this.invoiceKind = invoiceKind;
    }

    public String getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(String invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getPayerRegisterNo() {
        return payerRegisterNo;
    }

    public void setPayerRegisterNo(String payerRegisterNo) {
        this.payerRegisterNo = payerRegisterNo;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getFkId() {
        return fkId;
    }

    public void setFkId(String fkId) {
        this.fkId = fkId;
    }
}
