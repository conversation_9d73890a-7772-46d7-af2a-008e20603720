package com.kuaidizs.jxc.domain.print.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.kuaidizs.jxc.common.util.ExServiceItemModel;
import com.kuaidizs.jxc.domain.print.*;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/8/2
 */
public class KddTemplateInfoVo implements Serializable {

    @JSONField(name = "ModeList")
    private ModeList modeList = new ModeList();

    @JSONField(name = "ModeListShow")
    private ModeListshow modeListShow = new ModeListshow();

    @JSONField(name = "ModeInputs")
    private List<ModeInput> modeInputList = new ArrayList<ModeInput>();

    @JSONField(name = "ModeCustomerTemplate")
    private ModeCustomerTemplate modeCustomerTemplate = new ModeCustomerTemplate();

    @JSONField(name = "ModeServiceItems")
    private List<ModeServiceItem> modeServiceItemList = new ArrayList<ModeServiceItem>();

    @JSONField(name = "ModeTempPrintcfg")
    private ModeTempPrintCfg modeTempPrintcfg = new ModeTempPrintCfg();

    @JSONField(name = "DefaultServiceItems")
    private List<ExServiceItemModel> defaultServiceItems = new ArrayList<ExServiceItemModel>();

    @JSONField(name = "ModeAdvancedServices")
    private List<ModeAdvancedService> modeAdvancedServices = new ArrayList<ModeAdvancedService>();

    @JSONField(name = "DefaultLogisticsItmes")
    private List<ModeLogisticsItemCommon> defaultLogisticsItmes = new ArrayList<>();

    @JSONField(name = "DynamicServiceInfoDtoVos")
    private List<DynamicServiceInfoDtoVo> dynamicServiceInfoDtoVos = new ArrayList<>();

    @JSONField(name = "ModeLogisticsItems")
    private List<ModeLogisticsItem> modeLogisticsItems = new ArrayList<>();

    @JSONField(name = "ModeSet")
    private ModeSet modeSet;

    public ModeList getModeList() {
        return modeList;
    }

    public void setModeList(ModeList modeList) {
        this.modeList = modeList;
    }

    public ModeListshow getModeListShow() {
        return modeListShow;
    }

    public void setModeListShow(ModeListshow modeListShow) {
        this.modeListShow = modeListShow;
    }

    public List<ModeInput> getModeInputList() {
        return modeInputList;
    }

    public void setModeInputList(List<ModeInput> modeInputList) {
        this.modeInputList = modeInputList;
    }

    public ModeCustomerTemplate getModeCustomerTemplate() {
        return modeCustomerTemplate;
    }

    public void setModeCustomerTemplate(ModeCustomerTemplate modeCustomerTemplate) {
        this.modeCustomerTemplate = modeCustomerTemplate;
    }

    public List<ModeServiceItem> getModeServiceItemList() {
        return modeServiceItemList;
    }

    public void setModeServiceItemList(List<ModeServiceItem> modeServiceItemList) {
        this.modeServiceItemList = modeServiceItemList;
    }

    public ModeTempPrintCfg getModeTempPrintcfg() {
        return modeTempPrintcfg;
    }

    public void setModeTempPrintcfg(ModeTempPrintCfg modeTempPrintcfg) {
        this.modeTempPrintcfg = modeTempPrintcfg;
    }

    public List<ExServiceItemModel> getDefaultServiceItems() {
        return defaultServiceItems;
    }

    public void setDefaultServiceItems(List<ExServiceItemModel> defaultServiceItems) {
        this.defaultServiceItems = defaultServiceItems;
    }

    public List<ModeAdvancedService> getModeAdvancedServices() {
        return modeAdvancedServices;
    }

    public void setModeAdvancedServices(List<ModeAdvancedService> modeAdvancedServices) {
        this.modeAdvancedServices = modeAdvancedServices;
    }

    public List<ModeLogisticsItemCommon> getDefaultLogisticsItmes() {
        return defaultLogisticsItmes;
    }

    public void setDefaultLogisticsItmes(List<ModeLogisticsItemCommon> defaultLogisticsItmes) {
        this.defaultLogisticsItmes = defaultLogisticsItmes;
    }

    public List<ModeLogisticsItem> getModeLogisticsItems() {
        return modeLogisticsItems;
    }

    public void setModeLogisticsItems(List<ModeLogisticsItem> modeLogisticsItems) {
        this.modeLogisticsItems = modeLogisticsItems;
    }

    public List<DynamicServiceInfoDtoVo> getDynamicServiceInfoDtoVos() {
        return dynamicServiceInfoDtoVos;
    }

    public void setDynamicServiceInfoDtoVos(List<DynamicServiceInfoDtoVo> dynamicServiceInfoDtoVos) {
        this.dynamicServiceInfoDtoVos = dynamicServiceInfoDtoVos;
    }

    public ModeSet getModeSet() {
        return modeSet;
    }

    public void setModeSet(ModeSet modeSet) {
        this.modeSet = modeSet;
    }
}
