package com.kuaidizs.jxc.domain.agent;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023-05-22
 * 代发订单标记  用于合单、过滤
 */
@Data
public class AgentTradeTag {

    /**
     * 放心购判断是否分销代发订单：  true:是     false：否
     */
    private Boolean isDaifaTrade = false;

    /**
     * 分销店铺ID(商家店铺ID)
     */
    private String businessMallUserId;

    /**
     * 代发厂家名称
     */
    private String daiFaFactoryName;

    private String daiFaFactoryId;

    private boolean factoryMark = false;
}
