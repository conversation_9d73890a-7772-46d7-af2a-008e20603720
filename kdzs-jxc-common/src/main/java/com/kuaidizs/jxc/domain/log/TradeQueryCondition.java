package com.kuaidizs.jxc.domain.log;

import java.io.Serializable;
import java.util.List;

/**
 * 交易查询条件实体类
 */
public class TradeQueryCondition implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long tradeRuleId;
    private List<Long> userIds;
    private Integer timeType;
    private String startTime;
    private String endTime;
    private String tradeStatus;
    private AddressRule addressRule;
    private MessageOrMemo messageOrMemo;
    private List<Integer> stockType;
    private List<String> tid;
    private List<String> buyerNick;
    private ItemInfo itemInfo;
    private String itemCategory;
    private String itemNum;
    private String buyerMessage;
    private String tradeNum;
    private List<String> buyerMobile;
    private String tradeAmount;
    private String market;
    private SellerMemo sellerMemo;
    private TradeWeight tradeWeight;
    private String stall;
    private String buyerOpenUid;
    private KdInfo kdInfo;
    private List<String> receiverNames;
    private String itemFields;
    private Integer printStatus;
    private String tradeType;
    private Integer refundStatus;
    private String tradeSource;
    private Integer salesAttribute;
    private String quickQuery;
    private Integer labelId;
    private Integer memberLabel;
    private String remainDeliveryTime;
    private Integer daifaQueyType;
    private String daifaFactoryName;
    private Integer daifaStatus;
    private Integer yishenPushStatus;
    private Integer preciseQueryStatus;
    private Integer quickQueryLimitStatus;

    // 内部类定义
    public static class AddressRule implements Serializable {
        // addressType=0:表示选择地址
        // addressType=1:表示自定义地址关键词
        // includeType=0:表示包含，
        // includeType=1:表示不包含
        private Integer addressType;
        private Integer includeType;
        private List<AddressNode> selectRule;
        private List<String> keywordRule;

        // getter and setter methods
        public Integer getAddressType() {
            return addressType;
        }

        public void setAddressType(Integer addressType) {
            this.addressType = addressType;
        }

        public Integer getIncludeType() {
            return includeType;
        }

        public void setIncludeType(Integer includeType) {
            this.includeType = includeType;
        }

        public List<AddressNode> getSelectRule() {
            return selectRule;
        }

        public void setSelectRule(List<AddressNode> selectRule) {
            this.selectRule = selectRule;
        }

        public List<String> getKeywordRule() {
            return keywordRule;
        }

        public void setKeywordRule(List<String> keywordRule) {
            this.keywordRule = keywordRule;
        }
    }

    public static class AddressNode implements Serializable {
        private String name;
        private List<AddressNode> childList;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public List<AddressNode> getChildList() {
            return childList;
        }

        public void setChildList(List<AddressNode> childList) {
            this.childList = childList;
        }
    }

    public static class MessageOrMemo implements Serializable {
        private Integer type;
        private Integer includeType;
        private List<Integer> flagList;

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public Integer getIncludeType() {
            return includeType;
        }

        public void setIncludeType(Integer includeType) {
            this.includeType = includeType;
        }

        public List<Integer> getFlagList() {
            return flagList;
        }

        public void setFlagList(List<Integer> flagList) {
            this.flagList = flagList;
        }
    }

    public static class ItemInfo implements Serializable {
        private Integer includeType;
        private String itemKeyword;
        private String shortTitle;
        private String skuKeyword;
        private String color;

        public Integer getIncludeType() {
            return includeType;
        }

        public void setIncludeType(Integer includeType) {
            this.includeType = includeType;
        }

        public String getItemKeyword() {
            return itemKeyword;
        }

        public void setItemKeyword(String itemKeyword) {
            this.itemKeyword = itemKeyword;
        }

        public String getShortTitle() {
            return shortTitle;
        }

        public void setShortTitle(String shortTitle) {
            this.shortTitle = shortTitle;
        }

        public String getSkuKeyword() {
            return skuKeyword;
        }

        public void setSkuKeyword(String skuKeyword) {
            this.skuKeyword = skuKeyword;
        }

        public String getColor() {
            return color;
        }

        public void setColor(String color) {
            this.color = color;
        }
    }

    public static class SellerMemo implements Serializable {
        private Integer includeType;
        private String content;

        public Integer getIncludeType() {
            return includeType;
        }

        public void setIncludeType(Integer includeType) {
            this.includeType = includeType;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }

    public static class TradeWeight implements Serializable {
        private Integer type;
        private String content;

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }

    public static class KdInfo implements Serializable {
        private String kdName;
        private List<String> kdNo;

        public String getKdName() {
            return kdName;
        }

        public void setKdName(String kdName) {
            this.kdName = kdName;
        }

        public List<String> getKdNo() {
            return kdNo;
        }

        public void setKdNo(List<String> kdNo) {
            this.kdNo = kdNo;
        }
    }

    // Getter and Setter methods for main class
    public Long getTradeRuleId() {
        return tradeRuleId;
    }

    public void setTradeRuleId(Long tradeRuleId) {
        this.tradeRuleId = tradeRuleId;
    }

    public List<Long> getUserIds() {
        return userIds;
    }

    public void setUserIds(List<Long> userIds) {
        this.userIds = userIds;
    }

    public Integer getTimeType() {
        return timeType;
    }

    public void setTimeType(Integer timeType) {
        this.timeType = timeType;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getTradeStatus() {
        return tradeStatus;
    }

    public void setTradeStatus(String tradeStatus) {
        this.tradeStatus = tradeStatus;
    }

    public AddressRule getAddressRule() {
        return addressRule;
    }

    public void setAddressRule(AddressRule addressRule) {
        this.addressRule = addressRule;
    }

    public MessageOrMemo getMessageOrMemo() {
        return messageOrMemo;
    }

    public void setMessageOrMemo(MessageOrMemo messageOrMemo) {
        this.messageOrMemo = messageOrMemo;
    }

    public List<Integer> getStockType() {
        return stockType;
    }

    public void setStockType(List<Integer> stockType) {
        this.stockType = stockType;
    }

    public List<String> getTid() {
        return tid;
    }

    public void setTid(List<String> tid) {
        this.tid = tid;
    }

    public List<String> getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(List<String> buyerNick) {
        this.buyerNick = buyerNick;
    }

    public ItemInfo getItemInfo() {
        return itemInfo;
    }

    public void setItemInfo(ItemInfo itemInfo) {
        this.itemInfo = itemInfo;
    }

    public String getItemCategory() {
        return itemCategory;
    }

    public void setItemCategory(String itemCategory) {
        this.itemCategory = itemCategory;
    }

    public String getItemNum() {
        return itemNum;
    }

    public void setItemNum(String itemNum) {
        this.itemNum = itemNum;
    }

    public String getBuyerMessage() {
        return buyerMessage;
    }

    public void setBuyerMessage(String buyerMessage) {
        this.buyerMessage = buyerMessage;
    }

    public String getTradeNum() {
        return tradeNum;
    }

    public void setTradeNum(String tradeNum) {
        this.tradeNum = tradeNum;
    }

    public List<String> getBuyerMobile() {
        return buyerMobile;
    }

    public void setBuyerMobile(List<String> buyerMobile) {
        this.buyerMobile = buyerMobile;
    }

    public String getTradeAmount() {
        return tradeAmount;
    }

    public void setTradeAmount(String tradeAmount) {
        this.tradeAmount = tradeAmount;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public SellerMemo getSellerMemo() {
        return sellerMemo;
    }

    public void setSellerMemo(SellerMemo sellerMemo) {
        this.sellerMemo = sellerMemo;
    }

    public TradeWeight getTradeWeight() {
        return tradeWeight;
    }

    public void setTradeWeight(TradeWeight tradeWeight) {
        this.tradeWeight = tradeWeight;
    }

    public String getStall() {
        return stall;
    }

    public void setStall(String stall) {
        this.stall = stall;
    }

    public String getBuyerOpenUid() {
        return buyerOpenUid;
    }

    public void setBuyerOpenUid(String buyerOpenUid) {
        this.buyerOpenUid = buyerOpenUid;
    }

    public KdInfo getKdInfo() {
        return kdInfo;
    }

    public void setKdInfo(KdInfo kdInfo) {
        this.kdInfo = kdInfo;
    }

    public List<String> getReceiverNames() {
        return receiverNames;
    }

    public void setReceiverNames(List<String> receiverNames) {
        this.receiverNames = receiverNames;
    }

    public String getItemFields() {
        return itemFields;
    }

    public void setItemFields(String itemFields) {
        this.itemFields = itemFields;
    }

    public Integer getPrintStatus() {
        return printStatus;
    }

    public void setPrintStatus(Integer printStatus) {
        this.printStatus = printStatus;
    }

    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public Integer getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(Integer refundStatus) {
        this.refundStatus = refundStatus;
    }

    public String getTradeSource() {
        return tradeSource;
    }

    public void setTradeSource(String tradeSource) {
        this.tradeSource = tradeSource;
    }

    public Integer getSalesAttribute() {
        return salesAttribute;
    }

    public void setSalesAttribute(Integer salesAttribute) {
        this.salesAttribute = salesAttribute;
    }

    public String getQuickQuery() {
        return quickQuery;
    }

    public void setQuickQuery(String quickQuery) {
        this.quickQuery = quickQuery;
    }

    public Integer getLabelId() {
        return labelId;
    }

    public void setLabelId(Integer labelId) {
        this.labelId = labelId;
    }

    public Integer getMemberLabel() {
        return memberLabel;
    }

    public void setMemberLabel(Integer memberLabel) {
        this.memberLabel = memberLabel;
    }

    public String getRemainDeliveryTime() {
        return remainDeliveryTime;
    }

    public void setRemainDeliveryTime(String remainDeliveryTime) {
        this.remainDeliveryTime = remainDeliveryTime;
    }

    public Integer getDaifaQueyType() {
        return daifaQueyType;
    }

    public void setDaifaQueyType(Integer daifaQueyType) {
        this.daifaQueyType = daifaQueyType;
    }

    public String getDaifaFactoryName() {
        return daifaFactoryName;
    }

    public void setDaifaFactoryName(String daifaFactoryName) {
        this.daifaFactoryName = daifaFactoryName;
    }

    public Integer getDaifaStatus() {
        return daifaStatus;
    }

    public void setDaifaStatus(Integer daifaStatus) {
        this.daifaStatus = daifaStatus;
    }

    public Integer getYishenPushStatus() {
        return yishenPushStatus;
    }

    public void setYishenPushStatus(Integer yishenPushStatus) {
        this.yishenPushStatus = yishenPushStatus;
    }

    public Integer getPreciseQueryStatus() {
        return preciseQueryStatus;
    }

    public void setPreciseQueryStatus(Integer preciseQueryStatus) {
        this.preciseQueryStatus = preciseQueryStatus;
    }

    public Integer getQuickQueryLimitStatus() {
        return quickQueryLimitStatus;
    }

    public void setQuickQueryLimitStatus(Integer quickQueryLimitStatus) {
        this.quickQueryLimitStatus = quickQueryLimitStatus;
    }
} 