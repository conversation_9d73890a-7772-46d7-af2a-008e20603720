package com.kuaidizs.jxc.domain.log.opera;

import com.kuaidizs.jxc.domain.trade.GoodsRule;
import lombok.Data;

import java.util.List;

@Data
public class TradeRuleString {

    private String buyerNick;
    private String buyerOpenUid;
    private boolean fuzzySearch;
    private boolean filterByTrade;
    private int goodsTotalNum;
    private int goodsTypeNum;
    private String tradeNum;
    private String tradeWeight;
    private List<GoodsRule> goodsRuleList;
    private String printStatus;
    private String refundStatus;
    private String flagValue;
    private String msgMemoValue;
    private String buyerMessage;
    private String sellerMemo;
    private List<String> receiverAddressList;
    private String abortTime;
    private String abortTimeDay;
    private String exceedTime;
    private String startTime;
    private String endTime;
    private String payment;
    private String type;
    private String status;
    private String market;
    private String stall;
    private String kdName;
    private String addressType;
    private String areaJson;
    private String areaContain;
    private String addrRule;
    private String addrRuleId;
}
