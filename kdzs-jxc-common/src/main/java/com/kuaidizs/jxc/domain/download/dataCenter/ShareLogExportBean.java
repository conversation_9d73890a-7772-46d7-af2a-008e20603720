package com.kuaidizs.jxc.domain.download.dataCenter;

import com.kuaidizs.jxc.domain.trade.TradeExportConfig;

import java.util.Date;

/**
 * @auther xudaomeng
 * @since 2020-08-04 15:41
 */
public class ShareLogExportBean {

    /**
     * 店铺旺旺
     */
    @TradeExportConfig.ExportField(name = "店铺旺旺", value = 1, order = 1)
    private String sharedTaobaoNick;

    /**
     * 快递公司
     */
    @TradeExportConfig.ExportField(name = "快递公司", value = 1, order = 2)
    private String exName;

    /**
     * 电子面单发货地址
     */
    @TradeExportConfig.ExportField(name = "电子面单发货地址", value = 1, order = 3)
    private String sendAddr;

    /**
     * 网点名称
     */
    @TradeExportConfig.ExportField(name = "网点名称", value = 1, order = 4)
    private String branchName;

    /**
     * 网点编码
     */
    @TradeExportConfig.ExportField(name = "网点编码", value = 1, order = 5)
    private String branchCode;

    /**
     * 操作记录
     */
    @TradeExportConfig.ExportField(name = "操作记录", value = 1, order = 6)
    private String content;

    /**
     * 操作人
     */
    @TradeExportConfig.ExportField(name = "操作人", value = 1, order = 7)
    private String operator;

    /**
     * 操作时间
     */
    @TradeExportConfig.ExportField(name = "操作时间", value = 1, order = 8)
    private Date created;

    public String getSharedTaobaoNick() {
        return sharedTaobaoNick;
    }

    public void setSharedTaobaoNick(String sharedTaobaoNick) {
        this.sharedTaobaoNick = sharedTaobaoNick;
    }

    public String getExName() {
        return exName;
    }

    public void setExName(String exName) {
        this.exName = exName;
    }

    public String getSendAddr() {
        return sendAddr;
    }

    public void setSendAddr(String sendAddr) {
        this.sendAddr = sendAddr;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }
}
