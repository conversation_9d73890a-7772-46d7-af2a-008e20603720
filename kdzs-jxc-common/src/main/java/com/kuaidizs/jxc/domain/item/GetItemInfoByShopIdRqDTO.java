package com.kuaidizs.jxc.domain.item;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/9/5 10:20
 */
@Data
public class GetItemInfoByShopIdRqDTO implements Serializable {
    @NotBlank(message = "参数对象不能为空")
    private String paramJson;
    @Data
    public static class ParamDTO{
        @NotNull(message = "平台类型不能为空")
        private String platformType;
        //@NotNull(message = "店铺id不能为空")
        private String shopId;
        @NotNull(message = "用户id不能为空")
        private String mallUserId;
        @NotNull(message = "页码不能为空")
        private Integer pageNo;
        @NotNull(message = "pageSize不能为空")
        private Integer pageSize;
    }
    @NotBlank(message = "时间戳不能为空")
    private String timestamp;
    @NotBlank(message = "签名不能为空")
    private String sign;
}
