package com.kuaidizs.jxc.domain.ai;

import java.io.Serializable;
import lombok.Data;

@Data
public class AiTradeSyncByUserResult implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 订单所属用户ID
     */
    private Long userId;
    /**
     * 订单所属用户名称
     */
    private String userName;
    /**
     * 同步了多少订单
     */
    private Integer syncTradeNum;
    /**
     * 查询订单耗时(毫秒)
     */
    private Long queryTradeCost;
    /**
     * 保存订单耗时(毫秒)
     */
    private Long saveTradeCost;
    /**
     * 填充订单属性耗时(毫秒)
     */
    private Long fillTradeCost;
    /**
     * 总耗时(毫秒)
     */
    private Long totalCost;
    /**
     * 是否同步失败
     */
    private Boolean isError;
    /**
     * 失败信息
     */
    private String errorMsg;

    public static AiTradeSyncByUserResult emptyObj() {
        AiTradeSyncByUserResult r = new AiTradeSyncByUserResult();
        r.setSyncTradeNum(0);
        r.setIsError(false);
        return r;
    }

}
