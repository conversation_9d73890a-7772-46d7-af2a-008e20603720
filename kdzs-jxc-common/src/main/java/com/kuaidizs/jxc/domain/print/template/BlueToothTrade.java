package com.kuaidizs.jxc.domain.print.template;

import java.io.Serializable;

/**
 * 蓝牙打印trade vo
 */
public class BlueToothTrade implements Serializable {


    private static final long serialVersionUID = -4285440123081507066L;

    /**
     * 收件人名
     */
    private String receiverName;
    /**
     * 收件人省
     */
    private String receiverProvince;
    /**
     * 收件人市
     */
    private String receiverCity;
    /**
     * 收件人区
     */
    private String receiverDistrict;
    /**
     * 收件人街道
     */
    private String receiverTown;
    /**
     * 收件人详细地址
     */
    private String receiverAddress;
    /**
     * 收件人手机号
     */
    private String receiverMobile;
    /**
     * 收件人固话
     */
    private String receiverPhone;
    /**
     * 收件人邮编
     */
    private String receiverZip;
    /**
     * 发件人名
     */
    private String senderName;
    /**
     * 发件人省
     */
    private String senderProvince;
    /**
     * 发件人市
     */
    private String senderCity;
    /**
     * 发件人区
     */
    private String senderDistrict;
    /**
     * 发件人街道
     */
    private String senderTown;
    /**
     * 发件人详细地址
     */
    private String senderAddress;
    /**
     * 发件人手机号
     */
    private String senderMobile;
    /**
     * 发件人固话
     */
    private String senderPhone;
    /**
     * 发件人邮编
     */
    private String senderZip;
    /**
     * 发货信息
     */
    private String sendInfo;


    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getReceiverProvince() {
        return receiverProvince;
    }

    public void setReceiverProvince(String receiverProvince) {
        this.receiverProvince = receiverProvince;
    }

    public String getReceiverCity() {
        return receiverCity;
    }

    public void setReceiverCity(String receiverCity) {
        this.receiverCity = receiverCity;
    }

    public String getReceiverDistrict() {
        return receiverDistrict;
    }

    public void setReceiverDistrict(String receiverDistrict) {
        this.receiverDistrict = receiverDistrict;
    }

    public String getReceiverTown() {
        return receiverTown;
    }

    public void setReceiverTown(String receiverTown) {
        this.receiverTown = receiverTown;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }

    public String getReceiverMobile() {
        return receiverMobile;
    }

    public void setReceiverMobile(String receiverMobile) {
        this.receiverMobile = receiverMobile;
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }

    public String getReceiverZip() {
        return receiverZip;
    }

    public void setReceiverZip(String receiverZip) {
        this.receiverZip = receiverZip;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public String getSenderProvince() {
        return senderProvince;
    }

    public void setSenderProvince(String senderProvince) {
        this.senderProvince = senderProvince;
    }

    public String getSenderCity() {
        return senderCity;
    }

    public void setSenderCity(String senderCity) {
        this.senderCity = senderCity;
    }

    public String getSenderDistrict() {
        return senderDistrict;
    }

    public void setSenderDistrict(String senderDistrict) {
        this.senderDistrict = senderDistrict;
    }

    public String getSenderTown() {
        return senderTown;
    }

    public void setSenderTown(String senderTown) {
        this.senderTown = senderTown;
    }

    public String getSenderAddress() {
        return senderAddress;
    }

    public void setSenderAddress(String senderAddress) {
        this.senderAddress = senderAddress;
    }

    public String getSenderMobile() {
        return senderMobile;
    }

    public void setSenderMobile(String senderMobile) {
        this.senderMobile = senderMobile;
    }

    public String getSenderPhone() {
        return senderPhone;
    }

    public void setSenderPhone(String senderPhone) {
        this.senderPhone = senderPhone;
    }

    public String getSenderZip() {
        return senderZip;
    }

    public void setSenderZip(String senderZip) {
        this.senderZip = senderZip;
    }

    public String getSendInfo() {
        return sendInfo;
    }

    public void setSendInfo(String sendInfo) {
        this.sendInfo = sendInfo;
    }

}
