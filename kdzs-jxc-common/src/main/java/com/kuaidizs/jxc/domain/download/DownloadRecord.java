package com.kuaidizs.jxc.domain.download;

import java.util.Date;

/**
 * @auther xudaomeng
 * @since 2020-07-09 10:32
 */
public class DownloadRecord {

    private Long id;            //主键id
    private Long taobaoId;         //用户id
    private Date created;       //创建时间
    private String queryWayBillInfo;   //订单导出条件
    private Integer tradeCount; //订单数量
    private Integer downloadStatus; //生成状态：0.等待生成，1.生成中，2.生成完成，3.生成失败
    private String opreator;       //操作人
    private Integer status;         //状态：1.生效，2.已删除
    private Integer module;         //模块：1.单号记录 2.分享日志
    private String fileName;        //生成的文件名
    private Date modified;          //修改时间
    private String exCode;         //快递codes
    private String comsumer;       //客户

    /**
     * 客户所在平台：1、淘宝基础助手；2、快递助手ERP
     * 假设是A分单给B，也就是B所在的平台
     * 默认是淘宝基础助手
     **/
    private String customerPlatform;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public String getQueryWayBillInfo() {
        return queryWayBillInfo;
    }

    public void setQueryWayBillInfo(String queryWayBillInfo) {
        this.queryWayBillInfo = queryWayBillInfo;
    }

    public Integer getTradeCount() {
        return tradeCount;
    }

    public void setTradeCount(Integer tradeCount) {
        this.tradeCount = tradeCount;
    }

    public Integer getDownloadStatus() {
        return downloadStatus;
    }

    public void setDownloadStatus(Integer downloadStatus) {
        this.downloadStatus = downloadStatus;
    }

    public String getOpreator() {
        return opreator;
    }

    public void setOpreator(String opreator) {
        this.opreator = opreator;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getModule() {
        return module;
    }

    public void setModule(Integer module) {
        this.module = module;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public String getExCode() {
        return exCode;
    }

    public void setExCode(String exCode) {
        this.exCode = exCode;
    }

    public String getComsumer() {
        return comsumer;
    }

    public void setComsumer(String comsumer) {
        this.comsumer = comsumer;
    }

    public String getCustomerPlatform() {
        return customerPlatform;
    }

    public void setCustomerPlatform(String customerPlatform) {
        this.customerPlatform = customerPlatform;
    }
}
