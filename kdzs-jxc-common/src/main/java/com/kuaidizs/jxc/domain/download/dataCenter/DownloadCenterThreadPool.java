package com.kuaidizs.jxc.domain.download.dataCenter;

import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.concurrent.*;

/**
 * @auther xudaomeng
 * @since 2020-07-09 15:27
 */
@Component
public class DownloadCenterThreadPool implements InitializingBean, DisposableBean {

    private ExecutorService asyncExcutor;

    private ConcurrentHashMap<Long, Future> taskMap = new ConcurrentHashMap<>(16);

    public void addTask(Runnable dnTask, Long taskId) {
        Future future = asyncExcutor.submit(dnTask);
        //taskMap.put(taskId, future);
    }

    public void removeTask(Long taskId) {
        taskMap.remove(taskId);
    }

    public boolean isTaskRunning(Long taskId) {
        return taskMap.get(taskId) != null;
    }

    public boolean cancelTask(Long id) {
        Future future = taskMap.get(id);
        if (future != null) {
            taskMap.remove(id);
            return future.cancel(true);
        }
        return false;
    }

    @Override
    public void destroy() throws Exception {
        if (asyncExcutor != null) {
            asyncExcutor.shutdown();
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        asyncExcutor = new ThreadPoolExecutor(1, 2, 10,
                TimeUnit.SECONDS, new ArrayBlockingQueue<Runnable>(2));
    }
}
