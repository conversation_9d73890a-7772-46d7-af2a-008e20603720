package com.kuaidizs.jxc.domain.bhd;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * Date    2024-06-06
 */
public class BhdShare implements Serializable {

    private static final long serialVersionUID = 2349627216413161463L;
    /**
     * 分享唯一码
     */
    private String shortCode;
    /**
     * id
     */
    private Long id;
    /**
     * 淘宝id
     */
    private Long taobaoId;
    /**
     * 内容，oos地址
     */
    private String content;
    /**
     * 状态 0.可用 1.已取消
     */
    private Integer status;
    /**
     * 状态 0.未删除 1.已删除
     */
    private Integer deleteStatus;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;


    /**
     * @return shortCode 分享唯一码
     */
    public String getShortCode() {
        return shortCode;
    }

    /**
     * @param shortCode 分享唯一码
     */
    public void setShortCode(String shortCode) {
        this.shortCode = shortCode;
    }

    /**
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return taobaoId 淘宝id
     */
    public Long getTaobaoId() {
        return taobaoId;
    }

    /**
     * @param taobaoId 淘宝id
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return content 内容，oos地址
     */
    public String getContent() {
        return content;
    }

    /**
     * @param content 内容，oos地址
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * @return status 状态 0.可用 1.已取消
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * @param status 状态 0.可用 1.已取消
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * @return deleteStatus 状态 0.未删除 1.已删除
     */
    public Integer getDeleteStatus() {
        return deleteStatus;
    }

    /**
     * @param deleteStatus 状态 0.未删除 1.已删除
     */
    public void setDeleteStatus(Integer deleteStatus) {
        this.deleteStatus = deleteStatus;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

}