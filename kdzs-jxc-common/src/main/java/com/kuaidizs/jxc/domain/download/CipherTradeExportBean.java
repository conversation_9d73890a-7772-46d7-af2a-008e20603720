package com.kuaidizs.jxc.domain.download;

import com.kuaidizs.jxc.domain.trade.TradeExportConfig.ExportField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * CipherTradeExportBean
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CipherTradeExportBean implements Serializable {

    private static final long serialVersionUID = -477295828317086147L;

    /**
     * 订单编号
     */
    private String tids;

    /**
     * 订单编号
     */
    @ExportField(name = "订单编号", value = 1, order = 1)
    private String tid;

    /**
     * 买家昵称(脱敏)
     */
    @ExportField(name = "买家昵称(脱敏)", value = 1, order = 2)
    private String buyerNick;

    /**
     * 收件人(脱敏)
     */
    @ExportField(name = "收件人(脱敏)", value = 1, order = 3)
    private String receiverName;

    /**
     * 联系电话(脱敏)
     */
    @ExportField(name = "联系电话(脱敏)", value = 1, order = 4)
    private String receiverMobile;

    /**
     * 收件地址(脱敏)
     */
    @ExportField(name = "收件地址(脱敏)", value = 1, order = 5)
    private String receiverAddress;

    /**
     * 收件人(密文)
     */
    @ExportField(name = "收件人(密文)", value = 1, order = 6)
    private String cipherReceiverName;

    /**
     * 联系电话(密文)
     */
    @ExportField(name = "联系电话(密文)", value = 1, order = 7)
    private String cipherReceiverMobile;

    /**
     * 收件地址(密文)
     */
    @ExportField(name = "收件地址(密文)", value = 1, order = 8)
    private String cipherReceiverAddress;

    /**
     * 平台
     */
    @ExportField(name = "平台", value = 1, order = 9)
    private String platform;

    /**
     * 店铺ID
     */
    @ExportField(name = "店铺ID", value = 1, order = 10)
    private String shopId;

    /**
     * 店铺名称
     */
    @ExportField(name = "店铺名称", value = 1, order = 11)
    private String shopName;

    /**
     * OAID
     */
    @ExportField(name = "OAID", value = 1, order = 12)
    private String oaid;

    /**
     * 发货内容
     */
    @ExportField(name = "发货内容", value = 1, order = 13)
    private String info;

    /**
     * 数量
     */
    @ExportField(name="数量", value = 1, order = 14)
    private String num;

    /**
     * 总重量
     */
    @ExportField(name="总重量", value = 0, order = 15)
    private String weight;

    /**
     * 备注
     */
    @ExportField(name = "备注", value = 1, order = 16)
    private String sellerMemo;

    /**
     * 代收金额
     */
    @ExportField(name = "代收金额", value = 1, order = 17)
    private String collectionMoney;

    /**
     * 保价金额
     */
    @ExportField(name = "保价金额", value = 0, order = 18)
    private String declarationValue;
    /**
     * 业务类型
     */
    @ExportField(name = "业务类型", value = 0, order = 19)
    private String businessType;

    /**
     * 实付金额
     */
    @ExportField(name = "实付金额", value = 1, order = 20)
    private String payment;
}

