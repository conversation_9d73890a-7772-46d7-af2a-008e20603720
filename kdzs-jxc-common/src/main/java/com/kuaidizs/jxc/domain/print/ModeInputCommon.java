package com.kuaidizs.jxc.domain.print;

import java.util.*;
import java.io.Serializable;

/**
 *
 * <AUTHOR> @date    2016-07-26
 */
public class ModeInputCommon implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 自增ID
     */
    private Integer id;
	/**
     * 创建时间
     */
    private Date created;
	/**
     * 修改时间
     */
    private Date modified;
	/**
     * 逻辑状态
     */
    private Boolean enableStatus;
	/**
     * kdd/fhd
     */
    private String inputType;
	/**
     * mode_listshow表的主键
     */
    private Integer modeListShowKddId;
	/**
     * mode_listshow表的主键
     */
    private Integer modeListShowFhdId;
	/**
     * 模板项的宽
     */
    private Integer w;
	/**
     * 模板项的高
     */
    private Integer h;
	/**
     * 模板项的x坐标
     */
    private Integer x;
	/**
     * 模板项的y坐标
     */
    private Integer y;
	/**
     * 是否可编辑0不可编辑 1可编辑
     */
    private Integer isEdit;
	/**
     * 字体 空为使用默认值
     */
    private String fontName;
	/**
     * 字号 0为使用默认值
     */
    private Integer fontSize;
	/**
     * 是否加粗 空为使用默认值  1加粗  0不加粗
     */
    private Integer isbN;
	/**
     * 行间距
     */
    private Integer lineSpacing;
	/**
     * 字间距
     */
    private Integer wordSpacing;
	/**
     * 前文字
     */
    private String wordLeft;
	/**
     * 后文字
     */
    private String wordRight;
	/**
     * 状态
     */
    private Integer status;
	/**
     * 是否允许编辑--1：允许
     */
    private Boolean isEditable;
	/**
     * 是否允许拖动--1：允许
     */
    private Boolean isDraggable;

    /**
     * 0,1,2 水平方向 左中右
     */
    private String align;

    /**
     * 不存库处理
     */
    private List<ModeInputproCommon> modeInputproCommons;
	
   /**
    * @return id 自增ID
    */
    public Integer getId() {
       return id;
    }
   /**
    * @param id 自增ID
    */
    public void setId(Integer id) {
       this.id = id;
    }
	
   /**
    * @return created 创建时间
    */
    public Date getCreated() {
       return created;
    }
   /**
    * @param created 创建时间
    */
    public void setCreated(Date created) {
       this.created = created;
    }
	
   /**
    * @return modified 修改时间
    */
    public Date getModified() {
       return modified;
    }
   /**
    * @param modified 修改时间
    */
    public void setModified(Date modified) {
       this.modified = modified;
    }
	
   /**
    * @return enableStatus 逻辑状态
    */
    public Boolean getEnableStatus() {
       return enableStatus;
    }
   /**
    * @param enableStatus 逻辑状态
    */
    public void setEnableStatus(Boolean enableStatus) {
       this.enableStatus = enableStatus;
    }
	
   /**
    * @return inputType kdd/fhd
    */
    public String getInputType() {
       return inputType;
    }
   /**
    * @param inputType kdd/fhd
    */
    public void setInputType(String inputType) {
       this.inputType = inputType;
    }
	
   /**
    * @return modeListShowKddId mode_listshow表的主键
    */
    public Integer getModeListShowKddId() {
       return modeListShowKddId;
    }
   /**
    * @param modeListShowKddId mode_listshow表的主键
    */
    public void setModeListShowKddId(Integer modeListShowKddId) {
       this.modeListShowKddId = modeListShowKddId;
    }
	
   /**
    * @return modeListShowFhdId mode_listshow表的主键
    */
    public Integer getModeListShowFhdId() {
       return modeListShowFhdId;
    }
   /**
    * @param modeListShowFhdId mode_listshow表的主键
    */
    public void setModeListShowFhdId(Integer modeListShowFhdId) {
       this.modeListShowFhdId = modeListShowFhdId;
    }
	
   /**
    * @return w 模板项的宽
    */
    public Integer getW() {
       return w;
    }
   /**
    * @param w 模板项的宽
    */
    public void setW(Integer w) {
       this.w = w;
    }
	
   /**
    * @return h 模板项的高
    */
    public Integer getH() {
       return h;
    }
   /**
    * @param h 模板项的高
    */
    public void setH(Integer h) {
       this.h = h;
    }
	
   /**
    * @return x 模板项的x坐标
    */
    public Integer getX() {
       return x;
    }
   /**
    * @param x 模板项的x坐标
    */
    public void setX(Integer x) {
       this.x = x;
    }
	
   /**
    * @return y 模板项的y坐标
    */
    public Integer getY() {
       return y;
    }
   /**
    * @param y 模板项的y坐标
    */
    public void setY(Integer y) {
       this.y = y;
    }
	
   /**
    * @return isEdit 是否可编辑0不可编辑 1可编辑
    */
    public Integer getIsEdit() {
       return isEdit;
    }
   /**
    * @param isEdit 是否可编辑0不可编辑 1可编辑
    */
    public void setIsEdit(Integer isEdit) {
       this.isEdit = isEdit;
    }
	
   /**
    * @return fontName 字体 空为使用默认值
    */
    public String getFontName() {
       return fontName;
    }
   /**
    * @param fontName 字体 空为使用默认值
    */
    public void setFontName(String fontName) {
       this.fontName = fontName;
    }
	
   /**
    * @return fontSize 字号 0为使用默认值
    */
    public Integer getFontSize() {
       return fontSize;
    }
   /**
    * @param fontSize 字号 0为使用默认值
    */
    public void setFontSize(Integer fontSize) {
       this.fontSize = fontSize;
    }
	
   /**
    * @return isbN 是否加粗 空为使用默认值  1加粗  0不加粗
    */
    public Integer getIsbN() {
       return isbN;
    }
   /**
    * @param isbN 是否加粗 空为使用默认值  1加粗  0不加粗
    */
    public void setIsbN(Integer isbN) {
       this.isbN = isbN;
    }
	
   /**
    * @return lineSpacing 行间距
    */
    public Integer getLineSpacing() {
       return lineSpacing;
    }
   /**
    * @param lineSpacing 行间距
    */
    public void setLineSpacing(Integer lineSpacing) {
       this.lineSpacing = lineSpacing;
    }
	
   /**
    * @return wordSpacing 字间距
    */
    public Integer getWordSpacing() {
       return wordSpacing;
    }
   /**
    * @param wordSpacing 字间距
    */
    public void setWordSpacing(Integer wordSpacing) {
       this.wordSpacing = wordSpacing;
    }
	
   /**
    * @return wordLeft 前文字
    */
    public String getWordLeft() {
       return wordLeft;
    }
   /**
    * @param wordLeft 前文字
    */
    public void setWordLeft(String wordLeft) {
       this.wordLeft = wordLeft;
    }
	
   /**
    * @return wordRight 后文字
    */
    public String getWordRight() {
       return wordRight;
    }
   /**
    * @param wordRight 后文字
    */
    public void setWordRight(String wordRight) {
       this.wordRight = wordRight;
    }
	
   /**
    * @return status 状态
    */
    public Integer getStatus() {
       return status;
    }
   /**
    * @param status 状态
    */
    public void setStatus(Integer status) {
       this.status = status;
    }
	
   /**
    * @return isEditable 是否允许编辑--1：允许
    */
    public Boolean getIsEditable() {
       return isEditable;
    }
   /**
    * @param isEditable 是否允许编辑--1：允许
    */
    public void setIsEditable(Boolean isEditable) {
       this.isEditable = isEditable;
    }
	
   /**
    * @return isDraggable 是否允许拖动--1：允许
    */
    public Boolean getIsDraggable() {
       return isDraggable;
    }
   /**
    * @param isDraggable 是否允许拖动--1：允许
    */
    public void setIsDraggable(Boolean isDraggable) {
       this.isDraggable = isDraggable;
    }

    public String getAlign() {
        return align;
    }

    public void setAlign(String align) {
        this.align = align;
    }

    public List<ModeInputproCommon> getModeInputproCommons() {
        return modeInputproCommons;
    }

    public void setModeInputproCommons(List<ModeInputproCommon> modeInputproCommons) {
        this.modeInputproCommons = modeInputproCommons;
    }
}