package com.kuaidizs.jxc.domain.ai;

import java.io.Serializable;
import lombok.Data;

@Data
public class AiSortTradeSyncResult implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 订单源数据的版本号
     */
    private Long version;
    /**
     * 订单同步的唯一标识
     */
    private String syncKey;
    /**
     * 同步了多少AiTrade数据
     */
    private Integer syncAiTradeNum;
    /**
     * 同步了多少AiTrade数据(在过滤完之后)
     */
    private Integer syncAiTradeNumAfterFilter;
    /**
     * 同步了多少AiSortTrade数据
     */
    private Integer syncAiSortTradeNum;
    /**
     * 同步了多少AiSortTrade数据(在分类操作之后剩余的单量)
     */
    private Integer syncAiSortTradeNumAfterCategorize;
    /**
     * 同步进度,[0,100]
     */
    private Integer syncProgress;
    /**
     * 查询订单源数据的耗时(毫秒)
     */
    private Long queryAiTradeCost;
    /**
     * 过滤订单源数据的耗时(毫秒)
     */
    private Long filterAiTradeCost;
    /**
     * 转换成AiSortTrade的耗时(毫秒)
     */
    private Long convertToAiSortTradeCost;
    /**
     * 分类AiSortTrade的耗时(毫秒)
     */
    private Long categorizeAiSortTradeCost;
    /**
     * 排序AiSortTrade的耗时(毫秒)
     */
    private Long sortAiSortTradeCost;
    /**
     * 保存AiSortTrade的耗时(毫秒)
     */
    private Long saveAiSortTradeCost;
    /**
     * 统计AiSortTrade的耗时(毫秒)
     */
    private Long statisticsSortAiSortTradeCost;
    /**
     * 总耗时(毫秒)
     */
    private Long totalCost;
    /**
     * 是否同步失败
     */
    private Boolean isError;
    /**
     * 失败信息
     */
    private String errorMsg;

    public static AiSortTradeSyncResult emptyObj() {
        AiSortTradeSyncResult r = new AiSortTradeSyncResult();
        r.setSyncAiTradeNum(0);
        r.setSyncAiTradeNumAfterFilter(0);
        r.setSyncAiSortTradeNum(0);
        r.setSyncAiSortTradeNumAfterCategorize(0);
        r.setSyncProgress(0);
        r.setIsError(false);
        return r;
    }

}
