package com.kuaidizs.jxc.domain.ai;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

@Data
public class AiTradeSyncResult implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 订单时间范围-开始
     */
    private Date tradeStartTime;
    /**
     * 订单时间范围-结束
     */
    private Date tradeEndTime;
    /**
     * 同步订单的版本号
     */
    private Long version;
    /**
     * 同步了多少订单
     */
    private Integer syncTradeNum;
    /**
     * 同步了多少订单(合并后)
     */
    private Integer syncTradeNumAfterMerge;
    /**
     * 同步进度,[0,100]
     */
    private Integer syncProgress;
    /**
     * 同步订单总共耗时(毫秒)
     */
    private Long syncTradeCost;
    /**
     * 同步订单总数量限制
     */
    private Integer syncTradeNumLimit;
    /**
     * 是否同步失败
     */
    private Boolean isError;
    /**
     * 失败信息
     */
    private String errorMsg;

    /**
     * 同步结果-用户维度
     */
    private List<AiTradeSyncByUserResult> userSyncResults;

    public static AiTradeSyncResult emptyObj() {
        AiTradeSyncResult r = new AiTradeSyncResult();
        r.setSyncTradeNum(0);
        r.setSyncTradeNumAfterMerge(0);
        r.setSyncProgress(0);
        r.setIsError(false);
        return r;
    }

}
