package com.kuaidizs.jxc.domain.print.vo;

import com.alibaba.fastjson.JSONObject;
import com.taobao.api.response.CainiaoWaybillIiSearchResponse;

import java.io.Serializable;

/**
 * 动态的菜鸟服务节点信息
 * 注：展示给前端使用的数据，数据来源自 {@link com.kuaidizs.jxc.domain.print.ModeLogisticsDynamicCommon}
 *
 * <AUTHOR>
 * @date 2018/9/12
 */
public class DynamicServiceInfoDtoVo implements Serializable {

    /**
     * 是否需要订购
     * 由用户数据和系统数据动态判定
     */
    private Boolean purchase;

    /**
     * 快递公司
     */
    private String excode;
    /**
     * 快递公司名称
     */
    private String excodeName;
    /**
     * 快递类型:普通面单:1,网点面单:2;菜鸟面单:3
     */
    private Integer expressType;
    /**
     * 面单样式 自由，推荐，官方三联等等
     */
    private String expressStyle;

    /**
     * 此服务所属的号段，只有特殊服务有，比如中通星联，中通菜鸟国际保税
     *
     */
    private String segmentCode;

    /**
     * 网点服务
     * 原始的网点服务信息
     */
    private CainiaoWaybillIiSearchResponse.ServiceInfoDto serviceInfoDto;

    /**
     * 是否自定义渲染
     *
     * @see com.kuaidizs.jxc.domain.print.ModeLogisticsDynamicCommon#customStatus
     */
    private Integer customStatus;

    /**
     * 自定义网点服务
     * 注：大部分结构同自定义网点服务，还有其他的小改动点
     *
     * @see com.kuaidizs.jxc.domain.print.ModeLogisticsDynamicCommon#customValue
     */
    private JSONObject customServiceInfoDto;

    /**
     * 服务状态
     *
     * @see com.kuaidizs.jxc.domain.print.ModeLogisticsDynamicCommon#serviceStatus
     */
    private Integer serviceStatus;

    /**
     * 服务描述
     *
     * @see com.kuaidizs.jxc.domain.print.ModeLogisticsDynamicCommon#serviceDesc
     */
    private String serviceDesc;

    /**
     * 服务是否单选 默认可多选
     */
    private boolean singleChoice = false;

    public boolean isSingleChoice() {
        return singleChoice;
    }

    public void setSingleChoice(boolean singleChoice) {
        this.singleChoice = singleChoice;
    }

    public Boolean getPurchase() {
        return purchase;
    }

    public void setPurchase(Boolean purchase) {
        this.purchase = purchase;
    }

    public String getExcode() {
        return excode;
    }

    public void setExcode(String excode) {
        this.excode = excode;
    }

    public String getExcodeName() {
        return excodeName;
    }

    public void setExcodeName(String excodeName) {
        this.excodeName = excodeName;
    }

    public Integer getExpressType() {
        return expressType;
    }

    public void setExpressType(Integer expressType) {
        this.expressType = expressType;
    }

    public String getExpressStyle() {
        return expressStyle;
    }

    public void setExpressStyle(String expressStyle) {
        this.expressStyle = expressStyle;
    }

    public CainiaoWaybillIiSearchResponse.ServiceInfoDto getServiceInfoDto() {
        return serviceInfoDto;
    }

    public void setServiceInfoDto(CainiaoWaybillIiSearchResponse.ServiceInfoDto serviceInfoDto) {
        this.serviceInfoDto = serviceInfoDto;
    }

    public Integer getCustomStatus() {
        return customStatus;
    }

    public void setCustomStatus(Integer customStatus) {
        this.customStatus = customStatus;
    }

    public JSONObject getCustomServiceInfoDto() {
        return customServiceInfoDto;
    }

    public void setCustomServiceInfoDto(JSONObject customServiceInfoDto) {
        this.customServiceInfoDto = customServiceInfoDto;
    }

    public Integer getServiceStatus() {
        return serviceStatus;
    }

    public void setServiceStatus(Integer serviceStatus) {
        this.serviceStatus = serviceStatus;
    }

    public String getServiceDesc() {
        return serviceDesc;
    }

    public void setServiceDesc(String serviceDesc) {
        this.serviceDesc = serviceDesc;
    }

    public String getSegmentCode() {
        return segmentCode;
    }

    public void setSegmentCode(String segmentCode) {
        this.segmentCode = segmentCode;
    }
}
