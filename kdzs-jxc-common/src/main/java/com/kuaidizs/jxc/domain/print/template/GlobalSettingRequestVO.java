package com.kuaidizs.jxc.domain.print.template;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.List;

/**
 * Created by Administrator on 2016/7/26
 */
public class GlobalSettingRequestVO {

    //全局设置
    @JSONField(name = "ModeSet")
    private ModeSetVO modeSetVO;
    //默认快递ID
    @JSONField(name = "ModeListShowId")
    private Long modeListShowId;
    //用户ID
    @JSONField(name = "ExUserId")
    private Long taobaoId;
    //需要删除的快递ID集合
    @JSONField(name = "DelIds")
    private List<Long> delModeListshowIds;

    public ModeSetVO getModeSetVO() {
        return modeSetVO;
    }

    public void setModeSetVO(ModeSetVO modeSetVO) {
        this.modeSetVO = modeSetVO;
    }

    public Long getModeListShowId() {
        return modeListShowId;
    }

    public void setModeListShowId(Long modeListShowId) {
        this.modeListShowId = modeListShowId;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public List<Long> getDelModeListshowIds() {
        return delModeListshowIds;
    }

    public void setDelModeListshowIds(List<Long> delModeListshowIds) {
        this.delModeListshowIds = delModeListshowIds;
    }
}
