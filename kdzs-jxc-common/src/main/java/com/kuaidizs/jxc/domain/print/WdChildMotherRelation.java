package com.kuaidizs.jxc.domain.print;

import java.io.Serializable;
import java.util.*;

/**
 * <AUTHOR> (<EMAIL>)
 * @date 2018-04-18
 */
public class WdChildMotherRelation implements Serializable {


    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;
    /**
     * 用户id
     */
    private Long taobaoId;
    /**
     * 快递公司code
     */
    private String exCode;
    /**
     * 实际tid
     */
    private String realTid;

    private String requestOid;
    /**
     * 母运单号
     */
    private String ydId;
    /**
     * 子订单号，以,号分隔
     */
    private String zdYdId;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;


    /**
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return taobaoId 用户id
     */
    public Long getTaobaoId() {
        return taobaoId;
    }

    /**
     * @param taobaoId 用户id
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return exCode 快递公司code
     */
    public String getExCode() {
        return exCode;
    }

    /**
     * @param exCode 快递公司code
     */
    public void setExCode(String exCode) {
        this.exCode = exCode;
    }

    /**
     * @return realTid 实际tid
     */
    public String getRealTid() {
        return realTid;
    }

    /**
     * @param realTid 实际tid
     */
    public void setRealTid(String realTid) {
        this.realTid = realTid;
    }

    /**
     * @return ydId 母运单号
     */
    public String getYdId() {
        return ydId;
    }

    /**
     * @param ydId 母运单号
     */
    public void setYdId(String ydId) {
        this.ydId = ydId;
    }

    /**
     * @return zdYdId 子订单号，以,号分隔
     */
    public String getZdYdId() {
        return zdYdId;
    }

    /**
     * @param zdYdId 子订单号，以,号分隔
     */
    public void setZdYdId(String zdYdId) {
        this.zdYdId = zdYdId;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    public String getRequestOid() {
        return requestOid;
    }

    public void setRequestOid(String requestOid) {
        this.requestOid = requestOid;
    }
}