package com.kuaidizs.jxc.domain.ai;

import com.kuaidizs.jxc.common.util.StringUtil;
import java.util.List;
import lombok.Data;

@Data
public class AiSortTradeStatisticsOfItem {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 订单同步的唯一标识
     */
    private String syncKey;
    /**
     * 商品所属用户ID
     */
    private Long userId;
    /**
     * 商品ID
     */
    private String itemId;
    /**
     * 商品名称
     */
    private String itemName;
    /**
     * 商品商家编码
     */
    private String itemOuterId;
    /**
     * 商品简称
     */
    private String itemShortName;
    /**
     * 商品图片
     */
    private String itemPicUrl;
    /**
     * 订单数量
     */
    private Integer tradeNum;
    /**
     * 商品数量
     */
    private Integer itemNum;
    /**
     * 规格级别结果集
     */
    private List<AiSortTradeStatisticsOfSku> skuStatisticses;

    public void initFieldsBeforeInsert() {
        //一些不是很重要的字段，按数据库分配的长度截断一下
        this.itemName = StringUtil.frontSubstring(this.itemName, 256);
        this.itemShortName = StringUtil.frontSubstring(this.itemShortName, 128);
    }
}
