package com.kuaidizs.jxc.domain.data;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class PrintBatchRule implements Serializable {

    private Long id;

    private Long taobaoId;

    private String printBatchNo; // 打印批次号

    private Date printTime; // 打印时间

    private String ruleData; // 各个字段规则，json数据

    private Date created;

    private Date modified;


}
