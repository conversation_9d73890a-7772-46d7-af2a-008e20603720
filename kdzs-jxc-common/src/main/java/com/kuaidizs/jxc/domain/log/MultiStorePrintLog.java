package com.kuaidizs.jxc.domain.log;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/26.
 * @time 10:36.
 */
public class MultiStorePrintLog implements Serializable {

    /**
     *序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date modifyTime;
    /**
     * 操作的店铺id
     */
    private Long taobaoId;
    /**
     * 被打单的店铺id
     */
    private Long printTaobaoId;
    /**
     * 店铺分组id
     */
    private Long groupId;
    /**
     * 打单内容
     */
    private String content;
    /**
     * 操作人
     */
    private String operator;


    /**
     * @return id 主键
     */
    public Long getId() {
        return id;
    }
    /**
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return createTime 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }
    /**
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return modifyTime 修改时间
     */
    public Date getModifyTime() {
        return modifyTime;
    }
    /**
     * @param modifyTime 修改时间
     */
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * @return taobaoId 操作的店铺id
     */
    public Long getTaobaoId() {
        return taobaoId;
    }
    /**
     * @param taobaoId 操作的店铺id
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return printTaobaoId 被打单的店铺id
     */
    public Long getPrintTaobaoId() {
        return printTaobaoId;
    }
    /**
     * @param printTaobaoId 被打单的店铺id
     */
    public void setPrintTaobaoId(Long printTaobaoId) {
        this.printTaobaoId = printTaobaoId;
    }

    /**
     * @return groupId 店铺分组id
     */
    public Long getGroupId() {
        return groupId;
    }
    /**
     * @param groupId 店铺分组id
     */
    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    /**
     * @return content 打单内容
     */
    public String getContent() {
        return content;
    }
    /**
     * @param content 打单内容
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * @return operator 操作人
     */
    public String getOperator() {
        return operator;
    }
    /**
     * @param operator 操作人
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

}
