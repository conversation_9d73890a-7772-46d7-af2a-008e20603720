package com.kuaidizs.jxc.domain.ai;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class AIItem implements Serializable {

    private Long id;

    private Long taobaoId;

    /**
     * 淘宝昵称
     */
    private String taobaoNick;

    /**
     * 商品 id
     */
    private String numIid;

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品图片
     */
    private String picUrl;

    /**
     * sku id
     */
    private String skuId;

    /**
     * 规格商家编码
     */
    private String skuOuterId;

    /**
     *  sku 名称
     */
    private String skuName;

    /**
     * 是否转换成向量
     * 0：否；1：是
     */
    private int isVec;

    /**
     * 相似商品分组
     */
    private String similarGroup;

    /**
     * 不参与同款商品分组，0：不是，1：是
     */
    private int noGroup;

    private Date created;

    private Date modified;

    private String tableName;

    private String fkId;

    /**
     * 订单数量
     */
    private int tradeCount;

}
