package com.kuaidizs.jxc.domain.print.enums;

import org.apache.commons.lang3.StringUtils;

/**
 * 模版类型
 *
 * <AUTHOR>
 * @date 2017/7/11
 */
public enum EnumTemplateType {

    /**
     * 快递单
     */
    KDD,

    /**
     * 发货单
     */
    FHD,

    /**
     * 备货单
     */
    BHD,

    /**
     * 退货单
     */
    THD,

    /**
     * 地址单
     */
    DDD,

    /**
     * 报关签条
     */
    BGQT;


    /**
     * 根据名称获取对应的模板类型
     *
     * @param name
     * @return
     */
    public static EnumTemplateType getTemplateTypeByName(String name) {
        if (StringUtils.isBlank(name)) {
            return null;
        }
        EnumTemplateType[] types = EnumTemplateType.values();
        for (EnumTemplateType templateType : types) {
            if (StringUtils.equals(templateType.name(), name.toUpperCase())) {
                return templateType;
            }
        }
        return null;
    }

}
