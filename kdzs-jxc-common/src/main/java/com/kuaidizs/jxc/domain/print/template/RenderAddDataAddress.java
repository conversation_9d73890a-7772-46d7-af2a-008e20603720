package com.kuaidizs.jxc.domain.print.template;


import java.io.Serializable;

/**
 * 菜鸟指令渲染时的AddData数据结构地址信息
 */
public class RenderAddDataAddress implements Serializable {

    private static final long serialVersionUID = -7001220498709963557L;

    /**
     * 发件人省份
     */
    private String province;
    /**
     * 发件人城市
     */
    private String city;
    /**
     * 发件人区县
     */
    private String district;
    /**
     * 发件人城镇
     */
    private String town;
    /**
     * 发件人详细信息
     */
    private String detail;

    public String getProvince() {
        return province;
    }

    public void setProvince(String province) {
        this.province = province;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getDistrict() {
        return district;
    }

    public void setDistrict(String district) {
        this.district = district;
    }

    public String getTown() {
        return town;
    }

    public void setTown(String town) {
        this.town = town;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }
}
