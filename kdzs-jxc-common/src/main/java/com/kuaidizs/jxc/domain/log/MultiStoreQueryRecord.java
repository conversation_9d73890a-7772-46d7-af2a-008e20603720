package com.kuaidizs.jxc.domain.log;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/16.
 * @time 21:22.
 */
public class MultiStoreQueryRecord implements Serializable {

    private static final long serialVersionUID = -5677800785512511313L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date modifyTime;
    /**
     * 店铺分组id
     */
    private Long groupId;


    /**
     * @return id 主键
     */
    public Long getId() {
        return id;
    }
    /**
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return createTime 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }
    /**
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return modifyTime 修改时间
     */
    public Date getModifyTime() {
        return modifyTime;
    }
    /**
     * @param modifyTime 修改时间
     */
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * @return groupId 店铺分组id
     */
    public Long getGroupId() {
        return groupId;
    }
    /**
     * @param groupId 店铺分组id
     */
    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

}
