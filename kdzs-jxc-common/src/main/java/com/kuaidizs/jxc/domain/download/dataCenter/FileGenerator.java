package com.kuaidizs.jxc.domain.download.dataCenter;

import com.kuaidizs.jxc.common.util.ZipUtils;
import com.kuaidizs.jxc.domain.share.WaybillUseLog;
import com.kuaidizs.jxc.domain.trade.TradeExportConfig;
import org.apache.commons.collections.CollectionUtils;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.Collections;
import java.util.List;

/**
 * @auther xudaomeng
 * @since 2020-07-09 20:23
 */
public class FileGenerator<T> {


    private static final Integer MAX_SIZE_ONE_FILE = 60000;

    private Integer fileNums = 0;

    private String path;

    private ExcelFile excelFile;

    private Class clazz;

    public FileGenerator(String path,Class clazz) throws FileNotFoundException {
        this.path = path;
        File pathFile = new File(path);
        if (!pathFile.exists()) {
            pathFile.mkdirs();
        }
        this.clazz = clazz;
        excelFile = createNewFile();
    }

    public void appendLogs(List<T> waybillUseLogList) throws IOException, NoSuchMethodException, IllegalAccessException, InvocationTargetException {
        if (CollectionUtils.isEmpty(waybillUseLogList)) {
            return;
        }

        File pathFile = new File(path);
        if (!pathFile.exists()) {
            pathFile.mkdirs();
        }

        if (excelFile == null) {
            excelFile = createNewFile();
        }

        List<TradeExportConfig.ExportConfig> tradeExportConfigList = TradeExportConfig.ExportConfig.getExportConfig(clazz, 1);
        Collections.sort(tradeExportConfigList);

        if (excelFile.getFileSize() + waybillUseLogList.size() > MAX_SIZE_ONE_FILE) {
            Integer remainSize = MAX_SIZE_ONE_FILE - excelFile.getFileSize();
            List<T> firstList = waybillUseLogList.subList(0, remainSize);
            excelFile.writeData(firstList, tradeExportConfigList);
            closeFile();
            List<T> remainList = waybillUseLogList.subList(remainSize, waybillUseLogList.size());
            appendLogs(remainList);
        } else {
            excelFile.writeData(waybillUseLogList, tradeExportConfigList);
        }
    }

    private ExcelFile createNewFile() throws FileNotFoundException {
        fileNums++;
        return ExcelFile.createNewFile(path + File.separator + fileNums + ".xlsx",this.clazz);
    }

    public void closeFile() throws FileNotFoundException {
        if (excelFile != null) {
            excelFile.closeFile();
            excelFile = null;
        }
    }

    public String getZipFile() throws Exception {
        String zipFileName = path + ".zip";
        ZipUtils.zip(zipFileName, path, true);
        return zipFileName;
    }
}
