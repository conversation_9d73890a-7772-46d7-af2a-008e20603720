package com.kuaidizs.jxc.domain.ai;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public abstract class AiBaseDomain implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 所属用户ID
     */
    private Long userId;

    /**
     * 所属用户名
     */
    private String userName;

    /**
     * 数据库记录创建时间
     */
    private Date created;
}
