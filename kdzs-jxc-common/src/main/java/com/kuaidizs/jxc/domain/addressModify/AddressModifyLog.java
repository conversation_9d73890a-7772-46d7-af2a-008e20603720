package com.kuaidizs.jxc.domain.addressModify;

import java.io.Serializable;
import java.util.Date;

/**
 * @auther xudaomeng
 * @since 2021-02-24 17:30
 */
public class AddressModifyLog implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 淘宝ID
     */
    private Long taobaoId;
    /**
     * 交易订单id
     */
    private String tid;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 最近一次修改时间
     */
    private Date modified;
    /**
     * 0-未使用，1-默认使用
     */
    private Integer enableStatus = 1;

    /**
     * 买家淘宝nick
     */
    private String taobaoNick;

    /**
     * 原-收件人
     */
    private String oldReceiverName;

    /**
     * 原-收件人Secret
     */
    private String oldReceiverNameSecret;

    /**
     * 原-收件人手机号
     */
    private String oldReceiverMobile;

    /**
     * 原-收件人手机号Secret
     */
    private String oldReceiverMobileSecret;

    /**
     * 原-收件人固定电话
     */
    private String oldReceiverFixedPhone;

    /**
     * 原-邮编
     */
    private String oldPostalCode;

    /**
     * 原-收件省
     */
    private String oldReceiverProvince;

    /**
     * 原-收件市
     */
    private String oldReceiverCity;

    /**
     * 原-收件区
     */
    private String oldReceiverCounty;

    /**
     * 原-收件详情地址
     */
    private String oldReceiverAddress;

    /**
     * 新-收件人
     */
    private String newReceiverName;

    /**
     * 新-收件人Secret
     */
    private String newReceiverNameSecret;

    /**
     * 新-收件人手机号
     */
    private String newReceiverMobile;

    /**
     * 新-收件人手机号Secret
     */
    private String newReceiverMobileSecret;

    /**
     * 新-收件人固定电话
     */
    private String newReceiverFixedPhone;

    /**
     * 新-邮编
     */
    private String newPostalCode;

    /**
     * 新-收件省
     */
    private String newReceiverProvince;

    /**
     * 新-收件市
     */
    private String newReceiverCity;

    /**
     * 新-收件区
     */
    private String newReceiverCounty;

    /**
     * 新-收件详情地址
     */
    private String newReceiverAddress;

    /**
     * 操作结果(0失败,1成功)默认1
     */
    private Integer opResult;

    /**
     * 操作结果信息
     */
    private String opResultMsg;

    /**
     * 操作类型。(1地址修改,2规格修改)默认1
     */
    private Integer type;

    /**
     * 子订单
     */
    private String oid;

    /**
     * 商品id
     */
    private String itemId;

    /**
     * 修改前的规格id
     */
    private String oldSkuId;

    /**
     * 修改前规格商家编码
     */
    private String oldSkuOuterId;

    /**
     * 修改后规格id
     */
    private String skuId;

    /**
     * 修改后规格商家编码
     */
    private String skuOuterId;

    /**
     * @return id 主键ID
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id 主键ID
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * @return taobaoId 淘宝ID
     */
    public Long getTaobaoId() {
        return taobaoId;
    }

    /**
     * @param taobaoId 淘宝ID
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return tid 交易订单id
     */
    public String getTid() {
        return tid;
    }

    /**
     * @param tid 交易订单id
     */
    public void setTid(String tid) {
        this.tid = tid;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 最近一次修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 最近一次修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return enableStatus 0-未使用，1-默认使用
     */
    public Integer getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 0-未使用，1-默认使用
     */
    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }


    /**
     * 分表使用
     */
    private String tableName;

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    /**
     * 分库标识
     */
    private String fkId;

    public String getFkId() {
        return fkId;
    }

    public void setFkId(String fkId) {
        this.fkId = fkId;
    }

    public String getTaobaoNick() {
        return taobaoNick;
    }

    public void setTaobaoNick(String taobaoNick) {
        this.taobaoNick = taobaoNick;
    }

    public String getOldReceiverName() {
        return oldReceiverName;
    }

    public void setOldReceiverName(String oldReceiverName) {
        this.oldReceiverName = oldReceiverName;
    }

    public String getOldReceiverNameSecret() {
        return oldReceiverNameSecret;
    }

    public void setOldReceiverNameSecret(String oldReceiverNameSecret) {
        this.oldReceiverNameSecret = oldReceiverNameSecret;
    }

    public String getOldReceiverMobile() {
        return oldReceiverMobile;
    }

    public void setOldReceiverMobile(String oldReceiverMobile) {
        this.oldReceiverMobile = oldReceiverMobile;
    }

    public String getOldReceiverMobileSecret() {
        return oldReceiverMobileSecret;
    }

    public void setOldReceiverMobileSecret(String oldReceiverMobileSecret) {
        this.oldReceiverMobileSecret = oldReceiverMobileSecret;
    }

    public String getOldReceiverFixedPhone() {
        return oldReceiverFixedPhone;
    }

    public void setOldReceiverFixedPhone(String oldReceiverFixedPhone) {
        this.oldReceiverFixedPhone = oldReceiverFixedPhone;
    }

    public String getOldPostalCode() {
        return oldPostalCode;
    }

    public void setOldPostalCode(String oldPostalCode) {
        this.oldPostalCode = oldPostalCode;
    }

    public String getOldReceiverProvince() {
        return oldReceiverProvince;
    }

    public void setOldReceiverProvince(String oldReceiverProvince) {
        this.oldReceiverProvince = oldReceiverProvince;
    }

    public String getOldReceiverCity() {
        return oldReceiverCity;
    }

    public void setOldReceiverCity(String oldReceiverCity) {
        this.oldReceiverCity = oldReceiverCity;
    }

    public String getOldReceiverCounty() {
        return oldReceiverCounty;
    }

    public void setOldReceiverCounty(String oldReceiverCounty) {
        this.oldReceiverCounty = oldReceiverCounty;
    }

    public String getOldReceiverAddress() {
        return oldReceiverAddress;
    }

    public void setOldReceiverAddress(String oldReceiverAddress) {
        this.oldReceiverAddress = oldReceiverAddress;
    }

    public String getNewReceiverName() {
        return newReceiverName;
    }

    public void setNewReceiverName(String newReceiverName) {
        this.newReceiverName = newReceiverName;
    }

    public String getNewReceiverNameSecret() {
        return newReceiverNameSecret;
    }

    public void setNewReceiverNameSecret(String newReceiverNameSecret) {
        this.newReceiverNameSecret = newReceiverNameSecret;
    }

    public String getNewReceiverMobile() {
        return newReceiverMobile;
    }

    public void setNewReceiverMobile(String newReceiverMobile) {
        this.newReceiverMobile = newReceiverMobile;
    }

    public String getNewReceiverMobileSecret() {
        return newReceiverMobileSecret;
    }

    public void setNewReceiverMobileSecret(String newReceiverMobileSecret) {
        this.newReceiverMobileSecret = newReceiverMobileSecret;
    }

    public String getNewReceiverFixedPhone() {
        return newReceiverFixedPhone;
    }

    public void setNewReceiverFixedPhone(String newReceiverFixedPhone) {
        this.newReceiverFixedPhone = newReceiverFixedPhone;
    }

    public String getNewPostalCode() {
        return newPostalCode;
    }

    public void setNewPostalCode(String newPostalCode) {
        this.newPostalCode = newPostalCode;
    }

    public String getNewReceiverProvince() {
        return newReceiverProvince;
    }

    public void setNewReceiverProvince(String newReceiverProvince) {
        this.newReceiverProvince = newReceiverProvince;
    }

    public String getNewReceiverCity() {
        return newReceiverCity;
    }

    public void setNewReceiverCity(String newReceiverCity) {
        this.newReceiverCity = newReceiverCity;
    }

    public String getNewReceiverCounty() {
        return newReceiverCounty;
    }

    public void setNewReceiverCounty(String newReceiverCounty) {
        this.newReceiverCounty = newReceiverCounty;
    }

    public String getNewReceiverAddress() {
        return newReceiverAddress;
    }

    public void setNewReceiverAddress(String newReceiverAddress) {
        this.newReceiverAddress = newReceiverAddress;
    }

    public Integer getOpResult() {
        return opResult;
    }

    public void setOpResult(Integer opResult) {
        this.opResult = opResult;
    }

    public String getOpResultMsg() {
        return opResultMsg;
    }

    public void setOpResultMsg(String opResultMsg) {
        this.opResultMsg = opResultMsg;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getOid() {
        return oid;
    }

    public void setOid(String oid) {
        this.oid = oid;
    }

    public String getItemId() {
        return itemId;
    }

    public void setItemId(String itemId) {
        this.itemId = itemId;
    }

    public String getOldSkuId() {
        return oldSkuId;
    }

    public void setOldSkuId(String oldSkuId) {
        this.oldSkuId = oldSkuId;
    }

    public String getOldSkuOuterId() {
        return oldSkuOuterId;
    }

    public void setOldSkuOuterId(String oldSkuOuterId) {
        this.oldSkuOuterId = oldSkuOuterId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getSkuOuterId() {
        return skuOuterId;
    }

    public void setSkuOuterId(String skuOuterId) {
        this.skuOuterId = skuOuterId;
    }
}
