package com.kuaidizs.jxc.domain.print;

import java.io.Serializable;

/**
 *
 * <AUTHOR> @date    2016-12-05
 */
public class ModeAdvancedServiceCommon implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * id
     */
    private Integer id;
	/**
     * code
     */
    private String code;
	/**
     * name
     */
    private String name;
	/**
     * item_id
     */
    private Integer itemId;
	/**
     * item_key
     */
    private String itemKey;
	/**
     * item_name
     */
    private String itemName;
	/**
     * item_value
     */
    private String itemValue;
	/**
     * item_tips
     */
    private String itemTips;
	/**
     * data_name
     */
    private String dataName;
	/**
     * is_enable
     */
    private Integer isEnable;
	/**
     * is_set_val
     */
    private Integer isSetVal;
	/**
     * unit
     */
    private Integer unit;
    /**
     * 类型：2-网点电子面单，3-菜鸟电子面单
     */
    private Integer kddType;
	
   /**
    * @return id id
    */
    public Integer getId() {
       return id;
    }
   /**
    * @param id id
    */
    public void setId(Integer id) {
       this.id = id;
    }
	
   /**
    * @return code code
    */
    public String getCode() {
       return code;
    }
   /**
    * @param code code
    */
    public void setCode(String code) {
       this.code = code;
    }
	
   /**
    * @return name name
    */
    public String getName() {
       return name;
    }
   /**
    * @param name name
    */
    public void setName(String name) {
       this.name = name;
    }
	
   /**
    * @return itemId item_id
    */
    public Integer getItemId() {
       return itemId;
    }
   /**
    * @param itemId item_id
    */
    public void setItemId(Integer itemId) {
       this.itemId = itemId;
    }
	
   /**
    * @return itemKey item_key
    */
    public String getItemKey() {
       return itemKey;
    }
   /**
    * @param itemKey item_key
    */
    public void setItemKey(String itemKey) {
       this.itemKey = itemKey;
    }
	
   /**
    * @return itemName item_name
    */
    public String getItemName() {
       return itemName;
    }
   /**
    * @param itemName item_name
    */
    public void setItemName(String itemName) {
       this.itemName = itemName;
    }
	
   /**
    * @return itemValue item_value
    */
    public String getItemValue() {
       return itemValue;
    }
   /**
    * @param itemValue item_value
    */
    public void setItemValue(String itemValue) {
       this.itemValue = itemValue;
    }
	
   /**
    * @return itemTips item_tips
    */
    public String getItemTips() {
       return itemTips;
    }
   /**
    * @param itemTips item_tips
    */
    public void setItemTips(String itemTips) {
       this.itemTips = itemTips;
    }
	
   /**
    * @return dataName data_name
    */
    public String getDataName() {
       return dataName;
    }
   /**
    * @param dataName data_name
    */
    public void setDataName(String dataName) {
       this.dataName = dataName;
    }
	
   /**
    * @return isEnable is_enable
    */
    public Integer getIsEnable() {
       return isEnable;
    }
   /**
    * @param isEnable is_enable
    */
    public void setIsEnable(Integer isEnable) {
       this.isEnable = isEnable;
    }
	
   /**
    * @return isSetVal is_set_val
    */
    public Integer getIsSetVal() {
       return isSetVal;
    }
   /**
    * @param isSetVal is_set_val
    */
    public void setIsSetVal(Integer isSetVal) {
       this.isSetVal = isSetVal;
    }
	
   /**
    * @return unit unit
    */
    public Integer getUnit() {
       return unit;
    }
   /**
    * @param unit unit
    */
    public void setUnit(Integer unit) {
       this.unit = unit;
    }

    public Integer getKddType() {
        return kddType;
    }

    public void setKddType(Integer kddType) {
        this.kddType = kddType;
    }
}