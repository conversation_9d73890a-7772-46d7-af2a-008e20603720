package com.kuaidizs.jxc.domain.print;

import java.util.*;
import java.io.Serializable;

/**
 * <AUTHOR> @date 2017-02-16
 */
public class SortCodeEmsState implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Integer id;
    /**
     * 省份
     */
    private String state;
    /**
     * 分拣码
     */
    private String sortCode;


    /**
     * @return id id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * @return state 省份
     */
    public String getState() {
        return state;
    }

    /**
     * @param state 省份
     */
    public void setState(String state) {
        this.state = state;
    }

    /**
     * @return sortCode 分拣码
     */
    public String getSortCode() {
        return sortCode;
    }

    /**
     * @param sortCode 分拣码
     */
    public void setSortCode(String sortCode) {
        this.sortCode = sortCode;
    }

}