package com.kuaidizs.jxc.domain.print.template;

import com.alibaba.fastjson.annotation.JSONField;

import java.util.Date;

/**
 * Created by Administrator on 2016/7/26.
 */
public class ModeSetVO {
    /**
     * 自增列ID
     */
    @JSONField(name="Id")
    private Integer id;
    /**
     * 用户ID
     */
    @JSONField(name="ExUserId")
    private Long taobaoId;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * 逻辑删除
     */
    private Boolean enableStatus;
    /**
     * kdd/fhd
     */
    @JSONField(name="Modeid")
    private String modeId;
    /**
     * 字体
     */
    @JSONField(name="Fontname")
    private String fontName;
    /**
     * 字号
     */
    @JSONField(name="Fontsize")
    private String fontSize;
    /**
     * 是否加粗 0不加粗 1加粗
     */
    @JSONField(name="Isb")
    private Integer isb;
    /**
     * 横向缩放
     */
    @JSONField(name="Slider<PERSON>")
    private Integer sliderW;
    /**
     * 纵向缩放
     */
    @JSONField(name="SliderH")
    private Integer sliderH;
    /**
     * a4纸张的连接是否开启
     */
    private Integer a4mode;
    /**
     * a4纸张拼接的时候是否打印分隔线
     */
    private Integer a4line;
    /**
     * 发货单中有相同的商品是否合并
     */
    private Integer fhdIsMerge;
    /**
     * 电子面单线条颜色
     */
    private String lineStyle;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public Boolean getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Boolean enableStatus) {
        this.enableStatus = enableStatus;
    }

    public String getModeId() {
        return modeId;
    }

    public void setModeId(String modeId) {
        this.modeId = modeId;
    }

    public String getFontName() {
        return fontName;
    }

    public void setFontName(String fontName) {
        this.fontName = fontName;
    }

    public String getFontSize() {
        return fontSize;
    }

    public void setFontSize(String fontSize) {
        this.fontSize = fontSize;
    }

    public Integer getIsb() {
        return isb;
    }

    public void setIsb(Integer isb) {
        this.isb = isb;
    }

    public Integer getSliderW() {
        return sliderW;
    }

    public void setSliderW(Integer sliderW) {
        this.sliderW = sliderW;
    }

    public Integer getSliderH() {
        return sliderH;
    }

    public void setSliderH(Integer sliderH) {
        this.sliderH = sliderH;
    }

    public Integer getA4mode() {
        return a4mode;
    }

    public void setA4mode(Integer a4mode) {
        this.a4mode = a4mode;
    }

    public Integer getA4line() {
        return a4line;
    }

    public void setA4line(Integer a4line) {
        this.a4line = a4line;
    }

    public Integer getFhdIsMerge() {
        return fhdIsMerge;
    }

    public void setFhdIsMerge(Integer fhdIsMerge) {
        this.fhdIsMerge = fhdIsMerge;
    }

    public String getLineStyle() {
        return lineStyle;
    }

    public void setLineStyle(String lineStyle) {
        this.lineStyle = lineStyle;
    }
}
