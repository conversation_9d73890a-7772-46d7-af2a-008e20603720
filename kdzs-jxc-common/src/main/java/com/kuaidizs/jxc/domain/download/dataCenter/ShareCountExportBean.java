package com.kuaidizs.jxc.domain.download.dataCenter;

import com.kuaidizs.jxc.domain.trade.TradeExportConfig;

/**
 * @auther xudaomeng
 * @since 2021-01-04 15:40
 */
public class ShareCountExportBean {

    /**
     * 店铺旺旺
     */
    @TradeExportConfig.ExportField(name = "店铺旺旺", value = 1, order = 1)
    private String taobaoNick;



    /**
     * 分享者店铺的单号账户名称如sanchephoto
     */
    @TradeExportConfig.ExportField(name = "单号账户", value = 1, order = 2)
    private String shareTaobaoNick;

    /**
     * 客户备注
     */
    @TradeExportConfig.ExportField(name = "客户备注", value = 1, order = 3)
    private String memo;

    /**
     * 快递名称
     */
    @TradeExportConfig.ExportField(name = "快递公司", value = 1, order = 4)
    private String exName;

    /**
     * 网点名称
     */
    @TradeExportConfig.ExportField(name = "网点名称", value = 1, order = 5)
    private String branchName;

    /**
     * 网点编码
     */
    @TradeExportConfig.ExportField(name = "网点编码", value = 1, order = 6)
    private String branchCode;

    /**
     * 分享数量
     */
    @TradeExportConfig.ExportField(name = "分享数量", value = 1, order = 7)
    private String shareNumber;

    /**
     * 使用数量
     */
    @TradeExportConfig.ExportField(name = "使用数量", value = 1, order = 8)
    private Integer shareUsedNumber;

    /**
     * 回收数量
     */
    @TradeExportConfig.ExportField(name = "回收数量", value = 1, order = 9)
    private Integer shareCancelNumber;

    public String getTaobaoNick() {
        return taobaoNick;
    }

    public void setTaobaoNick(String taobaoNick) {
        this.taobaoNick = taobaoNick;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getExName() {
        return exName;
    }

    public void setExName(String exName) {
        this.exName = exName;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getShareNumber() {
        return shareNumber;
    }

    public void setShareNumber(String shareNumber) {
        this.shareNumber = shareNumber;
    }

    public Integer getShareUsedNumber() {
        return shareUsedNumber;
    }

    public void setShareUsedNumber(Integer shareUsedNumber) {
        this.shareUsedNumber = shareUsedNumber;
    }

    public Integer getShareCancelNumber() {
        return shareCancelNumber;
    }

    public void setShareCancelNumber(Integer shareCancelNumber) {
        this.shareCancelNumber = shareCancelNumber;
    }

    public String getShareTaobaoNick() {
        return shareTaobaoNick;
    }

    public void setShareTaobaoNick(String shareTaobaoNick) {
        this.shareTaobaoNick = shareTaobaoNick;
    }
}
