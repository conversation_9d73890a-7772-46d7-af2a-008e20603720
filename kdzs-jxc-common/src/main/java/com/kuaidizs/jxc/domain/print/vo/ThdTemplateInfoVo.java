package com.kuaidizs.jxc.domain.print.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.kuaidizs.jxc.domain.print.*;

import java.io.Serializable;
import java.util.List;

/**
 * Created by liumandong on 2020/7/20
 */
public class ThdTemplateInfoVo implements Serializable {

    private static final long serialVersionUID = 7785529622636986814L;

    /**
     * 模版输入框和输入项
     */
    @JSONField(name = "ModeInputs")
    private List<ModeInput> modeInputs;

    /**
     * 打印基础设置
     */
    @JSONField(name = "ModeTempPrintcfg")
    private ModeTempPrintCfg modeTempPrintCfg;

    /**
     * 原生模版信息
     */
    @JSONField(name = "ModeList")
    private ModeList modeList;

    /**
     * 用户模版基础信息
     */
    @JSONField(name = "ModeListShow")
    private ModeListshow modeListshow;

    /**
     * 全局设置信息
     */
    @JSONField(name = "ModeSet")
    private ModeSet modeSet;

    public List<ModeInput> getModeInputs() {
        return modeInputs;
    }

    public void setModeInputs(List<ModeInput> modeInputs) {
        this.modeInputs = modeInputs;
    }

    public ModeTempPrintCfg getModeTempPrintCfg() {
        return modeTempPrintCfg;
    }

    public void setModeTempPrintCfg(ModeTempPrintCfg modeTempPrintCfg) {
        this.modeTempPrintCfg = modeTempPrintCfg;
    }

    public ModeList getModeList() {
        return modeList;
    }

    public void setModeList(ModeList modeList) {
        this.modeList = modeList;
    }

    public ModeListshow getModeListshow() {
        return modeListshow;
    }

    public void setModeListshow(ModeListshow modeListshow) {
        this.modeListshow = modeListshow;
    }

    public ModeSet getModeSet() {
        return modeSet;
    }

    public void setModeSet(ModeSet modeSet) {
        this.modeSet = modeSet;
    }
}
