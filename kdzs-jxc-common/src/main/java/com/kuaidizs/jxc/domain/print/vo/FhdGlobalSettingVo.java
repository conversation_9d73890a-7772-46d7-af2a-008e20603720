package com.kuaidizs.jxc.domain.print.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.kuaidizs.jxc.domain.print.ModeListshow;
import com.kuaidizs.jxc.domain.print.ModeSet;
import com.kuaidizs.jxc.domain.print.ModeSetFjrFhd;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2018/8/23
 */
public class FhdGlobalSettingVo implements Serializable {

    /**
     * 默认模板ID
     */
    @JSONField(name = "ModeListShowId")
    private Long defaultModeListshowId;
    /**
     * 获取模版列表
     */
    @JSONField(name = "ModeListShows")
    private List<ModeListshow> modeListshows;
    /**
     * 默认发货单发件人信息
     */
    @JSONField(name = "FjrSet")
    private ModeSetFjrFhd modeSetFjrFhd;
    /**
     * 发货单全局设置
     */
    @JSONField(name = "ModeSet")
    private ModeSet modeSet;
    /**
     * 系统字体
     */
    @JSONField(name = "SystemFonts")
    private List<String> systemFonts;

    @JSONField(name = "DelIds")
    private List<Long> deleteModeListshowIds;

    public Long getDefaultModeListshowId() {
        return defaultModeListshowId;
    }

    public void setDefaultModeListshowId(Long defaultModeListshowId) {
        this.defaultModeListshowId = defaultModeListshowId;
    }

    public List<ModeListshow> getModeListshows() {
        return modeListshows;
    }

    public void setModeListshows(List<ModeListshow> modeListshows) {
        this.modeListshows = modeListshows;
    }

    public ModeSetFjrFhd getModeSetFjrFhd() {
        return modeSetFjrFhd;
    }

    public void setModeSetFjrFhd(ModeSetFjrFhd modeSetFjrFhd) {
        this.modeSetFjrFhd = modeSetFjrFhd;
    }

    public ModeSet getModeSet() {
        return modeSet;
    }

    public void setModeSet(ModeSet modeSet) {
        this.modeSet = modeSet;
    }

    public List<String> getSystemFonts() {
        return systemFonts;
    }

    public void setSystemFonts(List<String> systemFonts) {
        this.systemFonts = systemFonts;
    }

    public List<Long> getDeleteModeListshowIds() {
        return deleteModeListshowIds;
    }

    public void setDeleteModeListshowIds(List<Long> deleteModeListshowIds) {
        this.deleteModeListshowIds = deleteModeListshowIds;
    }
}
