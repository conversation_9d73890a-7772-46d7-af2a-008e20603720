package com.kuaidizs.jxc.domain.cjdf;

import lombok.Data;

import java.util.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * Date    2022-03-02
 */
@Data
public class CjdfUserCustomSet implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long taobaoId;
    /**
     * 主键
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date modifyTime;
    /**
     * 逻辑删除 1：可用 0 不可用
     */
    private Integer enableStatus;
    /**
     * 展示方式  0：单列 1：双列 2：三列
     */
    private Integer showProductWay;
    /**
     * 产品内容展示 1：规格 2：标题  3：标题+规格
     */
    private Integer showProductContent;
}