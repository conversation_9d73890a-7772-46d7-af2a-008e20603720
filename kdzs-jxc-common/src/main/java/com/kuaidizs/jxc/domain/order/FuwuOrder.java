package com.kuaidizs.jxc.domain.order;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@TableName("fuwu_order")
public class FuwuOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * order_id
     */
    private String orderId;

    /**
     * nick
     */
    private String nick;

    /**
     * order_status
     */
    private Integer orderStatus;

    /**
     * activity_code
     */
    private String activityCode;

    /**
     * article_code
     */
    private String articleCode;

    /**
     * article_name
     */
    private String articleName;

    /**
     * item_name
     */
    private String itemName;

    /**
     * item_code
     */
    private String itemCode;

    /**
     * biz_type
     */
    private Integer bizType;

    /**
     * order_cycle
     */
    private String orderCycle;

    /**
     * create
     */
    private Date created;

    /**
     * pay_time
     */
    private Date payTime;

    /**
     * pay_status
     */
    private Integer payStatus;

    /**
     * order_cycle_start
     */
    private Date orderCycleStart;

    /**
     * order_cycle_end
     */
    private Date orderCycleEnd;

    /**
     * refund_fee
     */
    private String refundFee;

    /**
     * biz_order_id
     */
    private Long bizOrderId;

    /**
     * total_pay_fee
     */
    private String totalPayFee;

    /**
     * fee
     */
    private String fee;

    /**
     * prom_fee
     */
    private String promFee;

    /**
     * version_no
     */
    private Integer versionNo;

    /**
     * outer_trade_code
     */
    private String outerTradeCode;

    /**
     * add_time
     */
    private Date addTime;

    /**
     * upd_time
     */
    private Date updTime;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 首次登录时间
     */
    private Date firstLoginTime;

    private String fkId;

}