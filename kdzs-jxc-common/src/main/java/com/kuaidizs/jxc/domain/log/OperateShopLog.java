package com.kuaidizs.jxc.domain.log;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 20190322
 */
public class OperateShopLog implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 主店铺或关联店铺的淘宝id
     */
    private Long taobaoId;
    /**
     * 操作人淘宝昵称
     */
    private String taobaoNick;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 逻辑删除
     */
    private Integer enableStatus;
    /**
     * 操作时间
     */
    private Date operateTime;
    /**
     * 操作类型
     */
    private Integer operateType;
    /**
     * ip地址
     */
    private String ipAddr;
    /**
     * 操作内容
     */
    private String content;
    /**
     * 操作结果 1 成功 0 失败
     */
    private String result;

    private final String ADDSHOP = "添加关联店铺";
    private final String REMOVESHOP = "移除关联店铺";


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public String getTaobaoNick() {
        return taobaoNick;
    }

    public void setTaobaoNick(String taobaoNick) {
        this.taobaoNick = taobaoNick;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Integer getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Date getOperateTime() {
        return operateTime;
    }

    public void setOperateTime(Date operateTime) {
        this.operateTime = operateTime;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public String getIpAddr() {
        return ipAddr;
    }

    public void setIpAddr(String ipAddr) {
        this.ipAddr = ipAddr;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    /**
     * @param isAdd      true 添加店铺 false 移除店铺
     * @param initiative true 主动方 false  被动方
     * @param shopName   isAdd为true 时 shopName 为被关联店铺； 为false，shopName 为主店铺
     */
    public void setContent(boolean isAdd, boolean initiative, String shopName) {
        if (isAdd && initiative) {
            this.content = ADDSHOP + "( " + shopName + " )";
        }
        if (!isAdd && initiative) {
            this.content = REMOVESHOP + "( " + shopName + " )";
        }
        if (isAdd && !initiative) {
            this.content = "被( " + shopName + " )" + ADDSHOP;
        }
        if (!isAdd && !initiative) {
            this.content = "被( " + shopName + " )" + REMOVESHOP;
        }
    }


}

