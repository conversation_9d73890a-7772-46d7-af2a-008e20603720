package com.kuaidizs.jxc.domain.print.waybill;

import com.kuaidizs.jxc.common.util.ConstantLogistics;

import java.io.Serializable;

/**
 * 物流服务选项
 * <p>
 * 属于{@link com.kuaidizs.jxc.domain.print.ModeLogisticsItemCommon#serviceValue} 的结构
 *
 * <AUTHOR>
 * @date 2018/1/10
 * @see com.kuaidizs.jxc.domain.print.ModeLogisticsItemCommon
 */
public class LogisticsServiceValue implements Serializable {

    public static final String ATTRIBUTE_CODE = "value";

    /**
     * 服务选项名称
     * 例如：订单金额保价|声明价值保价
     */
    private String name;

    /**
     * 服务选项是否展示
     */
    private Boolean show;

    /**
     * 服务选项类型
     * 即:服务编码(serviceCode)下不同的业务从属
     * {@link ConstantLogistics}
     */
    private String type;

    /**
     * 服务选项取值
     */
    private String value;

    /**
     * 属性信息
     * 默认为value
     */
    private String code;


    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getShow() {
        return show;
    }

    public void setShow(Boolean show) {
        this.show = show;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
