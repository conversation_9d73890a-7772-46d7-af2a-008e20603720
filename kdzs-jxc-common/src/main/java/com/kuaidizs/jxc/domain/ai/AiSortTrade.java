package com.kuaidizs.jxc.domain.ai;

import com.kuaidizs.jxc.common.enums.SellerFlag;
import com.kuaidizs.jxc.common.enums.TradeTypeEnum;
import com.kuaidizs.jxc.common.enums.ai.AiSortTradeCategoryEnum;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@Data
public class AiSortTrade extends AiBaseDomain {

    /**
     * 订单同步的唯一标识
     */
    private String syncKey;

    /**
     * 主单号
     */
    private String masterTid;

    /**
     * 订单号集合
     */
    private List<String> tids;

    /**
     * 收件人省份
     */
    private String receiverProvince;

    /**
     * 卖家备注旗帜
     */
    private List<SellerFlag> sellerFlags;

    /**
     * 是否有卖家备注
     */
    private Boolean hasSellerMemo;

    /**
     * 是否有买家留言
     */
    private Boolean hasBuyerMessage;

    /**
     * 数据版本
     */
    private Long version;

    /**
     * 订单商品信息
     */
    private List<AiSortOrder> orders;

    /**
     * 订单类型
     */
    private List<TradeTypeEnum> tradeTypes;

    /**
     * 订单所属分类信息
     */
    private AiSortTradeCategoryEnum category;

    /**
     * 订单所属分类对应的分值，用于排序，分值越大，排序的时候越靠前
     */
    private Integer categoryScore;

    /**
     * 订单所属二级分类信息
     */
    private AiSortTradeCategoryEnum secondCategory;

    /**
     * 订单所属二级分类对应的分值，用于排序，分值越大，排序的时候越靠前
     */
    private Integer secondCategoryScore;

    //排序字段=====================
    private List<Date> payTimes;
    private List<Date> tbCreateds;
    private String receiverCity;
    private String receiverDistrict;
    private List<Date> expectedShippingTimes;
    private BigDecimal tradeWeight;
    private String buyerOpenId;
    private Integer itemOuterIdNum;
    private Integer itemIdNum;
    private Integer skuColorNum;
    private Integer skuSizeNum;
    private Integer skuIdNum;
    private Integer similarSkuIdNum;
    private String itemOuterId;
    private String itemId;
    private String skuColor;
    private Integer skuSize;
    private String skuId;
    private String similarSkuId;
    /**
     * 有一些排序依赖的不是单个字段，而是多个字段联合，这种统一放这个字段
     */
    private String unionSortKey;
    /**
     * unionSortKey对应的分值
     */
    private Integer unionSortKeyScore;


    //数据库字段=====================
    private String tidsStr;
    private String categoryCode;

    public void initFieldsBeforeInsert() {
        if (CollectionUtils.isNotEmpty(this.tids)) {
            this.tidsStr = StringUtils.join(this.tids, ",");
        }
        if (this.category != null) {
            this.categoryCode = this.category.name();
        }
    }

    public void initFieldsForResult() {
        if (StringUtils.isNotBlank(this.tidsStr)) {
            this.tids = new ArrayList<>();
            for (String tid : this.tidsStr.split(",")) {
                this.tids.add(tid);
            }
        }
        if (StringUtils.isNotBlank(this.categoryCode)) {
            this.category = AiSortTradeCategoryEnum.getByName(this.categoryCode);
        }
    }

}
