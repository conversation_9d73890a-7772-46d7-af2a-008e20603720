package com.kuaidizs.jxc.domain.user;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @create 2021-06-08 2:04 下午
 */
public class UserIllegalAddress implements Serializable {
    /**
     *序列化ID
     */
    private static final long serialVersionUID = -1L;

    /**
     * id
     */
    private Long id;
    /**
     * taobao_id
     */
    private Long taobaoId;
    /**
     * 自定义异常地址 ,分割
     */
    private String customAddress;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 更新时间
     */
    private Date modified;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public String getCustomAddress() {
        return customAddress;
    }

    public void setCustomAddress(String customAddress) {
        this.customAddress = customAddress;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }
}
