package com.kuaidizs.jxc.domain.erpplus;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class MoveUserInfo implements Serializable {
    private static final long serialVersionUID = 3430367739915660451L;

    //主账户taobaoId
    private Long groupUserId;

    //迁移用户的taobaoIdMap，k:taobaoId,v:是否已开启新版进销存
    Map<Long, Boolean> moveUserIdMap = new HashMap<>();

    public Long getGroupUserId() {
        return groupUserId;
    }

    public void setGroupUserId(Long groupUserId) {
        this.groupUserId = groupUserId;
    }

    public Map<Long, Boolean> getMoveUserIdMap() {
        return moveUserIdMap;
    }

    public void setMoveUserIdMap(Map<Long, Boolean> moveUserIdMap) {
        this.moveUserIdMap = moveUserIdMap;
    }
}
