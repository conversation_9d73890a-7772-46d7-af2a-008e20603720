package com.kuaidizs.jxc.domain.print.template;

import java.io.Serializable;

/**
 * 小程序打印数据请求vo
 * <AUTHOR>
 * @date 2020/4/27 上午9:50
 */
public class AppPrintLogVO implements Serializable {


    private static final long serialVersionUID = 3971430677003649758L;

    /**
     * 小程序域名
     */
    public static final String appDomain = "mpxcxtemplate.kuaidizs.cn";

    /**
     * 打印机型号
     */
    private String printerName;

    /**
     * 打印方式
     */
    private String printTypeMQ;

    /**
     * 订单来源
     */
    private String orderFrom;

    /**
     * 打印次数
     */
    private String printNums;

    /**
     * 用户qnquerystring信息
     */
    private String qnquerystring;

    /**
     * 网点名
     */
    private String branchName;

    /**
     * 网点代码
     */
    private String branchCode;

    /**
     * 网点省
     */
    private String branchProvince;

    /**
     * 网点市
     */
    private String branchCity;

    /**
     * 网点区
     */
    private String branchArea;

    /**
     * 网点镇
     */
    private String branchTwon;

    /**
     * 网点详细地址
     */
    private String branchAddress;

    public static String getAppDomain() {
        return appDomain;
    }

    public String getPrinterName() {
        return printerName;
    }

    public void setPrinterName(String printerName) {
        this.printerName = printerName;
    }

    public String getPrintTypeMQ() {
        return printTypeMQ;
    }

    public void setPrintTypeMQ(String printTypeMQ) {
        this.printTypeMQ = printTypeMQ;
    }

    public String getOrderFrom() {
        return orderFrom;
    }

    public void setOrderFrom(String orderFrom) {
        this.orderFrom = orderFrom;
    }

    public String getPrintNums() {
        return printNums;
    }

    public void setPrintNums(String printNums) {
        this.printNums = printNums;
    }

    public String getQnquerystring() {
        return qnquerystring;
    }

    public void setQnquerystring(String qnquerystring) {
        this.qnquerystring = qnquerystring;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getBranchProvince() {
        return branchProvince;
    }

    public void setBranchProvince(String branchProvince) {
        this.branchProvince = branchProvince;
    }

    public String getBranchCity() {
        return branchCity;
    }

    public void setBranchCity(String branchCity) {
        this.branchCity = branchCity;
    }

    public String getBranchArea() {
        return branchArea;
    }

    public void setBranchArea(String branchArea) {
        this.branchArea = branchArea;
    }

    public String getBranchTwon() {
        return branchTwon;
    }

    public void setBranchTwon(String branchTwon) {
        this.branchTwon = branchTwon;
    }

    public String getBranchAddress() {
        return branchAddress;
    }

    public void setBranchAddress(String branchAddress) {
        this.branchAddress = branchAddress;
    }
}
