package com.kuaidizs.jxc.domain.ai;

import com.kuaidizs.jxc.common.enums.ai.AiSortTradeCategoryEnum;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
public class AIPrintRuleDetail implements Serializable {

    private int id;

    /**
     * 打印规则
     */
    private String printRuleDetail;

    private String printRuleDesc;

    private AiSortTradeCategoryEnum  category;

    /**
     * 是否选中
     * 0：未选中；1：选中
     */
    private int isCheck;

    public AIPrintRuleDetail() {
    }

    public AIPrintRuleDetail(Builder builder) {
        this.id = builder.id;
        this.printRuleDetail = builder.printRuleDetail;
        this.printRuleDesc = builder.printRuleDesc;
        this.isCheck = builder.isCheck;
    }

    public static class Builder {
        private int id;

        private String printRuleDetail;

        private String printRuleDesc;

        private int isCheck;

        public Builder id(int id) {
            this.id = id;
            return this;
        }

        public Builder printRuleDetail(String printRuleDetail) {
            this.printRuleDetail = printRuleDetail;
            return this;
        }

        public Builder printRuleDesc(String printRuleDesc) {
            this.printRuleDesc = printRuleDesc;
            return this;
        }

        public Builder isCheck(int isCheck) {
            this.isCheck = isCheck;
            return this;
        }

        public AIPrintRuleDetail build() {
            return new AIPrintRuleDetail(this);
        }
    }

    @Override
    public String toString() {
        return "AIPrintRuleDetail{" +
                "id=" + id +
                ", printRuleDetail='" + printRuleDetail + '\'' +
                ", isCheck=" + isCheck +
                '}';
    }
}
