package com.kuaidizs.jxc.domain.log;

import java.io.Serializable;
import java.util.Date;

/**
 * @auther xudaomeng
 * @since 2020-09-02 17:31
 */
public class DecryptLog implements Serializable {

    /**
     *序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;
    /**
     * 登录者淘宝id
     */
    private Long taobaoId;
    /**
     * 订单号
     */
    private String tid;
    /**
     * content
     */
    private String content;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 操作者ip
     */
    private String ip;
    /**
     * 1:可用,0:删除
     */
    private Integer enableStatus;
    /**
     * 添加时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 1:普通解密 2：接口解密
     */
    private Integer type;


    /**
     * @return id id
     */
    public Long getId() {
        return id;
    }
    /**
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return taobaoId 登录者淘宝id
     */
    public Long getTaobaoId() {
        return taobaoId;
    }
    /**
     * @param taobaoId 登录者淘宝id
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return tid 订单号
     */
    public String getTid() {
        return tid;
    }
    /**
     * @param tid 订单号
     */
    public void setTid(String tid) {
        this.tid = tid;
    }

    /**
     * @return content content
     */
    public String getContent() {
        return content;
    }
    /**
     * @param content content
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * @return operator 操作人
     */
    public String getOperator() {
        return operator;
    }
    /**
     * @param operator 操作人
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * @return ip 操作者ip
     */
    public String getIp() {
        return ip;
    }
    /**
     * @param ip 操作者ip
     */
    public void setIp(String ip) {
        this.ip = ip;
    }

    /**
     * @return enableStatus 1:可用,0:删除
     */
    public Integer getEnableStatus() {
        return enableStatus;
    }
    /**
     * @param enableStatus 1:可用,0:删除
     */
    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return created 添加时间
     */
    public Date getCreated() {
        return created;
    }
    /**
     * @param created 添加时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }
    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * 分表使用
     */
    private String tableName;

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    /**
     * 分库标识
     */
    private String fkId;

    public String getFkId() {
        return fkId;
    }

    public void setFkId(String fkId) {
        this.fkId = fkId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}
