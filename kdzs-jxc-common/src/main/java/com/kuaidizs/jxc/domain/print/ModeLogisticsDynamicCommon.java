package com.kuaidizs.jxc.domain.print;

import java.io.Serializable;
import java.util.Date;

/**
 * 模板动态化公共服务
 *
 * <AUTHOR>
 * Date    2018-09-11
 */
public class ModeLogisticsDynamicCommon implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键Id
     */
    private Long id;
    /**
     * 快递公司code
     */
    private String excode;
    /**
     * 服务编码
     */
    private String serviceCode;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * 逻辑状态：0删除，1使用
     */
    private Integer enableStatus;
    /**
     * 快递公司名称
     */
    private String excodeName;
    /**
     * 快递类型:
     * 普通面单:1
     * 网点面单:2
     * 菜鸟面单:3
     */
    private Integer expressType;
    /**
     * 面单样式 styleId
     */
    private String expressStyle;
    /**
     * 自定义服务详情描述
     */
    private String serviceDesc;
    /**
     * 原始结构描述
     */
    private String originValue;
    /**
     * 渲染状态：1，渲染默认值；2，渲染自定义值
     * 默认值：1
     */
    private Integer customStatus;
    /**
     * 自定义渲染内容
     */
    private String customValue;
    /**
     * 服务状态，是否展现，是否渲染等等
     * 默认值1
     * 0 已添加了服务，但是菜鸟做了下线处理操作
     * 1 展示，用户不可以添加，需要系统确认
     * 2 展示，用户可以添加，正常操作
     */
    private Integer serviceStatus;
    /**
     * 其他服务属性
     */
    private String serviceProperty;
    /**
     * 排序id
     */
    private Integer sortId;


    /**
     * @return id 主键Id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id 主键Id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return excode 快递公司code
     */
    public String getExcode() {
        return excode;
    }

    /**
     * @param excode 快递公司code
     */
    public void setExcode(String excode) {
        this.excode = excode;
    }

    /**
     * @return serviceCode 服务编码
     */
    public String getServiceCode() {
        return serviceCode;
    }

    /**
     * @param serviceCode 服务编码
     */
    public void setServiceCode(String serviceCode) {
        this.serviceCode = serviceCode;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return enableStatus 逻辑状态：0删除，1使用
     */
    public Integer getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 逻辑状态：0删除，1使用
     */
    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return excodeName 快递公司名称
     */
    public String getExcodeName() {
        return excodeName;
    }

    /**
     * @param excodeName 快递公司名称
     */
    public void setExcodeName(String excodeName) {
        this.excodeName = excodeName;
    }

    /**
     * @return expressType 快递类型:普通面单:1,网点面单:2;菜鸟面单:3
     */
    public Integer getExpressType() {
        return expressType;
    }

    /**
     * @param expressType 快递类型:普通面单:1,网点面单:2;菜鸟面单:3
     */
    public void setExpressType(Integer expressType) {
        this.expressType = expressType;
    }

    /**
     * @return expressStyle 面单样式
     */
    public String getExpressStyle() {
        return expressStyle;
    }

    /**
     * @param expressStyle 面单样式
     */
    public void setExpressStyle(String expressStyle) {
        this.expressStyle = expressStyle;
    }

    public String getServiceDesc() {
        return serviceDesc;
    }

    public void setServiceDesc(String serviceDesc) {
        this.serviceDesc = serviceDesc;
    }

    /**
     * @return originValue 原始结构描述
     */
    public String getOriginValue() {
        return originValue;
    }

    /**
     * @param originValue 原始结构描述
     */
    public void setOriginValue(String originValue) {
        this.originValue = originValue;
    }

    /**
     * @return customStatus 渲染状态：1，渲染默认值；2，渲染自定义值
     */
    public Integer getCustomStatus() {
        return customStatus;
    }

    /**
     * @param customStatus 渲染状态：1，渲染默认值；2，渲染自定义值
     */
    public void setCustomStatus(Integer customStatus) {
        this.customStatus = customStatus;
    }

    /**
     * @return customValue 自定义渲染内容
     */
    public String getCustomValue() {
        return customValue;
    }

    /**
     * @param customValue 自定义渲染内容
     */
    public void setCustomValue(String customValue) {
        this.customValue = customValue;
    }

    /**
     * @return serviceStatus 服务状态，是否展现，是否渲染等等
     */
    public Integer getServiceStatus() {
        return serviceStatus;
    }

    /**
     * @param serviceStatus 服务状态，是否展现，是否渲染等等
     */
    public void setServiceStatus(Integer serviceStatus) {
        this.serviceStatus = serviceStatus;
    }

    /**
     * @return serviceProperty 服务订购需求状态
     */
    public String getServiceProperty() {
        return serviceProperty;
    }

    /**
     * @param serviceProperty 服务订购需求状态
     */
    public void setServiceProperty(String serviceProperty) {
        this.serviceProperty = serviceProperty;
    }

    /**
     * @return sortId 排序id
     */
    public Integer getSortId() {
        return sortId;
    }

    /**
     * @param sortId 排序id
     */
    public void setSortId(Integer sortId) {
        this.sortId = sortId;
    }

}