package com.kuaidizs.jxc.domain.item;

import java.io.Serializable;

/**
 * Created by john on 2018/3/5.
 *
 * <AUTHOR>
 * @since 2018/3/5
 */
public class GoodsInfo implements Serializable {
    public String getIndex() {
        return index;
    }

    public void setIndex(String index) {
        this.index = index;
    }


    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getShortTitle() {
        return shortTitle;
    }

    public void setShortTitle(String shortTitle) {
        this.shortTitle = shortTitle;
    }

    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public String getSkuAlias() {
        return skuAlias;
    }

    public void setSkuAlias(String skuAlias) {
        this.skuAlias = skuAlias;
    }

    public String getWeight() {
        return weight;
    }

    public void setWeight(String weight) {
        this.weight = weight;
    }

    public String getPicUrl() {
        return picUrl;
    }

    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    public String getNumIid() {
        return numIid;
    }

    public void setNumIid(String numIid) {
        this.numIid = numIid;
    }

    /**
     * 宝贝序号
     */
    private String index;

    /**
     * 商品id
     */
    private String numIid;


    /**
     * 图片地址
     */
    private String picUrl;

    /**
     * 宝贝标题
     */
    private String title;
    /**
     * 所属店铺
     */
    private String shopName;
    /**
     * 宝贝简称
     */
    private String shortTitle;
    /**
     * 规格名称
     */
    private String skuName;
    /**
     * 规格别名
     */
    private String skuAlias;
    /**
     * 重量
     */
    private String weight;
    /**
     * 商家编码
     */
    private String outerId;
    /**
     * 市场
     */
    private String market;
    /**
     * 档口
     */
    private String stall;
    /**
     * 成本价
     */
    private String cost;

    /**
     * 规格id
     */
    private String skuId;

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getMarket() {
        return market;
    }

    public void setMarket(String market) {
        this.market = market;
    }

    public String getStall() {
        return stall;
    }

    public void setStall(String stall) {
        this.stall = stall;
    }

    public String getCost() {
        return cost;
    }

    public void setCost(String cost) {
        this.cost = cost;
    }

    public String getOuterId() {
        return outerId;
    }

    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }
}
