package com.kuaidizs.jxc.domain.express;

import java.io.Serializable;
import java.util.Date;


public class GoodsWeightExpresssVO implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    private String weightMin;

    private String weightMax;

    private String weightExName;

    private String weightExCode;

    private Date created;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getWeightMin() {
        return weightMin;
    }

    public void setWeightMin(String weightMin) {
        this.weightMin = weightMin;
    }

    public String getWeightMax() {
        return weightMax;
    }

    public void setWeightMax(String weightMax) {
        this.weightMax = weightMax;
    }

    public String getWeightExName() {
        return weightExName;
    }

    public void setWeightExName(String weightExName) {
        this.weightExName = weightExName;
    }

    public String getWeightExCode() {
        return weightExCode;
    }

    public void setWeightExCode(String weightExCode) {
        this.weightExCode = weightExCode;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }
}
