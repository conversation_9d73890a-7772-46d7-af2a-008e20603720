package com.kuaidizs.jxc.domain.print;

import java.io.Serializable;
import java.util.Date;

/**
 * 打印错误监控提醒
 *
 * <AUTHOR>
 * @date 2018-01-04
 */
public class PrintErrorNotify implements Serializable {

    /**
     * 任务失败，通用错误
     */
    public static final String TASK_FAILURE = "task_failure";

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键Id
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * 逻辑状态：0删除，1使用
     */
    private Integer enableStatus;
    /**
     * 当前用户taobaoId
     */
    private Long taobaoId;
    /**
     * 异常运单号
     */
    private String waybills;
    /**
     * 异常订单号
     */
    private String tids;
    /**
     * 异常数量
     */
    private Integer number;
    /**
     * 操作来源 0：单打 1：批打 2：预发货 3：自动预发货 4：手工订单
     */
    private Integer optionType;
    /**
     * 打印开始时间
     */
    private Date printStartTime;
    /**
     * 打印机
     */
    private String printer;
    /**
     * 处理状态
     */
    private Integer handleStatus;
    /**
     * 任务状态情况
     */
    private String taskCondition;
    /**
     * 异常原因
     */
    private String errorMessage;
    /**
     * 打印详细信息
     */
    private String printDetail;

    /**
     * 分表使用
     */
    private String tableName;

    private String fkId;

    /**
     * @return id 主键Id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id 主键Id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return enableStatus 逻辑状态：0删除，1使用
     */
    public Integer getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 逻辑状态：0删除，1使用
     */
    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return taobaoId 当前用户taobaoId
     */
    public Long getTaobaoId() {
        return taobaoId;
    }

    /**
     * @param taobaoId 当前用户taobaoId
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return waybills 异常运单号
     */
    public String getWaybills() {
        return waybills;
    }

    /**
     * @param waybills 异常运单号
     */
    public void setWaybills(String waybills) {
        this.waybills = waybills;
    }

    /**
     * @return tids 异常订单号
     */
    public String getTids() {
        return tids;
    }

    /**
     * @param tids 异常订单号
     */
    public void setTids(String tids) {
        this.tids = tids;
    }

    /**
     * @return number 异常数量
     */
    public Integer getNumber() {
        return number;
    }

    /**
     * @param number 异常数量
     */
    public void setNumber(Integer number) {
        this.number = number;
    }

    /**
     * @return optionType 操作来源 0：单打 1：批打 2：预发货 3：自动预发货 4：手工订单
     */
    public Integer getOptionType() {
        return optionType;
    }

    /**
     * @param optionType 操作来源 0：单打 1：批打 2：预发货 3：自动预发货 4：手工订单
     */
    public void setOptionType(Integer optionType) {
        this.optionType = optionType;
    }

    /**
     * @return printStartTime 打印开始时间
     */
    public Date getPrintStartTime() {
        return printStartTime;
    }

    /**
     * @param printStartTime 打印开始时间
     */
    public void setPrintStartTime(Date printStartTime) {
        this.printStartTime = printStartTime;
    }

    /**
     * @return printer 打印机
     */
    public String getPrinter() {
        return printer;
    }

    /**
     * @param printer 打印机
     */
    public void setPrinter(String printer) {
        this.printer = printer;
    }

    /**
     * @return handleStatus 处理状态
     */
    public Integer getHandleStatus() {
        return handleStatus;
    }

    /**
     * @param handleStatus 处理状态
     */
    public void setHandleStatus(Integer handleStatus) {
        this.handleStatus = handleStatus;
    }

    /**
     * @return taskCondition 任务状态情况
     */
    public String getTaskCondition() {
        return taskCondition;
    }

    /**
     * @param taskCondition 任务状态情况
     */
    public void setTaskCondition(String taskCondition) {
        this.taskCondition = taskCondition;
    }

    /**
     * @return errorMessage 异常原因
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * @param errorMessage 异常原因
     */
    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    /**
     * @return printDetail 打印详细信息
     */
    public String getPrintDetail() {
        return printDetail;
    }

    /**
     * @param printDetail 打印详细信息
     */
    public void setPrintDetail(String printDetail) {
        this.printDetail = printDetail;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getFkId() {
        return fkId;
    }

    public void setFkId(String fkId) {
        this.fkId = fkId;
    }
}