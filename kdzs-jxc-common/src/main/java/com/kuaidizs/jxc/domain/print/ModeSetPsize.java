package com.kuaidizs.jxc.domain.print;

import java.util.*;
import java.io.Serializable;

/**
 *
 * <AUTHOR> @date    2016-07-26
 */
public class ModeSetPsize implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 自增ID
     */
    private Integer psizeId;
	/**
     * 用户ID
     */
    private Long taobaoId;
	/**
     * 创建时间
     */
    private Date created;
	/**
     * 修改时间
     */
    private Date modified;
	/**
     * 逻辑删除
     */
    private Boolean enableStatus;
	/**
     * mode_listshow主键id
     */
    private Integer modeListShowId;
	/**
     * 打印机名称
     */
    private String printName;
	/**
     * 纸张宽度
     */
    private String width;
	/**
     * 纸张高度
     */
    private String height;
	/**
     * 是否共享打印机
     */
    private Integer isShare;

	
   /**
    * @return psizeId 自增ID
    */
    public Integer getPsizeId() {
       return psizeId;
    }
   /**
    * @param psizeId 自增ID
    */
    public void setPsizeId(Integer psizeId) {
       this.psizeId = psizeId;
    }
	
   /**
    * @return taobaoId 用户ID
    */
    public Long getTaobaoId() {
       return taobaoId;
    }
   /**
    * @param taobaoId 用户ID
    */
    public void setTaobaoId(Long taobaoId) {
       this.taobaoId = taobaoId;
    }
	
   /**
    * @return created 创建时间
    */
    public Date getCreated() {
       return created;
    }
   /**
    * @param created 创建时间
    */
    public void setCreated(Date created) {
       this.created = created;
    }
	
   /**
    * @return modified 修改时间
    */
    public Date getModified() {
       return modified;
    }
   /**
    * @param modified 修改时间
    */
    public void setModified(Date modified) {
       this.modified = modified;
    }
	
   /**
    * @return enableStatus 逻辑删除
    */
    public Boolean getEnableStatus() {
       return enableStatus;
    }
   /**
    * @param enableStatus 逻辑删除
    */
    public void setEnableStatus(Boolean enableStatus) {
       this.enableStatus = enableStatus;
    }
	
   /**
    * @return modeListShowId mode_listshow主键id
    */
    public Integer getModeListShowId() {
       return modeListShowId;
    }
   /**
    * @param modeListShowId mode_listshow主键id
    */
    public void setModeListShowId(Integer modeListShowId) {
       this.modeListShowId = modeListShowId;
    }
	
   /**
    * @return printName 打印机名称
    */
    public String getPrintName() {
       return printName;
    }
   /**
    * @param printName 打印机名称
    */
    public void setPrintName(String printName) {
       this.printName = printName;
    }
	
   /**
    * @return width 纸张宽度
    */
    public String getWidth() {
       return width;
    }
   /**
    * @param width 纸张宽度
    */
    public void setWidth(String width) {
       this.width = width;
    }
	
   /**
    * @return height 纸张高度
    */
    public String getHeight() {
       return height;
    }
   /**
    * @param height 纸张高度
    */
    public void setHeight(String height) {
       this.height = height;
    }
	
   /**
    * @return isShare 是否共享打印机
    */
    public Integer getIsShare() {
       return isShare;
    }
   /**
    * @param isShare 是否共享打印机
    */
    public void setIsShare(Integer isShare) {
       this.isShare = isShare;
    }

}