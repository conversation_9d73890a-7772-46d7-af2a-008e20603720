package com.kuaidizs.jxc.domain.print.vo;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;
import java.util.List;

/**
 * 模板排序
 *
 * <AUTHOR>
 * @date 2018/8/22
 */
public class SortTemplateVo implements Serializable {

    @JSONField(name = "DelIds")
    private List<Long> deleteTemplateIds;

    @JSONField(name = "kddType")
    private String kddType;

    @JSONField(name = "SortTempList")
    private List<SortTemplate> sortTemplates;

    public List<Long> getDeleteTemplateIds() {
        return deleteTemplateIds;
    }

    public void setDeleteTemplateIds(List<Long> deleteTemplateIds) {
        this.deleteTemplateIds = deleteTemplateIds;
    }

    public String getKddType() {
        return kddType;
    }

    public void setKddType(String kddType) {
        this.kddType = kddType;
    }

    public List<SortTemplate> getSortTemplates() {
        return sortTemplates;
    }

    public void setSortTemplates(List<SortTemplate> sortTemplates) {
        this.sortTemplates = sortTemplates;
    }

    public static class SortTemplate implements Serializable {
        @JSONField(name = "Id")
        private Integer templateId;
        @JSONField(name = "SortId")
        private Integer sortId;

        public Integer getTemplateId() {
            return templateId;
        }

        public void setTemplateId(Integer templateId) {
            this.templateId = templateId;
        }

        public Integer getSortId() {
            return sortId;
        }

        public void setSortId(Integer sortId) {
            this.sortId = sortId;
        }
    }

}
