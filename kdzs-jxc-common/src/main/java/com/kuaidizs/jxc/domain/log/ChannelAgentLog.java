package com.kuaidizs.jxc.domain.log;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * Date    2023-04-18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChannelAgentLog implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * 操作人用户ID
     */
    private Long operatorId;
    /**
     * 代理商用户ID
     */
    private Long agentUserId;
    /**
     * 应用活动ID
     */
    private Long itemId;
    /**
     * 平台
     */
    private String platform;
    /**
     * 小程序下用户标识
     */
    private String openId;
    /**
     * 淘宝昵称
     */
    private String nick;
    /**
     * 时间
     */
    private Date time;
    /**
     * 上次过期时间，如果是新用户可以为空
     */
    private Date lastEndTime;
    /**
     * 淘宝订购记录
     */
    private String order;
}