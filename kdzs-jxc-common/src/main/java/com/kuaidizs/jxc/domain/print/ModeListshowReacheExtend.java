package com.kuaidizs.jxc.domain.print;

import java.io.Serializable;
import java.util.Calendar;
import java.util.Date;

/**
 * @Intro: 模版扩展表
 *
 * @Author: WangJiongDa(yunkai)
 * @Date: 2019/5/21
 * @Time: 下午5:16
 */
public class ModeListshowReacheExtend implements Serializable {

    private static final long serialVersionUID = 5579280624156800849L;

    private Integer id;

    /**
     * 模版Id
     */
    private Integer modeListshowId;

    /**
     * 用户Id
     */
    private Long taobaoId;

    /**
     * 模版是否开启圆通樱桃可达判断 1、开启;0、不开启
     */
    private Integer status;

    /**
     * 发货小标签中有相同的商品是否合并 0、不开启;1、开启
     */
    private Integer fhdIsMerge;

    /**
     * 添加时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified = Calendar.getInstance().getTime();

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getModeListshowId() {
        return modeListshowId;
    }

    public void setModeListshowId(Integer modeListshowId) {
        this.modeListshowId = modeListshowId;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getFhdIsMerge() {
        return fhdIsMerge;
    }

    public void setFhdIsMerge(Integer fhdIsMerge) {
        this.fhdIsMerge = fhdIsMerge;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }
}
