package com.kuaidizs.jxc.domain.print.enums;

import com.kuaidizs.jxc.common.util.ExServiceItem;
import com.kuaidizs.jxc.common.util.ExServiceItemModel;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 菜鸟就顺丰一家有个性化增值服务，可预见未来都不会有太多变化，直接写在内存中，没必要专门创建一张新表或修改网点表的逻辑(无法兼容)
 * 未来市场发生变化，菜鸟各家增值服务明显增多，再将数据落库
 */
public enum EnumCnExAdvancedServices {

    ONE("SF", 1, "zmj", "子母件", "子母件", "子母件", "1", "2"),
    TWO("SF", 2, "fkfs", "付款方式", "现结|到付|第三方付", "付款方式", "1", "1"),
    THREE("SF", 3, "yjkh", "月结卡号", "#1#1", "月结卡号", "1", "3"),
    FOUR("SF", 4, "khbm", "客户编码", "#1#1", "客户编码", "1", "1"),
    ;

    //后面接第三方付改成    TWO("SF", 2, "fkfs", "付款方式", "现结|到付|第三方付#1#1", "付款方式", "1", "1"),
    private String code;

    private Integer id;

    private String key;

    private String name;

    private String value;

    private String tips;

    private String isEnable;

    private String isSetVal;

    EnumCnExAdvancedServices(String code, Integer id, String key, String name, String value, String tips, String isEnable, String isSetVal) {
        this.code = code;
        this.id = id;
        this.key = key;
        this.name = name;
        this.value = value;
        this.tips = tips;
        this.isEnable = isEnable;
        this.isSetVal = isSetVal;
    }

    public String getCode() {
        return code;
    }

    public Integer getId() {
        return id;
    }

    public String getKey() {
        return key;
    }

    public String getName() {
        return name;
    }

    public String getValue() {
        return value;
    }

    public String getTips() {
        return tips;
    }

    public String getIsEnable() {
        return isEnable;
    }

    public String getIsSetVal() {
        return isSetVal;
    }

    public static ExServiceItemModel getExAdvancedServicesByExCode(String exCode) {
        if (StringUtils.isBlank(exCode)) {
            return null;
        }
        List<EnumCnExAdvancedServices> list = new ArrayList<>();
        for (EnumCnExAdvancedServices value : EnumCnExAdvancedServices.values()) {
            if (value.getCode().equals(exCode)) {
                list.add(value);
            }
        }
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        ExServiceItemModel exServiceItemModel = new ExServiceItemModel();
        exServiceItemModel.setCode(exCode);
        List<ExServiceItem> exServiceItemList = new ArrayList<>();
        for (EnumCnExAdvancedServices value : list) {
            ExServiceItem exServiceItem = new ExServiceItem();
            exServiceItem.setId(value.getId());
            exServiceItem.setKey(value.getKey());
            exServiceItem.setName(value.getName());
            exServiceItem.setValue(value.getValue());
            exServiceItem.setTips(value.getTips());
            exServiceItem.setIsEnable(value.getIsEnable());
            exServiceItem.setIsSetVal(value.getIsSetVal());
            exServiceItemList.add(exServiceItem);
        }
        exServiceItemModel.setExServiceItemList(exServiceItemList);
        return exServiceItemModel;
    }

}
