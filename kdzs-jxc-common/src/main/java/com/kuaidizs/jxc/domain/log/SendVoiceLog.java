package com.kuaidizs.jxc.domain.log;

import java.io.Serializable;
import java.util.Date;

public class SendVoiceLog implements Serializable {
    private static final long serialVersionUID = -2563475495793457688L;

    private Long id;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * 逻辑删除状态0表示删除 1表示未删除
     */
    private Boolean enableStatus;
    /**
     * 淘宝id
     */
    private Long taobaoId;

    /**
     * 语音召回code
     */
    private String callId;

    /**
     * 语音召回请求
     */
    private String requestId;

    /**
     * version 可以是发送的时间或者版本
     */
    private String version;

    /**
     * 1:3天后过期的付费用户（非专业版）
     * 2:3天后过期的付费用户（专业版）
     * 3:过期7天的付费用户
     * 4:过期30天的付费用户
     */
    private Integer type;

    private String mobile;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public Boolean getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Boolean enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public String getCallId() {
        return callId;
    }

    public void setCallId(String callId) {
        this.callId = callId;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }


    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
}
