package com.kuaidizs.jxc.domain.logisticsUpgrade;

import com.kuaidizs.jxc.common.util.DateUtil;

import java.io.Serializable;
import java.util.Date;

/***
 * program: kdzs-jxc
 * description: 天猫物流升级信息
 * author: <PERSON><PERSON><PERSON><PERSON>@raycloud.com
 * create: 2021-12-14 15:38
 **/
public class LogisticsUpgrade implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 订单号
     */
    private Long tid;

    /**
     * 天猫送货上门快递名称
     */

    private String sendExName ;
    /**
     * 天猫送货上门快递编码
     */
    private String sendExCode;

    /**
     * asdp_biz_type ：value=logistics_upgrade 为天猫物流升级订单 、asdp_Ads：value=201 201为送货上⻔服务，两个字段的value均满足，则订单标识为「送货上门」；
     */
    private String asdpBizType;

    /**
     * value=201   里面多个值时用英文逗号隔开
     * 该字段可能存在同时传201和202的情况
     * value=202 202为顺丰配送服务
     * value=201 201为按需配送服务
     * value=203 203为承诺发货时效
     * value=204 204为承诺送达时效
     */
    private String asdpAds;

    /**
     * 最晚送达时间
     */
    private Date signTime ;

    /**
     * 最晚发货时间
     */
    private Date deliveryTime;

    /**
     * 承诺/最晚送达时间，日期，格式2019-04-12 16:00:00
     */
    private Date promiseSignTime;

    private Date promiseCollectTime;

    /**
     * 是否主订单
     */
    private Boolean isMain;

    private Date collectTime;

    public Date getCollectTime() {
        return collectTime;
    }

    public void setCollectTime(Date collectTime) {
        this.collectTime = collectTime;
    }

    public Date getPromiseSignTime() {
        return promiseSignTime;
    }

    public void setPromiseSignTime(Date promiseSignTime) {
        this.promiseSignTime = promiseSignTime;
    }

    public Long getTid() {
        return tid;
    }

    public void setTid(Long tid) {
        this.tid = tid;
    }

    public String getSendExName() {
        return sendExName;
    }

    public void setSendExName(String sendExName) {
        this.sendExName = sendExName;
    }

    public String getSendExCode() {
        return sendExCode;
    }

    public void setSendExCode(String sendExCode) {
        this.sendExCode = sendExCode;
    }

    public String getAsdpBizType() {
        return asdpBizType;
    }

    public void setAsdpBizType(String asdpBizType) {
        this.asdpBizType = asdpBizType;
    }

    public String getAsdpAds() {
        return asdpAds;
    }

    public void setAsdpAds(String asdpAds) {
        this.asdpAds = asdpAds;
    }

    public Date getSignTime() {
        return signTime;
    }

    public void setSignTime(Date signTime) {
        this.signTime = signTime;
    }

    public Date getDeliveryTime() {
        return deliveryTime;
    }

    public void setDeliveryTime(Date deliveryTime) {
        this.deliveryTime = deliveryTime;
    }

    public Boolean getMain() {
        return isMain;
    }

    public void setMain(Boolean main) {
        isMain = main;
    }

    public Date getPromiseCollectTime() {
        return promiseCollectTime;
    }

    public void setPromiseCollectTime(Date promiseCollectTime) {
        this.promiseCollectTime = promiseCollectTime;
    }

    @Override
    public String toString() {
        return "LogisticsUpgrade{" +
                "tid=" + tid +
                ", sendExName='" + sendExName + '\'' +
                ", sendExCode='" + sendExCode + '\'' +
                ", asdpBizType='" + asdpBizType + '\'' +
                ", asdpAds='" + asdpAds + '\'' +
                ", signTime=" + signTime +
                ", deliveryTime=" + deliveryTime +
                ", promiseSignTime=" + promiseSignTime +
                ", promiseCollectTime=" + promiseCollectTime +
                ", isMain=" + isMain +
                ", collectTime=" + collectTime +
                '}';
    }
}
