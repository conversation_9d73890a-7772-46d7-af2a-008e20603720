package com.kuaidizs.jxc.domain.print;

import java.util.*;
import java.io.Serializable;

/**
 *
 * <AUTHOR> @date    2016-07-28
 */
public class ModeLogo implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * id
     */
    private Integer id;
	/**
     * 用户ID
     */
    private Long taobaoId;
	/**
     * logo图片地址
     */
    private String logoSrc;
	/**
     * 宽度
     */
    private Integer width;
	/**
     * 高度
     */
    private Integer height;

	
   /**
    * @return id id
    */
    public Integer getId() {
       return id;
    }
   /**
    * @param id id
    */
    public void setId(Integer id) {
       this.id = id;
    }
	
   /**
    * @return taobaoId 用户ID
    */
    public Long getTaobaoId() {
       return taobaoId;
    }
   /**
    * @param taobaoId 用户ID
    */
    public void setTaobaoId(Long taobaoId) {
       this.taobaoId = taobaoId;
    }
	
   /**
    * @return logoSrc logo图片地址
    */
    public String getLogoSrc() {
       return logoSrc;
    }
   /**
    * @param logoSrc logo图片地址
    */
    public void setLogoSrc(String logoSrc) {
       this.logoSrc = logoSrc;
    }
	
   /**
    * @return width 宽度
    */
    public Integer getWidth() {
       return width;
    }
   /**
    * @param width 宽度
    */
    public void setWidth(Integer width) {
       this.width = width;
    }
	
   /**
    * @return height 高度
    */
    public Integer getHeight() {
       return height;
    }
   /**
    * @param height 高度
    */
    public void setHeight(Integer height) {
       this.height = height;
    }

}