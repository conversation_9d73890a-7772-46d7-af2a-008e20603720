package com.kuaidizs.jxc.domain.addressModify;

import java.io.Serializable;
import java.util.List;

/**
 * @auther xudaomeng
 * @since 2021-03-03 16:21
 */
public class AddressModifyRequest implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 校验类型,0:地址修改校验.1:sku地址修改校验，2:均要校验.默认0
     */
    private int type;

    private Long taobaoId;

    private List<AddressModifyLogVO> addressModifyLogVOList;

    public int getType() {
        return type;
    }

    public void setType(int type) {
        this.type = type;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public List<AddressModifyLogVO> getAddressModifyLogVOList() {
        return addressModifyLogVOList;
    }

    public void setAddressModifyLogVOList(List<AddressModifyLogVO> addressModifyLogVOList) {
        this.addressModifyLogVOList = addressModifyLogVOList;
    }
}
