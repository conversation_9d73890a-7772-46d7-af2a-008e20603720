package com.kuaidizs.jxc.domain.print.enums;

/**
 * 助手快递公司编码转换为菜鸟cp编码枚举
 */
public enum EnumKdCodeToCnCpCode {

    /**
     * 顺丰速运
     */
    SF("SF", "SF"),

    /**
     * 丰网速运
     */
    FW("FENGWANG", "SF"),

    /**
     * 顺丰快运
     */
    SFKY("LE03904430", "SF");

    private String kdCode;

    private String cpCode;

    EnumKdCodeToCnCpCode(String kdCode, String cpCode) {
        this.kdCode = kdCode;
        this.cpCode = cpCode;
    }

    public String getKdCode() {
        return kdCode;
    }

    public String getCpCode() {
        return cpCode;
    }

    public static String getCpCodeByKdCode(String kdCode) {
        for (EnumKdCodeToCnCpCode value : EnumKdCodeToCnCpCode.values()) {
            if (value.getKdCode().equals(kdCode)) {
                return value.getCpCode();
            }
        }
        return kdCode;
    }

    /**
     * 是否支持批量下单
     *
     * @param kdCode
     * @return
     */
    public static boolean isBatchGet(String kdCode) {
        return !SF.getKdCode().equals(kdCode) && !FW.getKdCode().equals(kdCode) && !SFKY.getKdCode().equals(kdCode);
    }

}
