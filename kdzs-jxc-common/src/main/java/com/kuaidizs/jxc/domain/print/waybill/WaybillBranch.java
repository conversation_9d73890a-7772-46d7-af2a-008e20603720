package com.kuaidizs.jxc.domain.print.waybill;

import com.alibaba.fastjson.annotation.JSONField;
import com.taobao.api.response.CainiaoWaybillIiSearchResponse;

import java.io.Serializable;
import java.util.List;

/**
 * 电子面单网点信息
 *
 * <AUTHOR>    Date:2016/7/30  14:50
 */
public class WaybillBranch implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 商家ID
     */
    @JSONField(name = "SellerId")
    private Long sellerId;

    /**
     * 网点ID
     */
    @JSONField(name = "BranchCode")
    private String branchCode;
    /**
     * 网点名称
     */
    @JSONField(name = "BranchName")
    private String branchName;

    /**
     * 已用单数
     */
    @JSONField(name = "AllocatedQuantity")
    private Long allocatedQuantity;

    /**
     * 取消的面对总数
     */
    @JSONField(name = "CancelQuantity")
    private Long cancelQuantity;

    /**
     * 物流服务商ID
     */
    @JSONField(name = "CpCode")
    private String cpCode;

    /**
     * 网点类型  1是直营，2是加盟
     */
    @JSONField(name = "CpType")
    private Long cpType;

    /**
     * 已经打印的面单总数
     */
    @JSONField(name = "PrintQuantity")
    private Long printQuantity;

    /**
     * 可用单数
     */
    @JSONField(name = "Quantity")
    private Long quantity;

    /**
     * 号段
     */
    @JSONField(name = "segmentCode")
    private String segmentCode;

    private String segmentCodeName;
    /**
     * 网点地址信息
     */
    @JSONField(name = "AddressList")
    private List<WaybillBranchAddress> waybillBranchAddressList;

    /**
     * 用户开通的物流服务
     */
    @JSONField(name = "serviceInfoCols")
    private List<CainiaoWaybillIiSearchResponse.ServiceInfoDto> serviceInfoList;

    /**
     * 品牌
     */
    @JSONField(name = "brandCode")
    private String brandCode;

    /**
     * 当前单号是否是别的店铺分享的 0:不是 1:是
     */
    private Integer shared = 0;

    /**
     * 月结卡号列表
     */
    @JSONField(name = "customerCodeList")
    private List<String> customerCodeList;

    public Long getSellerId() {
        return sellerId;
    }

    public void setSellerId(Long sellerId) {
        this.sellerId = sellerId;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public Long getAllocatedQuantity() {
        return allocatedQuantity;
    }

    public void setAllocatedQuantity(Long allocatedQuantity) {
        this.allocatedQuantity = allocatedQuantity;
    }

    public Long getCancelQuantity() {
        return cancelQuantity;
    }

    public void setCancelQuantity(Long cancelQuantity) {
        this.cancelQuantity = cancelQuantity;
    }

    public String getCpCode() {
        return cpCode;
    }

    public void setCpCode(String cpCode) {
        this.cpCode = cpCode;
    }

    public Long getCpType() {
        return cpType;
    }

    public void setCpType(Long cpType) {
        this.cpType = cpType;
    }

    public Long getPrintQuantity() {
        return printQuantity;
    }

    public void setPrintQuantity(Long printQuantity) {
        this.printQuantity = printQuantity;
    }

    public Long getQuantity() {
        return quantity;
    }

    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    public List<WaybillBranchAddress> getWaybillBranchAddressList() {
        return waybillBranchAddressList;
    }

    public void setWaybillBranchAddressList(List<WaybillBranchAddress> waybillBranchAddressList) {
        this.waybillBranchAddressList = waybillBranchAddressList;
    }

    public List<CainiaoWaybillIiSearchResponse.ServiceInfoDto> getServiceInfoList() {
        return serviceInfoList;
    }

    public void setServiceInfoList(List<CainiaoWaybillIiSearchResponse.ServiceInfoDto> serviceInfoList) {
        this.serviceInfoList = serviceInfoList;
    }

    public String getSegmentCode() {
        return segmentCode;
    }

    public void setSegmentCode(String segmentCode) {
        this.segmentCode = segmentCode;
    }

    public String getSegmentCodeName() {
        return segmentCodeName;
    }

    public void setSegmentCodeName(String segmentCodeName) {
        this.segmentCodeName = segmentCodeName;
    }

    public String getBrandCode() {
        return brandCode;
    }

    public void setBrandCode(String brandCode) {
        this.brandCode = brandCode;
    }

    public List<String> getCustomerCodeList() {
        return customerCodeList;
    }

    public void setCustomerCodeList(List<String> customerCodeList) {
        this.customerCodeList = customerCodeList;
    }

    public Integer getShared() {
        return shared;
    }

    public void setShared(Integer shared) {
        this.shared = shared;
    }
}
