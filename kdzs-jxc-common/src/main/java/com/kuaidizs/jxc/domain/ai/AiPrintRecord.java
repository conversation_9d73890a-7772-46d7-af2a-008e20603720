package com.kuaidizs.jxc.domain.ai;

import cn.hutool.crypto.SecureUtil;
import com.kuaidizs.jxc.common.enums.ai.AiSortTradeCategoryEnum;
import com.kuaidizs.jxc.common.enums.ai.PrintStatus;
import com.kuaidizs.jxc.common.enums.ai.TradeStatus;
import com.kuaidizs.jxc.common.util.StringUtil;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

@Data
public class AiPrintRecord {
    /**
     * 主键
     */
    private Long id;
    /**
     * 任务id
     */
    private Long taskId;
    /**
     * 订单所属用户ID
     */
    private Long userId;
    /**
     * 创建该记录的用户ID
     */
    private Long createUserId;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 打印时间
     */
    private Date printTime;

    /**
     * 快递名称集合(按顺序，不去重，跟expressNos对应)
     */
    private List<String> expressNames;
    /**
     * 快递单号集合
     */
    private List<String> expressNos;

    /**
     * 打印状态
     */
    private PrintStatus printStatus;

    /**
     * 订单状态
     */
    private TradeStatus tradeStatus;

    /**
     * 异常原因
     */
    private String errorMsg;

    /**
     * 收件人姓名
     */
    private String receiverName;

    /**
     * 收件人手机号
     */
    private String receiverMobile;

    /**
     * 收件人省
     */
    private String receiverProvince;

    /**
     * 收件人市
     */
    private String receiverCity;

    /**
     * 收件人区
     */
    private String receiverDistrict;

    /**
     * 收件人详细地址
     */
    private String receiverAddress;

    /**
     * 付款时间
     */
    private Date payTime;

    /**
     * 打印批次号
     */
    private String batchNo;

    /**
     * 打印序号
     */
    private String printNo;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 订单号集合
     */
    private List<String> tids;

    /**
     * 订单所属分类
     */
    private AiSortTradeCategoryEnum category;

    /**
     * 订单所属分类中文描述
     */
    private String categoryDesc;

    /**
     * 卖家备注
     */
    private List<String> sellerMemos;

    /**
     * 买家留言
     */
    private List<String> buyerMessages;

    /**
     * 订单商品集合
     */
    private List<AiPrintOrder> orders;


    /**
     * 订单号集合连接起来的字符串
     */
    private String tidsStr;
    private String sellerMemosStr;
    private String buyerMessagesStr;
    private String expressNamesStr;
    private String expressNosStr;
    private String categoryCode;

    private Integer printStatusCode;
    private Integer tradeStatusCode;
    /**
     * tidMd5值
     */
    private String tidMd5;

    public void initFieldsBeforeInsert() {
        if (CollectionUtils.isNotEmpty(this.tids)) {
            this.tids.sort(String::compareTo);
            this.tidsStr = StringUtils.join(this.tids, "|");
            this.tidMd5 = SecureUtil.md5(this.tidsStr);
        }
        if (CollectionUtils.isNotEmpty(this.sellerMemos)) {
            this.sellerMemosStr = StringUtils.join(this.sellerMemos, "|");
        }
        if (CollectionUtils.isNotEmpty(this.buyerMessages)) {
            this.buyerMessagesStr = StringUtils.join(this.buyerMessages, "|");
        }
        if (CollectionUtils.isNotEmpty(this.expressNames)) {
            this.expressNamesStr = StringUtils.join(this.expressNames, "|");
        }
        if (CollectionUtils.isNotEmpty(this.expressNos)) {
            this.expressNosStr = StringUtils.join(this.expressNos, "|");
        }
        if (this.category != null) {
            this.categoryCode = this.category.name();
        }
        if (this.printStatus != null) {
            this.printStatusCode = this.printStatus.getCode();
        }
        if (this.tradeStatus != null) {
            this.tradeStatusCode = this.tradeStatus.getCode();
        }

        //一些不是很重要的字段，按数据库分配的长度截断一下
        this.errorMsg = StringUtil.frontSubstring(this.errorMsg, 128);
        this.sellerMemosStr = StringUtil.frontSubstring(this.sellerMemosStr, 1024);
        this.buyerMessagesStr = StringUtil.frontSubstring(this.buyerMessagesStr, 1024);
    }

    public void initFieldsForResult() {
        if (StringUtils.isNotBlank(this.tidsStr)) {
            this.tids = StringUtil.splitToStringList(this.tidsStr, "\\|");
        }
        if (StringUtils.isNotBlank(this.sellerMemosStr)) {
            this.sellerMemos = StringUtil.splitToStringList(this.sellerMemosStr, "\\|");
        }
        if (StringUtils.isNotBlank(this.buyerMessagesStr)) {
            this.buyerMessages = StringUtil.splitToStringList(this.buyerMessagesStr, "\\|");
        }
        if (StringUtils.isNotBlank(this.expressNamesStr)) {
            this.expressNames = StringUtil.splitToStringList(this.expressNamesStr, "\\|");
        }
        if (StringUtils.isNotBlank(this.expressNosStr)) {
            this.expressNos = StringUtil.splitToStringList(this.expressNosStr, "\\|");
        }
        if (StringUtils.isNotBlank(this.categoryCode)) {
            this.category = AiSortTradeCategoryEnum.getByName(this.categoryCode);
        }
        if (this.printStatusCode != null) {
            this.printStatus = PrintStatus.getByCode(this.printStatusCode);
        }
        if (this.tradeStatusCode != null) {
            this.tradeStatus = TradeStatus.getByCode(this.tradeStatusCode);
        }
    }
}
