package com.kuaidizs.jxc.domain.logisticsUpgrade;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/17.
 * @time 16:19.
 */
@Data
public class LogisticsInfoVo implements Serializable {

    private static final long serialVersionUID = 5114634451688546450L;

    /**
     * 主交易号
     */
    private String tradeId;

    /**
     * 子交易号
     */
    private String subTradeId;

    /**
     * 承诺/最晚揽收时间，日期，格式2019-04-12 16:00:00
     */
    private String promiseCollectTime;

    /**
     * 承诺/最晚出库时间，日期，格式2019-04-12 16:00:00
     */
    private String promiseOutboundTime;

    /**
     * 择配信息
     */
    private String deliveryCps;

    /**
     * ERP商家配编码，建议使用快递名单（列表）
     */
    private String bizDeliveryCode;

    /**
     * 发货仓erp编码（新增字段）
     */
    private String bizStoreCode;

    /**
     * 仓配建议类型
     * 0：子单无仓建议
     * 1：子单有仓建议，erp可参考进行仓作业
     * 2：子单有仓建议，erp必须按照建议进行仓作业
     */
    private String bizSdType;

    /**
     * 订单推荐配送类型 *
     * 0：子单无配建议；ERP按照自己的逻辑进行择配。 *
     * 1：子单有推荐配list，erp可按需参考。 *
     * 2：子单有推荐配list，erp必须在推荐配list中选择配品牌。 *
     * 3：子单有禁用配list，erp需要过滤配品牌。
     */
    private Long bizDeliveryType;

    /**
     * 服务决策的发货地，国家
     */
    private String sendCountry;

    /**
     * 服务决策的发货地，省份
     */
    private String sendState;
    /**
     * 服务决策的发货地，城市
     */
    private String sendCity;

    /**
     * 服务决策的发货地，地区
     */
    private String sendDistrict;
    /**
     * 服务决策的发货地，街道地址
     */
    private String sendTown;
    /**
     * 服务决策的发货地最小地址编码
     */
    private String sendDivisionCode;
    /**
     * 服务决策的快递黑名单列表,STO,SF
     */
    private List<AoxKdVo> blackDeliveryCps;
    /**
     * 服务决策的快递白名单列表,STO,SF
     */
    private List<AoxKdVo> whiteDeliveryCps;

    /**
     * 强推荐的快递列表
     */
    private List<AoxKdVo> recommendDeliveryCps;

    /**
     * 禁用快递列表
     */
    private List<AoxKdVo> disableDeliveryCps;

    /**
     * 未使用仓建议报错
     */
    private String unusedWarehouseErrorMsg = "当前订单在猫淘平台对消费者进行了服务承诺：当日发次日达，建议按照平台要求进行择仓，否则存在赔付风险";
    /**
     * 未使用配建议报错
     */
    private String unusedDeliveryErrorMsg = "当前订单在猫淘平台对消费者进行了服务承诺，建议按照平台要求进行择配，否则存在赔付风险";

    /**
     * 使用禁止配报错
     */
    private String usedBlackDeliveryErrorMsg = "平台建议当前线路不要使用您选择的配品牌，存在电子面单无法打印风险";
}

