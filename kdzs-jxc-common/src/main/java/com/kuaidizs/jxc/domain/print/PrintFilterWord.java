package com.kuaidizs.jxc.domain.print;

import java.util.*;
import java.io.Serializable;

/**
 *
 * <AUTHOR> @date    2016-07-22
 */
public class PrintFilterWord implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 自增ID
     */
    private Integer id;
	/**
     * 用户ID
     */
    private Long taobaoId;
	/**
     * 创建时间
     */
    private Date created;
	/**
     * 修改时间
     */
    private Date modified;
	/**
     * 逻辑删除
     */
    private Boolean enableStatus;
	/**
     * 过滤词
     */
    private String delKey;
	/**
     * 是否使用统一过滤词：0否1是
     */
    private Integer isUnityKey;

    /**
     * 1.默认，过滤词 2.暂无该类型 3.新增替换词
     */
    private Integer type;

    /**
     * 要替换的文本
     */
    private String replaceContent;
	
   /**
    * @return id 自增ID
    */
    public Integer getId() {
       return id;
    }
   /**
    * @param id 自增ID
    */
    public void setId(Integer id) {
       this.id = id;
    }
	
   /**
    * @return taobaoId 用户ID
    */
    public Long getTaobaoId() {
       return taobaoId;
    }
   /**
    * @param taobaoId 用户ID
    */
    public void setTaobaoId(Long taobaoId) {
       this.taobaoId = taobaoId;
    }
	
   /**
    * @return created 创建时间
    */
    public Date getCreated() {
       return created;
    }
   /**
    * @param created 创建时间
    */
    public void setCreated(Date created) {
       this.created = created;
    }
	
   /**
    * @return modified 修改时间
    */
    public Date getModified() {
       return modified;
    }
   /**
    * @param modified 修改时间
    */
    public void setModified(Date modified) {
       this.modified = modified;
    }
	
   /**
    * @return enableStatus 逻辑删除
    */
    public Boolean getEnableStatus() {
       return enableStatus;
    }
   /**
    * @param enableStatus 逻辑删除
    */
    public void setEnableStatus(Boolean enableStatus) {
       this.enableStatus = enableStatus;
    }
	
   /**
    * @return delKey 过滤词
    */
    public String getDelKey() {
       return delKey;
    }
   /**
    * @param delKey 过滤词
    */
    public void setDelKey(String delKey) {
       this.delKey = delKey;
    }
	
   /**
    * @return isUnityKey 是否使用统一过滤词：0否1是
    */
    public Integer getIsUnityKey() {
       return isUnityKey;
    }
   /**
    * @param isUnityKey 是否使用统一过滤词：0否1是
    */
    public void setIsUnityKey(Integer isUnityKey) {
       this.isUnityKey = isUnityKey;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getReplaceContent() {
        return replaceContent;
    }

    public void setReplaceContent(String replaceContent) {
        this.replaceContent = replaceContent;
    }
}