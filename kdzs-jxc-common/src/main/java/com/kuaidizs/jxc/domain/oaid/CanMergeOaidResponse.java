package com.kuaidizs.jxc.domain.oaid;

import java.io.Serializable;
import java.util.List;

/***
 * program: kdzs-jxc
 * description: 能合并的oaid响应结果
 *
 *   批打合单问题：1。如果接口异常，会导致合单不准确。
 *
 *  基于收件人的信息(姓名、地址、手机号、电话）、订单创建时间、店铺、appkey加密生成的ID，长度128个字符以内。
 *  相同的收件人，在同一家店铺，在固定时间周期内（通常1个自然周）内创建的订单，OAID相同。
 *
 *  说明：对于一批订单来说，若订单都有oaid，并且这批订单的时间跨期为一个月，
 *  则这批订单的数据会存在如下几种情况：
 *
 *  1。同一买家多笔订单加密后组成的地址不同，oaid不同。（一定是不能合单的）
 *
 *  2。同一买家多笔订单加密后组成的地址不同，oaid相同。（这种情况不存在，所以不考虑）
 *
 *  3。同一买家多笔订单加密后组成的地址相同，但是oaid不同。（有些能够合单，有些不能合单）
 *     A：实际地址相同，但是oaid不同（可能是订单不在一个周期里，导致oaid不同）
 *
 *     B：实际地址不相同，oaid也不同（就是是订单地址不一致（但是我们是不知道的，只知道加密后组成的地址相同），导致oaid不同）
 *
 *  4。同一买家多笔订单加密后组成的地址相同，oaid相同。（一定是能合单的）
 *
 *  则合单策略如下：由于1和4是确定的，不确定的是3，所以需要解决3，
 *    所以对于三：拿到第三种情况的所有订单，调用taobao.top.oaid.merge接口，知道哪些订单是可以合并的，
 *    对于 taobao.top.oaid.merge 接口合单请求列表，最多支持100个，如果超过100，如何解决？
 *    方法：直接取前100个，其他的订单直接不参与合并
 *
 *
 * author: <EMAIL>
 * create: 2021-04-27 17:36
 **/
public class CanMergeOaidResponse implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * oaid的数组，格式：["oaid1,oaid2","oaid3"]，"oaid1,oaid2"表示oaid1和oaid2能合
     */
    private List<String> oaidList;

    /**
     * 是否成功
     */
    private Boolean success;


    public List<String> getOaidList() {
        return oaidList;
    }

    public void setOaidList(List<String> oaidList) {
        this.oaidList = oaidList;
    }

    public Boolean getSuccess() {
        return success;
    }

    public void setSuccess(Boolean success) {
        this.success = success;
    }
}
