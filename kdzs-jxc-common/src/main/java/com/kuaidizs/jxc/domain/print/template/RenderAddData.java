package com.kuaidizs.jxc.domain.print.template;


import java.io.Serializable;

/**
 * 菜鸟指令渲染时的AddData数据结构
 */
public class RenderAddData implements Serializable {

    private static final long serialVersionUID = -7942830787616577092L;

    /**
     * 一联(普通的)
     */
    public static final String BLUE_TOOTH_CUSTOME_ONE_COMMON = "http://cloudprint.cainiao.com/template/standard/307406/1";

    /**
     * 邮政一联单特殊处理
     */
    public static final String BLUE_TOOTH_CUSTOME_ONE_COMMON_POSTB = "http://cloudprint.cainiao.com/template/standard/306636/1";

    /**
     * 二联桌面
     */
    public static final String BLUE_TOOTH_CUSTOME_TWO_COMMON = "http://cloudprint.cainiao.com/template/standard/307804/1";

    private RenderAddDataSender sender;

    public RenderAddDataSender getSender() {
        return sender;
    }

    public void setSender(RenderAddDataSender sender) {
        this.sender = sender;
    }
}
