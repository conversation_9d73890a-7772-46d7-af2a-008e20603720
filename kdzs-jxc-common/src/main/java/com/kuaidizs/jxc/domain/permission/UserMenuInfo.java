package com.kuaidizs.jxc.domain.permission;

import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.io.Serializable;

/**
 *
 * <AUTHOR> @date    2016-08-26
 */
public class UserMenuInfo implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 自增列ID
     */
    private Long id;
	/**
     * 菜单ID
     */
    private Long menuId;
	/**
     * 菜单名称
     */
    private String menuName;
	/**
     * 菜单级别 1、一级菜单2、二级菜单
     */
    private Integer menuLevel;
	/**
     * 菜单英文标识
     */
    private String menuKey;
	/**
     * 父级菜单
     */
    private Long parentMenuId;
	/**
     * 添加时间
     */
    private Date created;
	/**
     * 更新时间
     */
    private Date modified;
	/**
     * 逻辑删除
     */
    private Integer enableStatus;

    private String urlStrs;

    private List<String> urlList;

   /**
    * @return id 自增列ID
    */
    public Long getId() {
       return id;
    }
   /**
    * @param id 自增列ID
    */
    public void setId(Long id) {
       this.id = id;
    }
	
   /**
    * @return menuId 菜单ID
    */
    public Long getMenuId() {
       return menuId;
    }
   /**
    * @param menuId 菜单ID
    */
    public void setMenuId(Long menuId) {
       this.menuId = menuId;
    }
	
   /**
    * @return menuName 菜单名称
    */
    public String getMenuName() {
       return menuName;
    }
   /**
    * @param menuName 菜单名称
    */
    public void setMenuName(String menuName) {
       this.menuName = menuName;
    }
	
   /**
    * @return menuLevel 菜单级别 1、一级菜单2、二级菜单
    */
    public Integer getMenuLevel() {
       return menuLevel;
    }
   /**
    * @param menuLevel 菜单级别 1、一级菜单2、二级菜单
    */
    public void setMenuLevel(Integer menuLevel) {
       this.menuLevel = menuLevel;
    }
	
   /**
    * @return menuKey 菜单英文标识
    */
    public String getMenuKey() {
       return menuKey;
    }
   /**
    * @param menuKey 菜单英文标识
    */
    public void setMenuKey(String menuKey) {
       this.menuKey = menuKey;
    }
	
   /**
    * @return parentMenuId 父级菜单
    */
    public Long getParentMenuId() {
       return parentMenuId;
    }
   /**
    * @param parentMenuId 父级菜单
    */
    public void setParentMenuId(Long parentMenuId) {
       this.parentMenuId = parentMenuId;
    }
	
   /**
    * @return created 添加时间
    */
    public Date getCreated() {
       return created;
    }
   /**
    * @param created 添加时间
    */
    public void setCreated(Date created) {
       this.created = created;
    }
	
   /**
    * @return modified 更新时间
    */
    public Date getModified() {
       return modified;
    }
   /**
    * @param modified 更新时间
    */
    public void setModified(Date modified) {
       this.modified = modified;
    }
	
   /**
    * @return enableStatus 逻辑删除
    */
    public Integer getEnableStatus() {
       return enableStatus;
    }
   /**
    * @param enableStatus 逻辑删除
    */
    public void setEnableStatus(Integer enableStatus) {
       this.enableStatus = enableStatus;
    }

    public String getUrlStrs() {
        return urlStrs;
    }

    public void setUrlStrs(String urlStrs) {
        this.urlStrs = urlStrs;
    }

    public List<String> getUrlList() {
        if(StringUtils.isNotBlank(urlStrs)){
            urlList = Arrays.asList(urlStrs.split(","));
        }
        return urlList;
    }

    public void setUrlList(List<String> urlList) {
        this.urlList = urlList;
    }
}