package com.kuaidizs.jxc.domain.data;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
public class UserVersionTrade {
    private Long id;
    private Long taobaoId;
    private String version;
    private Date expireTime;
    private Long weekRdsTradeCount;
    private Long weekPrintRecordCount1;
    private Long weekPrintRecordCount2;
    private Long weekPrintRecordCount3;
    private Long weekSendRecordCount1;
    private Long weekSendRecordCount2;
    private Long weekSendRecordCount3;
    private Date created;
    private Date modified;

    /**
     * 是否可以注册
     * 1-是
     * 0-否
     */
    private Integer canRegisterRds;
}