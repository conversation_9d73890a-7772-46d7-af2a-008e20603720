package com.kuaidizs.jxc.domain.agent;

import com.kuaidizs.jw.domain.common.BasePojo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023-04-14
 * 代发订单实体类
 */
@Data
public class AgentTrade extends BasePojo implements Serializable {

    private static final long serialVersionUID = -6693866818592365280L;

    private Long id;

    /**
     * 用户ID taobaoid
     */
    private Long userId;

    /**
     * 平台用户ID
     */
    private String sellerId;

    /**
     * 操作人用户id
     */
    private Long operationUserId;

    /**
     * 厂家id 系统内部用户id  因为绑定关系里面用的string taobaoid
     */
    private String factoryId;
    /**
     * 厂家名换成呢个
     */
    private String factoryName;

    /**
     * 平台类型
     */
    private String platform;

    /**
     * 主订单号
     */
    private String tid;

    /**
     * 子订单号
     */
    private String oid;

    /**
     * 推送状态 0 未推送 1 已推送
     */
    private Integer pushStatus;

    /**
     * 发货状态 0 未发货 1 已发货
     */
    private Integer shippingStatus;

    private Date created;

    private Date modified;
}
