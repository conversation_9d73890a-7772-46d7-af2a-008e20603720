package com.kuaidizs.jxc.domain.print.waybill;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description 批量获取模版列表网点余额请求
 * @Date 2024/12/12 14:12
 * @Created by cn
 */
@Data
public class BatchWaybillBranchRequest implements Serializable {
    /**
     * 模版id
     */
    private Long[] modeListShowIds;
    /**
     * 模版对应的exid
     */
    private Long[] exIds;
    /**
     * 模版对应的快递编码
     */
    private String[] excodes;
}
