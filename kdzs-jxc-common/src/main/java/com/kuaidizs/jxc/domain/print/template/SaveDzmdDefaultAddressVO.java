package com.kuaidizs.jxc.domain.print.template;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * 更新用户云栈信息
 *
 * <AUTHOR>    Date:2016/8/4  17:01
 */
public class SaveDzmdDefaultAddressVO implements Serializable {

    private static final long serialVersionUID = -686273618627107326L;

    /**
     * 主键id
     */
    private Long Id;

    private Long ListShowId;

    /**
     * 网点code
     */
    private String Branch_code;

    /**
     * 网点名称
     */
    private String Branch_name;

    /**
     * 网点类型
     */
    private Integer Type;

    /**
     * 快递id
     */
    private Long Exid;

    private Long Exsubid;

    private Long Exuserid;

    /**
     * 省份
     */
    private String F_p;

    /**
     * 城市
     */
    private String F_c;

    /**
     * 区级
     */
    private String F_q;

    /**
     * 地址
     */
    private String F_addr;
    /**
     * town
     */
    private String F_town;

    private String F_name;

    private String F_mobile;

    private String F_tel;

    private String F_zip;

    private String warehouseId;

    /**
     * 当前单号是否是别的店铺分享的 0:不是 1:是
     */
    private Integer shared;

    /**
     * 对应的淘宝昵称
     */
    private String taoBaoNick;

    public String getTaoBaoNick() {
        return taoBaoNick;
    }

    public void setTaoBaoNick(String taoBaoNick) {
        this.taoBaoNick = taoBaoNick;
    }

    public Integer getShared() {
        return shared;
    }

    public void setShared(Integer shared) {
        this.shared = shared;
    }

    /**
     * 如果当前网点是分享的网点，此处表示被分享者的taobaoId
     */
    private Long sharedTaobaoId;

    public Long getSharedTaobaoId() {
        return sharedTaobaoId;
    }

    public void setSharedTaobaoId(Long sharedTaobaoId) {
        this.sharedTaobaoId = sharedTaobaoId;
    }

    /**
     * 号段
     */
    private String segmentCode;

    private String segmentCodeName;

    /**
     * 获取用户网点信息，额外的flag信息
     */
    private boolean flag;

    //-------------------------------------------自助版多平台分单界面的淘外菜鸟分单 start---------------------------------------------------------
    /**
     * 是淘外菜鸟分单，还是淘宝内分单
     * 1：淘外分单，0：淘宝内分单
     * 默认是淘宝内分单
     */
    private Integer isZzTwcnShare;

    /**
     * 自助版多平台分单界面的淘外菜鸟分单场景才会用到
     * 对应的是自助版多平台分单里面的分享关系表的id
     */
    private String twcnShareId;

    /**
     * 自助版多平台分单界面的淘外菜鸟分单场景才会用到
     * 对应的是单号分享者在自助版里面的userId
     */
    private String ownerZzId;

    /**
     * 自助版多平台分单界面的淘外菜鸟分单场景才会用到
     * 对应的是被分享者在淘宝快递助手里面的userId
     */
    private String twcnSharedUserId;

    /**
     * 自助版多平台分单界面的淘外菜鸟分单场景才会用到
     * 对应的是被分享者在淘宝快递助手里面的taobaoId
     */
    private String twcnSharedTaobaoId;
    //-------------------------------------------自助版多平台分单界面的淘外菜鸟分单 end---------------------------------------------------------


    @JSONField(name = "Id")
    public Long getId() {
        return Id;
    }

    @JSONField(name = "Id")
    public void setId(Long id) {
        Id = id;
    }

    @JSONField(name = "ListShowId")
    public Long getListShowId() {
        return ListShowId;
    }

    @JSONField(name = "ListShowId")
    public void setListShowId(Long listShowId) {
        ListShowId = listShowId;
    }

    @JSONField(name = "Branch_code")
    public String getBranch_code() {
        return Branch_code;
    }

    @JSONField(name = "Branch_code")
    public void setBranch_code(String branch_code) {
        Branch_code = branch_code;
    }

    @JSONField(name = "Branch_name")
    public String getBranch_name() {
        return Branch_name;
    }

    @JSONField(name = "Branch_name")
    public void setBranch_name(String branch_name) {
        Branch_name = branch_name;
    }

    @JSONField(name = "Type")
    public Integer getType() {
        return Type;
    }

    @JSONField(name = "Type")
    public void setType(Integer type) {
        Type = type;
    }

    @JSONField(name = "Exid")
    public Long getExid() {
        return Exid;
    }

    @JSONField(name = "Exid")
    public void setExid(Long exid) {
        Exid = exid;
    }

    @JSONField(name = "Exsubid")
    public Long getExsubid() {
        return Exsubid;
    }

    @JSONField(name = "Exsubid")
    public void setExsubid(Long exsubid) {
        Exsubid = exsubid;
    }

    @JSONField(name = "Exuserid")
    public Long getExuserid() {
        return Exuserid;
    }

    @JSONField(name = "Exuserid")
    public void setExuserid(Long exuserid) {
        Exuserid = exuserid;
    }

    @JSONField(name = "F_p")
    public String getF_p() {
        return F_p;
    }

    @JSONField(name = "F_p")
    public void setF_p(String f_p) {
        F_p = f_p;
    }

    @JSONField(name = "F_c")
    public String getF_c() {
        return F_c;
    }

    @JSONField(name = "F_c")
    public void setF_c(String f_c) {
        F_c = f_c;
    }

    @JSONField(name = "F_q")
    public String getF_q() {
        return F_q;
    }

    @JSONField(name = "F_q")
    public void setF_q(String f_q) {
        F_q = f_q;
    }

    @JSONField(name = "F_addr")
    public String getF_addr() {
        return F_addr;
    }

    @JSONField(name = "F_addr")
    public void setF_addr(String f_addr) {
        F_addr = f_addr;
    }

    @JSONField(name = "F_name")
    public String getF_name() {
        return F_name;
    }

    @JSONField(name = "F_name")
    public void setF_name(String f_name) {
        F_name = f_name;
    }

    @JSONField(name = "F_mobile")
    public String getF_mobile() {
        return F_mobile;
    }

    @JSONField(name = "F_mobile")
    public void setF_mobile(String f_mobile) {
        F_mobile = f_mobile;
    }

    @JSONField(name = "F_tel")
    public String getF_tel() {
        return F_tel;
    }

    @JSONField(name = "F_tel")
    public void setF_tel(String f_tel) {
        F_tel = f_tel;
    }

    @JSONField(name = "F_zip")
    public String getF_zip() {
        return F_zip;
    }

    @JSONField(name = "F_zip")
    public void setF_zip(String f_zip) {
        F_zip = f_zip;
    }

    @JSONField(name = "F_town")
    public String getF_town() {
        return F_town;
    }

    public void setF_town(String f_town) {
        F_town = f_town;
    }

    public String getWarehouseId() {
        return warehouseId;
    }

    public void setWarehouseId(String warehouseId) {
        this.warehouseId = warehouseId;
    }

    public String getSegmentCode() {
        return segmentCode;
    }

    public void setSegmentCode(String segmentCode) {
        this.segmentCode = segmentCode;
    }

    public String getSegmentCodeName() {
        return segmentCodeName;
    }

    public void setSegmentCodeName(String segmentCodeName) {
        this.segmentCodeName = segmentCodeName;
    }

    public boolean isFlag() {
        return flag;
    }

    public void setFlag(boolean flag) {
        this.flag = flag;
    }

    public Integer getIsZzTwcnShare() {
        return isZzTwcnShare;
    }

    public void setIsZzTwcnShare(Integer isZzTwcnShare) {
        this.isZzTwcnShare = isZzTwcnShare;
    }

    public String getTwcnShareId() {
        return twcnShareId;
    }

    public void setTwcnShareId(String twcnShareId) {
        this.twcnShareId = twcnShareId;
    }

    public String getOwnerZzId() {
        return ownerZzId;
    }

    public void setOwnerZzId(String ownerZzId) {
        this.ownerZzId = ownerZzId;
    }

    public String getTwcnSharedUserId() {
        return twcnSharedUserId;
    }

    public void setTwcnSharedUserId(String twcnSharedUserId) {
        this.twcnSharedUserId = twcnSharedUserId;
    }

    public String getTwcnSharedTaobaoId() {
        return twcnSharedTaobaoId;
    }

    public void setTwcnSharedTaobaoId(String twcnSharedTaobaoId) {
        this.twcnSharedTaobaoId = twcnSharedTaobaoId;
    }
}
