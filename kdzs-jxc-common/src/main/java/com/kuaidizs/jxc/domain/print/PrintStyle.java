package com.kuaidizs.jxc.domain.print;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;

import java.io.Serializable;
import java.util.Date;

/**
 *
 * <AUTHOR> @date    2016-08-15
 */
public class PrintStyle implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 自增ID
     */
    @JSONField(name="Id",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer id;
	/**
     * 用户ID
     */
    @JSONField(name="Exuserid",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Long taobaoId;
	/**
     * 创建时间
     */
    private Date created;
	/**
     * 修改时间
     */
    private Date modified;
	/**
     * 逻辑删除
     */
    private Boolean enableStatus;
	/**
     * 是否合并同款产品
     */
    @JSONField(name="isTogether",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer isMerge;
	/**
     * 每款宝贝是否换行 -1不换行 1换行 其他值表示宝贝少于多少款时换行,否则不换行
     */
    @JSONField(name="breakline",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer isWrap;

    @JSONField(name="breaklineNum",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer breaklineNum;
	/**
     * 宝贝数量的前缀
     */
    @JSONField(name="NumLeft",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String numLeft;
	/**
     * 宝贝数量的后缀
     */
    @JSONField(name="NumRight",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String numRight;
    /**
     * 宝贝数量显示方式类型 0：不包含 1：包含 默认0
     */
    @JSONField(name="goodsNumDisplayType",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer goodsNumDisplayType;
	/**
     * 当订单有满就送时,是否加入发货内容中
     */
    @JSONField(name="tradeReward",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer isMjs;
	/**
     * 宝贝名称使用商品编码
     */
    @JSONField(name="useOuterIid",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer isTitleSjbm;


    /**
     * 自动分页
     */
    @JSONField(name = "autoBreakPage", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Boolean autoBreakPage;
	/**
     * 宝贝名称使用商品名称
     */
        @JSONField(name="useShortName",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer isTitleSjmc;
	/**
     * 宝贝规格使用规格编码
     */
    @JSONField(name="useRuleOutIid",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer isSkuSjbm;
	/**
     * 宝贝规格使用规格名称
     */
    @JSONField(name="useSkuName",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer isSkuSjmc;
	/**
     * 是否采用可以发件人样式：0否1是
     */
    private Integer isUnityStyle;
	/**
     * 是否使用统一的过滤词：0否1是
     */
    private Integer isUnityKey;

    /***
     * 宝贝单位显示方式：0:不显示1:件2:个3：包
     */
    @JSONField(name="unitStyle",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer unitType;


    /**
     * 宝贝名称使用档口
     */
    @JSONField(name = "useMall", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer useStall;

    /**
     * 宝贝名称使用市场
     */
    @JSONField(name = "useMarket", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer useMarket;

    /**
     * 0:没有内容时显示商家编码
     * 1:没有内容时显示标题
     * 2:没有时显示简称
     * 3:没有时显示档口
     */
    private Integer useOthersWithMarket;

    /**
     * 0:没有内容时显示标题
     * 1:没有内容时显示编码
     * 2:没有时显示简称
     * 3:没有时什么都不显示
     */
    private Integer useOthersWithStall;

    /**
     * 显示顺序
     */
    private String showSequence;

    /**
     * 规格显示顺序
     */
    private String showSequenceSku;

    /**
     * 展示样式
     */
    private Integer showStyle;

    /**
     * 分隔符
     */
    private String separatorSign;


    /***
     * 无商家编码时不显示内容
     */
    private Boolean useOuterIidEmpty;

    /***
     * 无规格商家编码时不显示内容
     */
    private Boolean useRuleOutIidEmpty;

    /***
     * 没有宝贝简称时不显示内容
     */
    private Integer useShortNameEmpty;

    /***
     * 使用宝贝标题
     */
    private Boolean useTitle;

    /***
     * 无规格别名时不显示
     */
    private Boolean showSkuAliasEmpty;

    /**
     * 是否显示规格别名
     */
    @JSONField(name="showSkuAlias",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private boolean showSkuAlias;

    /**
     * 是否显示规格档口 默认false
     */
    @JSONField(name = "showSkuStall", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private boolean showSkuStall;

    /**
     * 规格无档口时展示类型 0：无档口时显示商家编码 1：无档口时显示别名 2：无档口时显示名称 默认0
     */
    private Integer showSkuTypeByNoStall;

    /**
     * 显示商品价格 0：不显示；1：显示
     */
    private Boolean showOrderPrice;

    /**
     * 是否使用淘宝ID 'true使用，false:不使用'
     */
    private Boolean useNumIid;

    /**
     * 发货内容是否分页。 -1、不换行; 其他值、表示宝贝多于多少款时分页
     */
    private Integer isBreakPage;

    /**
     * 发货内容是否分页。 -1、不换行; 其他值、表示宝贝多于多少款时分页
     */
    private Integer breakConcatPageNum;

    public Integer getBreakConcatPageNum() {
        return breakConcatPageNum;
    }

    public void setBreakConcatPageNum(Integer breakConcatPageNum) {
        this.breakConcatPageNum = breakConcatPageNum;
    }
    /**
     * 设置快递单发货内容是否分页显示 -1、不分页；2、宝贝大于breakpageNum时，分页
     */
    private Integer breakpage;

    public Integer getBreakpage() {
        return breakpage;
    }

    public void setBreakpage(Integer breakpage) {
        this.breakpage = breakpage;
    }


    /**
     * 显示打印序号0不显示、1显示
     */
    private Integer displayPrintOrder;

    /**
     * 打印内容需要过滤的商品简称
     */
    private String shortNames;

    /**
     * 需要过滤的商品简称的宝贝不打印拿货小标签，扫描时自动备齐（此字段p51使用）0：关闭，1：开启
     */
    private Integer filterNotPrintTag;

    private String selfUnitStyle;

    /**
     * 存在成分品时，展示成分品规格信息 0 : 不展示， 1：展示（默认） 
     */
    private Integer showCombineInfo;

    public Integer getShowCombineInfo() {
        return showCombineInfo;
    }

    public void setShowCombineInfo(Integer showCombineInfo) {
        this.showCombineInfo = showCombineInfo;
    }

    public String getSelfUnitStyle() {
        return selfUnitStyle;
    }

    public void setSelfUnitStyle(String selfUnitStyle) {
        this.selfUnitStyle = selfUnitStyle;
    }

    public Integer getUseOthersWithMarket() {
        return useOthersWithMarket;
    }

    public void setUseOthersWithMarket(Integer useOthersWithMarket) {
        this.useOthersWithMarket = useOthersWithMarket;
    }

    public Integer getUseMarket() {
        return useMarket;
    }

    public void setUseMarket(Integer useMarket) {
        this.useMarket = useMarket;
    }

    public String getShortNames() {
        return shortNames;
    }

    public void setShortNames(String shortNames) {
        this.shortNames = shortNames;
    }

    public Integer getFilterNotPrintTag() {
        return filterNotPrintTag;
    }

    public void setFilterNotPrintTag(Integer filterNotPrintTag) {
        this.filterNotPrintTag = filterNotPrintTag;
    }

    public Boolean getUseNumIid() {
        return useNumIid;
    }

    public void setUseNumIid(Boolean useNumIid) {
        this.useNumIid = useNumIid;
    }

    public Boolean getShowOrderPrice() {
        return showOrderPrice;
    }

    public void setShowOrderPrice(Boolean showOrderPrice) {
        this.showOrderPrice = showOrderPrice;
    }

    public Boolean getShowSkuAliasEmpty() {
        return showSkuAliasEmpty;
    }

    public void setShowSkuAliasEmpty(Boolean showSkuAliasEmpty) {
        this.showSkuAliasEmpty = showSkuAliasEmpty;
    }

    public boolean isShowSkuAlias() {
        return showSkuAlias;
    }

    public void setShowSkuAlias(boolean showSkuAlias) {
        this.showSkuAlias = showSkuAlias;
    }

    public boolean isShowSkuStall() {
        return showSkuStall;
    }

    public void setShowSkuStall(boolean showSkuStall) {
        this.showSkuStall = showSkuStall;
    }

    public Integer getShowSkuTypeByNoStall() {
        return showSkuTypeByNoStall;
    }

    public void setShowSkuTypeByNoStall(Integer showSkuTypeByNoStall) {
        this.showSkuTypeByNoStall = showSkuTypeByNoStall;
    }

    public Integer getUnitType() {
        return unitType;
    }

    public void setUnitType(Integer unitType) {
        this.unitType = unitType;
    }

    public Boolean getUseOuterIidEmpty() {
        return useOuterIidEmpty;
    }

    public void setUseOuterIidEmpty(Boolean useOuterIidEmpty) {
        this.useOuterIidEmpty = useOuterIidEmpty;
    }

    public Boolean getUseRuleOutIidEmpty() {
        return useRuleOutIidEmpty;
    }

    public void setUseRuleOutIidEmpty(Boolean useRuleOutIidEmpty) {
        this.useRuleOutIidEmpty = useRuleOutIidEmpty;
    }

    public Integer getUseShortNameEmpty() {
        return useShortNameEmpty;
    }

    public void setUseShortNameEmpty(Integer useShortNameEmpty) {
        this.useShortNameEmpty = useShortNameEmpty;
    }

    public Boolean getUseTitle() {
        return useTitle;
    }

    public void setUseTitle(Boolean useTitle) {
        this.useTitle = useTitle;
    }

    /**
    * @return id 自增ID
    */
    public Integer getId() {
       return id;
    }
   /**
    * @param id 自增ID
    */
    public void setId(Integer id) {
       this.id = id;
    }

   /**
    * @return taobaoId 用户ID
    */
    public Long getTaobaoId() {
       return taobaoId;
    }
   /**
    * @param taobaoId 用户ID
    */
    public void setTaobaoId(Long taobaoId) {
       this.taobaoId = taobaoId;
    }

    /**
    * @return created 创建时间
    */
    public Date getCreated() {
       return created;
    }
   /**
    * @param created 创建时间
    */
    public void setCreated(Date created) {
       this.created = created;
    }

    /**
    * @return modified 修改时间
    */
    public Date getModified() {
       return modified;
    }
   /**
    * @param modified 修改时间
    */
    public void setModified(Date modified) {
       this.modified = modified;
    }

    /**
    * @return enableStatus 逻辑删除
    */
    public Boolean getEnableStatus() {
       return enableStatus;
    }
   /**
    * @param enableStatus 逻辑删除
    */
    public void setEnableStatus(Boolean enableStatus) {
       this.enableStatus = enableStatus;
    }

    /**
    * @return isMerge 是否合并同款产品
    */
    public Integer getIsMerge() {
       return isMerge;
    }
   /**
    * @param isMerge 是否合并同款产品
    */
    public void setIsMerge(Integer isMerge) {
       this.isMerge = isMerge;
    }

    /**
    * @return isWrap 每款宝贝是否换行 -1不换行 1换行 其他值表示宝贝少于多少款时换行,否则不换行
    */
    public Integer getIsWrap() {
       return isWrap;
    }
   /**
    * @param isWrap 每款宝贝是否换行 -1不换行 1换行 其他值表示宝贝少于多少款时换行,否则不换行
    */
    public void setIsWrap(Integer isWrap) {
       this.isWrap = isWrap;
    }

    /**
     * -1不换行 1换行 其他值表示宝贝少于多少款时换行,否则不换行
     * @return
     */
    public Integer getBreaklineNum() {
        return breaklineNum;
    }

    public void setBreaklineNum(Integer breaklineNum) {
        this.breaklineNum = breaklineNum;
    }

    /**
    * @return numLeft 宝贝数量的前缀
    */
    public String getNumLeft() {
       return numLeft;
    }
   /**
    * @param numLeft 宝贝数量的前缀
    */
    public void setNumLeft(String numLeft) {
       this.numLeft = numLeft;
    }

    /**
    * @return numRight 宝贝数量的后缀
    */
    public String getNumRight() {
       return numRight;
    }
   /**
    * @param numRight 宝贝数量的后缀
    */
    public void setNumRight(String numRight) {
       this.numRight = numRight;
    }

    public Integer getGoodsNumDisplayType() {
        return goodsNumDisplayType;
    }

    public void setGoodsNumDisplayType(Integer goodsNumDisplayType) {
        this.goodsNumDisplayType = goodsNumDisplayType;
    }

    /**
    * @return isMjs 当订单有满就送时,是否加入发货内容中
    */
    public Integer getIsMjs() {
       return isMjs;
    }
   /**
    * @param isMjs 当订单有满就送时,是否加入发货内容中
    */
    public void setIsMjs(Integer isMjs) {
       this.isMjs = isMjs;
    }

    /**
    * @return isTitleSjbm 宝贝名称使用商品编码
    */
    public Integer getIsTitleSjbm() {
       return isTitleSjbm;
    }
   /**
    * @param isTitleSjbm 宝贝名称使用商品编码
    */
    public void setIsTitleSjbm(Integer isTitleSjbm) {
       this.isTitleSjbm = isTitleSjbm;
    }

    /**
    * @return isTitleSjmc 宝贝名称使用商品名称
    */
    public Integer getIsTitleSjmc() {
       return isTitleSjmc;
    }
   /**
    * @param isTitleSjmc 宝贝名称使用商品名称
    */
    public void setIsTitleSjmc(Integer isTitleSjmc) {
       this.isTitleSjmc = isTitleSjmc;
    }

    /**
    * @return isSkuSjbm 宝贝规格使用规格编码
    */
    public Integer getIsSkuSjbm() {
       return isSkuSjbm;
    }
   /**
    * @param isSkuSjbm 宝贝规格使用规格编码
    */
    public void setIsSkuSjbm(Integer isSkuSjbm) {
       this.isSkuSjbm = isSkuSjbm;
    }

    /**
    * @return isSkuSjmc 宝贝规格使用规格名称
    */
    public Integer getIsSkuSjmc() {
       return isSkuSjmc;
    }
   /**
    * @param isSkuSjmc 宝贝规格使用规格名称
    */
    public void setIsSkuSjmc(Integer isSkuSjmc) {
       this.isSkuSjmc = isSkuSjmc;
    }

    /**
    * @return isUnityStyle 是否采用可以发件人样式：0否1是
    */
    public Integer getIsUnityStyle() {
       return isUnityStyle;
    }
   /**
    * @param isUnityStyle 是否采用可以发件人样式：0否1是
    */
    public void setIsUnityStyle(Integer isUnityStyle) {
       this.isUnityStyle = isUnityStyle;
    }

    /**
    * @return isUnityKey 是否使用统一的过滤词：0否1是
    */
    public Integer getIsUnityKey() {
       return isUnityKey;
    }
   /**
    * @param isUnityKey 是否使用统一的过滤词：0否1是
    */
    public void setIsUnityKey(Integer isUnityKey) {
       this.isUnityKey = isUnityKey;
    }

    public Integer getIsBreakPage() {
        return isBreakPage;
    }

    public void setIsBreakPage(Integer isBreakPage) {
        this.isBreakPage = isBreakPage;
    }

    public Integer getDisplayPrintOrder() {
        return displayPrintOrder;
    }

    public void setDisplayPrintOrder(Integer displayPrintOrder) {
        this.displayPrintOrder = displayPrintOrder;
    }

    public Integer getUseStall() {
        return useStall;
    }

    public void setUseStall(Integer useStall) {
        this.useStall = useStall;
    }

    public Integer getUseOthersWithStall() {
        return useOthersWithStall;
    }

    public void setUseOthersWithStall(Integer useOthersWithStall) {
        this.useOthersWithStall = useOthersWithStall;
    }

    public String getShowSequence() {
        return showSequence;
    }

    public void setShowSequence(String showSequence) {
        this.showSequence = showSequence;
    }

    public String getShowSequenceSku() {
        return showSequenceSku;
    }

    public void setShowSequenceSku(String showSequenceSku) {
        this.showSequenceSku = showSequenceSku;
    }

    public Integer getShowStyle() {
        return showStyle;
    }

    public void setShowStyle(Integer showStyle) {
        this.showStyle = showStyle;
    }

    public Boolean getAutoBreakPage() {
        return autoBreakPage;
    }

    public void setAutoBreakPage(Boolean autoBreakPage) {
        this.autoBreakPage = autoBreakPage;
    }

    public String getSeparatorSign() {
        return separatorSign;
    }

    public void setSeparatorSign(String separatorSign) {
        this.separatorSign = separatorSign;
    }
}