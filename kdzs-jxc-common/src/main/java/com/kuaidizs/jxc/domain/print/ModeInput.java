package com.kuaidizs.jxc.domain.print;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> @date 2016-07-26
 */
public class ModeInput implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @JSONField(name = "InputID", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer id;
    /**
     * 用户ID
     */
    @JSONField(name = "Exuserid", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Long taobaoId;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * 逻辑状态
     */
    private Boolean enableStatus;
    /**
     * kdd/fhd
     */
    @JSONField(name = "Inputtype", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String inputType;
    /**
     * mode_listshow表的主键
     */
    @JSONField(name = "Exlistshow_mkddId", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer modeListShowKddId;
    /**
     * mode_listshow表的主键
     */
    @JSONField(name = "Exlistshow_mfhdId", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer modeListShowFhdId;
    /**
     * 模板项的宽
     */
    @JSONField(name = "W_", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer w;
    /**
     * 模板项的高
     */
    @JSONField(name = "H_", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer h;
    /**
     * 模板项的x坐标
     */
    @JSONField(name = "X_", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer x;
    /**
     * 模板项的y坐标
     */
    @JSONField(name = "Y_", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer y;
    /**
     * 是否可编辑0不可编辑 1可编辑
     */
    @JSONField(name = "Isedit", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer isEdit;
    /**
     * 字体 空为使用默认值
     */
    @JSONField(name = "Fontname", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String fontName;
    /**
     * 字号 0为使用默认值
     */
    @JSONField(name = "Fontsize", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer fontSize;
    /**
     * 是否加粗 空为使用默认值（-1使用默认值）  1加粗  0不加粗
     */
    @JSONField(name = "Isb_n", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer isbN;
    /**
     * 行间距
     */
    @JSONField(name = "Hjj", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer lineSpacing;
    /**
     * 行间距2-百分比
     */
    @JSONField(name = "Hjj2", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer lineSpacing2;
    /**
     * 字间距
     */
    @JSONField(name = "Zjj", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer wordSpacing;
    /**
     * 前文字
     */
    @JSONField(name = "Str_q", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String wordLeft;
    /**
     * 后文字
     */
    @JSONField(name = "Str_h", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String wordRight;
    /**
     * 状态
     */
    @JSONField(name = "Status", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer status;
    /**
     * 是否允许编辑--1：允许
     */
    @JSONField(name = "IsEditable", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Boolean isEditable;
    /**
     * 是否允许拖动--1：允许
     */
    @JSONField(name = "IsDraggable", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Boolean isDraggable;
    /**
     * 属性集合
     * JSON串存储，包含各种属性内容
     */
    private String property;
    /**
     * 自定义区域的top值，不是数据库里查出来的
     */
    private String customAreaTop = "0";
    /**
     * 自定义区域的top偏移值，不是数据库里查出来的
     */
    private String offsetTop = "0";
    /**
     * 自定义区域的top偏移值，不是数据库里查出来的
     */
    private String offsetLeft = "0";

    /**
     * 自定义区域的left值，不是数据库里查出来的
     */
    private String customAreaLeft = "0";
    /**
     * 模板描述项集合
     * 注：不存库
     */
    @JSONField(name = "proArray", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private List<ModeInputpro> modeInputproList = new ArrayList<ModeInputpro>();

    /**
     * ModeTemplateItem表的DataKey
     * 注：历史数据，不存库
     */
    @JSONField(name = "DataKey", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String dataKey;

    /**
     * 特殊字段，不存库
     */
    private Integer inputCommonId;

    /**
     * 透明度，取值0-1; 默认1，无值默认1
     * 注：存储，从property中解析
     */
    @JSONField(name = "alpha", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String alpha;
    /**
     * 旋转角度，默认0; maxLength 3位
     * 注：存储，从property中解析
     */
    @JSONField(name = "deg", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String deg;
    /**
     * 背景颜色 无值不设置
     * 注：存储，从property中解析
     */
    @JSONField(name = "bColor", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String bColor;
    /**
     * 字体颜色 无值不设置默认黑色
     * 注：存储，从property中解析
     */
    @JSONField(name = "color", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String color;
    /**
     * 0/1 是否倾斜
     * 注：存储，从property中解析
     */
    @JSONField(name = "italic", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String italic;
    /**
     * 0/1 是否下划线
     * 注：存储，从property中解析
     */
    @JSONField(name = "under", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String under;
    /**
     * 0/1 方向 0:横排 1:竖排
     * 注：存储，从property中解析
     */
    @JSONField(name = "direct", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String direct;
    /**
     * 0,1,2 水平方向 左中右
     * 注：存储，从property中解析
     */
    @JSONField(name = "align", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String align;
    /**
     * 0,1,2 垂直方向 上中下
     * 注：存储，从property中解析
     */
    @JSONField(name = "valign", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String valign;
    /**
     * 0/1 是否加密
     * 注：存储，从property中解析
     */
    @JSONField(name = "encry", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String encry;

    private String tableName;
    private String fkId;

    public String getFkId() {
        return fkId;
    }

    public void setFkId(String fkId) {
        this.fkId = fkId;
    }

    /**
     * @return id 自增ID
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id 自增ID
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * @return taobaoId 用户ID
     */
    public Long getTaobaoId() {
        return taobaoId;
    }

    /**
     * @param taobaoId 用户ID
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return enableStatus 逻辑状态
     */
    public Boolean getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 逻辑状态
     */
    public void setEnableStatus(Boolean enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return inputType kdd/fhd
     */
    public String getInputType() {
        return inputType;
    }

    /**
     * @param inputType kdd/fhd
     */
    public void setInputType(String inputType) {
        this.inputType = inputType;
    }

    /**
     * @return modeListShowKddId mode_listshow表的主键
     */
    public Integer getModeListShowKddId() {
        return modeListShowKddId;
    }

    /**
     * @param modeListShowKddId mode_listshow表的主键
     */
    public void setModeListShowKddId(Integer modeListShowKddId) {
        this.modeListShowKddId = modeListShowKddId;
    }

    /**
     * @return modeListShowFhdId mode_listshow表的主键
     */
    public Integer getModeListShowFhdId() {
        return modeListShowFhdId;
    }

    /**
     * @param modeListShowFhdId mode_listshow表的主键
     */
    public void setModeListShowFhdId(Integer modeListShowFhdId) {
        this.modeListShowFhdId = modeListShowFhdId;
    }

    /**
     * @return w 模板项的宽
     */
    public Integer getW() {
        return w;
    }

    /**
     * @param w 模板项的宽
     */
    public void setW(Integer w) {
        this.w = w;
    }

    /**
     * @return h 模板项的高
     */
    public Integer getH() {
        return h;
    }

    /**
     * @param h 模板项的高
     */
    public void setH(Integer h) {
        this.h = h;
    }

    /**
     * @return x 模板项的x坐标
     */
    public Integer getX() {
        return x;
    }

    /**
     * @param x 模板项的x坐标
     */
    public void setX(Integer x) {
        this.x = x;
    }

    /**
     * @return y 模板项的y坐标
     */
    public Integer getY() {
        return y;
    }

    /**
     * @param y 模板项的y坐标
     */
    public void setY(Integer y) {
        this.y = y;
    }

    /**
     * @return isEdit 是否可编辑0不可编辑 1可编辑
     */
    public Integer getIsEdit() {
        return isEdit;
    }

    /**
     * @param isEdit 是否可编辑0不可编辑 1可编辑
     */
    public void setIsEdit(Integer isEdit) {
        this.isEdit = isEdit;
    }

    /**
     * @return fontName 字体 空为使用默认值
     */
    public String getFontName() {
        return fontName;
    }

    /**
     * @param fontName 字体 空为使用默认值
     */
    public void setFontName(String fontName) {
        this.fontName = fontName;
    }

    /**
     * @return fontSize 字号 0为使用默认值
     */
    public Integer getFontSize() {
        return fontSize;
    }

    /**
     * @param fontSize 字号 0为使用默认值
     */
    public void setFontSize(Integer fontSize) {
        this.fontSize = fontSize;
    }

    /**
     * @return isbN 是否加粗 空为使用默认值  1加粗  0不加粗
     */
    public Integer getIsbN() {
        return isbN;
    }

    /**
     * @param isbN 是否加粗 空为使用默认值  1加粗  0不加粗
     */
    public void setIsbN(Integer isbN) {
        this.isbN = isbN;
    }

    /**
     * @return lineSpacing 行间距
     */
    public Integer getLineSpacing() {
        return lineSpacing;
    }

    /**
     * @param lineSpacing 行间距
     */
    public void setLineSpacing(Integer lineSpacing) {
        this.lineSpacing = lineSpacing;
    }

    /**
     * @return lineSpacing 行间距-百分比
     */
    public Integer getLineSpacing2() {
        return lineSpacing2;
    }

    /**
     * @param lineSpacing2 行间距2-百分比
     */
    public void setLineSpacing2(Integer lineSpacing2) {
        this.lineSpacing2 = lineSpacing2;
    }

    /**
     * @return wordSpacing 字间距
     */
    public Integer getWordSpacing() {
        return wordSpacing;
    }

    /**
     * @param wordSpacing 字间距
     */
    public void setWordSpacing(Integer wordSpacing) {
        this.wordSpacing = wordSpacing;
    }

    /**
     * @return wordLeft 前文字
     */
    public String getWordLeft() {
        return wordLeft;
    }

    /**
     * @param wordLeft 前文字
     */
    public void setWordLeft(String wordLeft) {
        this.wordLeft = wordLeft;
    }

    /**
     * @return wordRight 后文字
     */
    public String getWordRight() {
        return wordRight;
    }

    /**
     * @param wordRight 后文字
     */
    public void setWordRight(String wordRight) {
        this.wordRight = wordRight;
    }

    /**
     * @return status 状态
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * @param status 状态
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * @return isEditable 是否允许编辑--1：允许
     */
    public Boolean getIsEditable() {
        return isEditable;
    }

    /**
     * @param isEditable 是否允许编辑--1：允许
     */
    public void setIsEditable(Boolean isEditable) {
        this.isEditable = isEditable;
    }

    /**
     * @return isDraggable 是否允许拖动--1：允许
     */
    public Boolean getIsDraggable() {
        return isDraggable;
    }

    /**
     * @param isDraggable 是否允许拖动--1：允许
     */
    public void setIsDraggable(Boolean isDraggable) {
        this.isDraggable = isDraggable;
    }

    public String getProperty() {
        return property;
    }

    public void setProperty(String property) {
        this.property = property;
    }

    public List<ModeInputpro> getModeInputproList() {
        return modeInputproList;
    }

    public void setModeInputproList(List<ModeInputpro> modeInputproList) {
        this.modeInputproList = modeInputproList;
    }

    public String getDataKey() {
        return dataKey;
    }

    public void setDataKey(String dataKey) {
        this.dataKey = dataKey;
    }

    public Integer getInputCommonId() {
        return inputCommonId;
    }

    public void setInputCommonId(Integer inputCommonId) {
        this.inputCommonId = inputCommonId;
    }

    public Boolean getEditable() {
        return isEditable;
    }

    public void setEditable(Boolean editable) {
        isEditable = editable;
    }

    public Boolean getDraggable() {
        return isDraggable;
    }

    public void setDraggable(Boolean draggable) {
        isDraggable = draggable;
    }

    public String getAlpha() {
        return alpha;
    }

    public void setAlpha(String alpha) {
        this.alpha = alpha;
    }

    public String getDeg() {
        return deg;
    }

    public void setDeg(String deg) {
        this.deg = deg;
    }

    public String getbColor() {
        return bColor;
    }

    public void setbColor(String bColor) {
        this.bColor = bColor;
    }

    public String getColor() {
        return color;
    }

    public void setColor(String color) {
        this.color = color;
    }

    public String getItalic() {
        return italic;
    }

    public void setItalic(String italic) {
        this.italic = italic;
    }

    public String getUnder() {
        return under;
    }

    public void setUnder(String under) {
        this.under = under;
    }

    public String getDirect() {
        return direct;
    }

    public void setDirect(String direct) {
        this.direct = direct;
    }

    public String getAlign() {
        return align;
    }

    public void setAlign(String align) {
        this.align = align;
    }

    public String getValign() {
        return valign;
    }

    public void setValign(String valign) {
        this.valign = valign;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getEncry() {
        return encry;
    }

    public void setEncry(String encry) {
        this.encry = encry;
    }

    public String getCustomAreaTop() {
        return customAreaTop;
    }

    public void setCustomAreaTop(String customAreaTop) {
        this.customAreaTop = customAreaTop;
    }
    public String getCustomAreaLeft() {
        return customAreaLeft;
    }

    public void setCustomAreaLeft(String customAreaLeft) {
        this.customAreaLeft = customAreaLeft;
    }

    public String getOffsetTop() {
        return offsetTop;
    }

    public void setOffsetTop(String offsetTop) {
        this.offsetTop = offsetTop;
    }

    public String getOffsetLeft() {
        return offsetLeft;
    }

    public void setOffsetLeft(String offsetLeft) {
        this.offsetLeft = offsetLeft;
    }
}