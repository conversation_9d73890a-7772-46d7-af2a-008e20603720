package com.kuaidizs.jxc.domain.agent;

import com.kuaidizs.jw.domain.common.BasePojo;
import lombok.Data;

import java.util.Date;

/**
 * 分销代发商品sku绑定关系
 *
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AgentGoodsSkuBindRel.java
 * @createTime 2022年12月13日 11:01:00
 */
@Data
public class AgentGoodsSkuBindRel extends BasePojo {
	/**
	 * 主键
	 */
	private Long id;

	/**
	 * 创建时间
	 */
	private Date created;

	/**
	 * 修改时间
	 */
	private Date modified;

	/**
	 * 商品id
	 */
	private String goodsId;

	/**
	 * 商品名称
	 */
	private String goodsName;

	/**
	 * 商品skuid
	 */
	private String skuId;

	/**
	 * 商品sku名称
	 */
	private String skuName;

	/**
	 * 规格描述
	 */
	private String skuSpec;

	/**
	 * 商品结算价格
	 */
	private Integer skuSettlementPrice;

	/**
	 * 上架状态（根据sku是否有销售转态决定 0 下架 1 上架）
	 */
	private Integer saleStatus;

	/**
	 * 商品供货类型（0 自营/1 厂家供货 ）
	 */
	private Integer supplyType;

	/**
	 * 商品图片地址
	 */
	private String imgUrl;

	/**
	 * 厂家id
	 */
	private String factoryUserId;

	/**
	 * 厂家名称
	 */
	private String factoryUserName;

	/**
	 * 商家id
	 */
	private String mallUserId;

	/**
	 * 商家名
	 */
	private String mallUserName;

	/**
	 * 绑定状态（0 未绑定 1 已绑定 2 虚拟绑定 通过订单号绑定）
	 */
	private Integer bindStatus;

	/**
	 * 绑定时间
	 */
	private Date bindTime;

	/**
	 * 解绑时间
	 */
	private Date unbindTime;

	/**
	 * 有效期
	 */
	private Date endTime;

	/**
	 * 扩展
	 */
	private String extra;
}
