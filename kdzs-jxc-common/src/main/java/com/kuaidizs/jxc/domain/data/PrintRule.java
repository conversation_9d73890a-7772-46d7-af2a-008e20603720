package com.kuaidizs.jxc.domain.data;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class PrintRule implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long id;
    private Long groupUserId;
    private String firstRule;
    private String allRule;
    /**
     * 批次
     */
    private String batch;
    private Date printTime;
    private Date created;
    private Date modified;

}
