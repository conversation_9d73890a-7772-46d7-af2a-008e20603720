package com.kuaidizs.jxc.domain.cjdf;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * Date    2022-03-08
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CjdfDistributeLog implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date modifyTime;
    /**
     * 逻辑删除 1：可用 0 不可用
     */
    private Integer enableStatus;
    /**
     * 用户ID
     */
    private Long taobaoId;
    /**
     * 订单编号
     */
    private String oid;
    /**
     * 代发订单编号
     */
    private String scpTid;
    /**
     * 供应商平台ID
     */
    private Long supplierId;
    /**
     * 供应商平台名称
     */
    private String supplierName;
    /**
     * 分配状态 2：已分配 3：取消分配 4：分配失败(不展示，enable_status置为0) 5：取消分配失败(不展示，enable_status置为0)
     */
    private Integer distributeStatus;
    /**
     * 操作人对应的淘宝ID
     */
    private Long operatorId;
    /**
     * 操作人淘宝昵称，精确到子账号
     */
    private String operatorNick;
    /**
     * 平台颁发的每次请求访问的唯一标识
     */
    private String requestId;

}