package com.kuaidizs.jxc.domain.log;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 商家配送日志
 *
 * <AUTHOR>
 * @date 2022/12/6 4:30 下午
 */
@Data
@TableName("seller_send_log")
public class SellerSendLog implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 创建时间
     */
    private Date created;

    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 逻辑删除状态 0表示删除 1表示未删除
     */
    private Integer enableStatus;

    /**
     * 淘宝ID
     */
    private Long taobaoId;

    /**
     * 操作人对应的淘宝ID
     */
    private Long operatorId;

    /**
     * 操作人淘宝昵称，精确到子账号
     */
    private String operatorNick;

    /**
     * 订单号
     */
    private String tid;

    /**
     * 子订单id串值(oid1,oid2)
     */
    private String oids;

    /**
     * 配送员姓名
     */
    private String deliveryName;

    /**
     * 配送员手机号
     */
    private String deliveryMobile;

}