package com.kuaidizs.jxc.domain.item;

import java.util.*;
import java.io.Serializable;

/**
 * <AUTHOR> (<EMAIL> )
 * @date 2016-07-21
 */
public class ProductOnline implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 商品主键id
     */
    private Long poid;
    /**
     * 所属用户id
     */
    private Long taobaoId;
    /**
     * 商品的标题
     */
    private String ptitle;
    /**
     * 商品的主图地址
     */
    private String picUrl;
    /**
     * 商品的简称
     */
    private String shortTitle;
    /**
     * 商家编码
     */
    private String outerIid;
    /**
     * 商品的原始的id例：num_iid
     */
    private String numIid;
    /**
     * 平台类型：1 淘宝
     */
    private Integer platformType = 1;
    /**
     * 类目id
     */
    private String cid;
    /**
     * 类目id串
     */
    private String cids;
    /**
     * 是否是分销 0：店铺 1：分销，2：代销
     */
    private Integer type;
    /**
     * 商品的销售状态 1：在售 0：仓库 2：删除
     */
    private Integer isSale;
    /**
     * 是否合并1：是 0：否
     * 平台商品所有的sku都已经关联，认为商品已经关联
     */
    private Integer isMerge;
    /**
     * 用户分类
     */
    private String sellerCat;
    /**
     * 属性值别名,比如颜色的自定义名称
     */
    private String propertyAlias;
    /**
     * 当前状态(1:启用，0:删除)
     */
    private Boolean enableStatus;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间（根据当前时间戳更新）
     */
    private Date modified;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 修改人
     */
    private Long updateBy;

    /**
     * 平台商品sku集合
     */
    private List<ProductSkuOnline> productSkuOnlineList;
    /**
     * 平台商品对应的关联库商品
     */
    private ProductBase productBase;

    /**
     * 平台商品和库商品的关联id
     */
    private Long id;
    /**
     * 判断平台商品和库商品的关联关系
     * 0：正常
     * 1：关联关系不对等（库商品无sku平台商品有或者库商品有sku平台商品无）
     */
    private int linkRelationStatus;

    /**
     * 分表标识
     */
    private String tableName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ProductBase getProductBase() {
        return productBase;
    }

    public void setProductBase(ProductBase productBase) {
        this.productBase = productBase;
    }

    public List<ProductSkuOnline> getProductSkuOnlineList() {
        return productSkuOnlineList;
    }

    public void setProductSkuOnlineList(List<ProductSkuOnline> productSkuOnlineList) {
        this.productSkuOnlineList = productSkuOnlineList;
    }

    /**
     * @return poid 商品主键id
     */
    public Long getPoid() {
        return poid;
    }

    /**
     * @param poid 商品主键id
     */
    public void setPoid(Long poid) {
        this.poid = poid;
    }

    /**
     * @return taobaoId 所属用户id
     */
    public Long getTaobaoId() {
        return taobaoId;
    }

    /**
     * @param taobaoId 所属用户id
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return ptitle 商品的标题
     */
    public String getPtitle() {
        return ptitle;
    }

    /**
     * @param ptitle 商品的标题
     */
    public void setPtitle(String ptitle) {
        this.ptitle = ptitle;
    }

    /**
     * @return picUrl 商品的主图地址
     */
    public String getPicUrl() {
        return picUrl;
    }

    /**
     * @param picUrl 商品的主图地址
     */
    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    /**
     * @return shortTitle 商品的简称
     */
    public String getShortTitle() {
        return shortTitle;
    }

    /**
     * @param shortTitle 商品的简称
     */
    public void setShortTitle(String shortTitle) {
        this.shortTitle = shortTitle;
    }

    /**
     * @return outerIid 商家编码
     */
    public String getOuterIid() {
        return outerIid;
    }

    /**
     * @param outerIid 商家编码
     */
    public void setOuterIid(String outerIid) {
        this.outerIid = outerIid;
    }

    /**
     * @return numIid 商品的原始的id例：num_iid
     */
    public String getNumIid() {
        return numIid;
    }

    /**
     * @param numIid 商品的原始的id例：num_iid
     */
    public void setNumIid(String numIid) {
        this.numIid = numIid;
    }

    /**
     * @return platformType 平台类型：1 淘宝
     */
    public Integer getPlatformType() {
        return platformType;
    }

    /**
     * @param platformType 平台类型：1 淘宝
     */
    public void setPlatformType(Integer platformType) {
        this.platformType = platformType;
    }

    /**
     * @return cid 类目id
     */
    public String getCid() {
        return cid;
    }

    /**
     * @param cid 类目id
     */
    public void setCid(String cid) {
        this.cid = cid;
    }

    /**
     * @return cids 类目id串
     */
    public String getCids() {
        return cids;
    }

    /**
     * @param cids 类目id串
     */
    public void setCids(String cids) {
        this.cids = cids;
    }

    /**
     * @return type 是否是分销 0：店铺 1：分销，2：代销
     */
    public Integer getType() {
        return type;
    }

    /**
     * @param type 是否是分销 0：店铺 1：分销，2：代销
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * @return isSale 商品的销售状态 1：在售 0：仓库 2：删除
     */
    public Integer getIsSale() {
        return isSale;
    }

    /**
     * @param isSale 商品的销售状态 1：在售 0：仓库 2：删除
     */
    public void setIsSale(Integer isSale) {
        this.isSale = isSale;
    }

    /**
     * @return isMerge 是否合并1：是 0：否
     */
    public Integer getIsMerge() {
        return isMerge;
    }

    /**
     * @param isMerge 是否合并1：是 0：否
     */
    public void setIsMerge(Integer isMerge) {
        this.isMerge = isMerge;
    }

    /**
     * @return sellerCat 用户分类
     */
    public String getSellerCat() {
        return sellerCat;
    }

    /**
     * @param sellerCat 用户分类
     */
    public void setSellerCat(String sellerCat) {
        this.sellerCat = sellerCat;
    }

    /**
     * @return propertyAlias 属性值别名,比如颜色的自定义名称
     */
    public String getPropertyAlias() {
        return propertyAlias;
    }

    /**
     * @param propertyAlias 属性值别名,比如颜色的自定义名称
     */
    public void setPropertyAlias(String propertyAlias) {
        this.propertyAlias = propertyAlias;
    }

    /**
     * @return enableStatus 当前状态(1:启用，0:删除)
     */
    public Boolean getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 当前状态(1:启用，0:删除)
     */
    public void setEnableStatus(Boolean enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间（根据当前时间戳更新）
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间（根据当前时间戳更新）
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return createBy 创建人
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * @param createBy 创建人
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * @return updateBy 修改人
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * @param updateBy 修改人
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public int getLinkRelationStatus() {
        return linkRelationStatus;
    }

    public void setLinkRelationStatus(int linkRelationStatus) {
        this.linkRelationStatus = linkRelationStatus;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
}