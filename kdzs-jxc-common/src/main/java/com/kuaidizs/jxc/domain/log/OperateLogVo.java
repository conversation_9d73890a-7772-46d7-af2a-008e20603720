package com.kuaidizs.jxc.domain.log;

import java.util.Date;

public class OperateLogVo {

    /**
     * 修改日志/合并日志
     */
    private String name;
    /**
     * 修改时间/合并时间
     */
    private Date dateTime;
    /**
     * 修改类型
     */
    private String typeName;
    /**
     * 旧值
     */
    private String oldValue;
    /**
     * 新值
     */
    private String newValue;
    /**
     * 成功/失败
     */
    private String result;
    /**
     * ip地址
     */
    private String ip;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 登录着主店铺id
     */
    private Long taobaoId;

    /**
     * 子类型
     */
    private Integer subType;

    /**
     * 手工单删除的订单编号
     */
    private String handTid;

    /**
     * 订单id
     */
    private String tid;

    /**
     * 快递模版id
     */
    private String templateId;

    /**
     * 快递模版名称
     */
    private String templateName;

    /**
     * 操作入口
     */
    private Integer source;

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public Date getDateTime() {
        return dateTime;
    }

    public void setDateTime(Date dateTime) {
        this.dateTime = dateTime;
    }

    public String getTypeName() {
        return typeName;
    }

    public void setTypeName(String typeName) {
        this.typeName = typeName;
    }

    public String getOldValue() {
        return oldValue;
    }

    public void setOldValue(String oldValue) {
        this.oldValue = oldValue;
    }

    public String getNewValue() {
        return newValue;
    }

    public void setNewValue(String newValue) {
        this.newValue = newValue;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public Integer getSubType() {
        return subType;
    }

    public void setSubType(Integer subType) {
        this.subType = subType;
    }

    public String getHandTid() {
        return handTid;
    }

    public void setHandTid(String handTid) {
        this.handTid = handTid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }
}
