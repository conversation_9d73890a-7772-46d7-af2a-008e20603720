package com.kuaidizs.jxc.domain.print.enums;

/**
 * 助手快递公司编码转换为菜鸟品牌编码枚举
 */
public enum EnumKdCodeToCnBrandCode {

    /**
     * 顺丰速运
     */
    SF("SF", "SF"),

    /**
     * 丰网速运
     */
    FW("FENGWANG", "FW"),

    /**
     * 顺丰快运
     */
    SFKY("LE03904430", "FOP");

    private String kdCode;

    private String brandCode;

    EnumKdCodeToCnBrandCode(String kdCode, String brandCode) {
        this.kdCode = kdCode;
        this.brandCode = brandCode;
    }

    public String getKdCode() {
        return kdCode;
    }

    public String getBrandCode() {
        return brandCode;
    }

    public static String getBrandCodeByKdCode(String kdCode) {
        for (EnumKdCodeToCnBrandCode value : EnumKdCodeToCnBrandCode.values()) {
            if (value.getKdCode().equals(kdCode)) {
                return value.getBrandCode();
            }
        }
        return null;
    }
}
