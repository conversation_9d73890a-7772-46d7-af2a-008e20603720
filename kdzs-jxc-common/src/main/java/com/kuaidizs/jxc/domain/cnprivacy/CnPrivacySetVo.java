package com.kuaidizs.jxc.domain.cnprivacy;

import java.io.Serializable;
import java.util.Date;

/***
 * program: kdzs-jxc
 * description: 
 * author: z<PERSON><PERSON><PERSON>@raycloud.com
 * create: 2020-08-05 21:07
 **/
public class CnPrivacySetVo implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键自增id
     */
    private Long id;
    /**
     * 用户淘宝id
     */
    private Long taobaoId;
    /**
     * 是否开启菜鸟隐私面单：1:开启0:关闭 默认0
     */
    private Integer privacySwitch = 0;

    /**
     * 是否允许其他店铺使用改帐号打印隐私面单，1:开启，0:关闭 ，默认0
     */
    private Integer otherSwitch = 0;

    /**
     * 匹配规则 0:没选中任何一项 ，1:全部，2:部分，默认 0
     */
    private Integer matchRule = 0;
    /**
     * 状态：1:有效0:无效 默认1
     */
    private Integer status = 1;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 保护的内容选项
     */
    private ProtectContent guardContent;

    /**
     * 部分匹配的规则
     */
    private Rule rule;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public Integer getPrivacySwitch() {
        return privacySwitch;
    }

    public void setPrivacySwitch(Integer privacySwitch) {
        this.privacySwitch = privacySwitch;
    }

    public Integer getOtherSwitch() {
        return otherSwitch;
    }

    public void setOtherSwitch(Integer otherSwitch) {
        this.otherSwitch = otherSwitch;
    }

    public Integer getMatchRule() {
        return matchRule;
    }

    public void setMatchRule(Integer matchRule) {
        this.matchRule = matchRule;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public ProtectContent getGuardContent() {
        return guardContent;
    }

    public void setGuardContent(ProtectContent guardContent) {
        this.guardContent = guardContent;
    }

    public Rule getRule() {
        return rule;
    }

    public void setRule(Rule rule) {
        this.rule = rule;
    }

    /**
     * 保护内容
     */
    public static class ProtectContent {

        private static final long serialVersionUID = 1L;
        /**
         * 保护收件人姓名 1：保护 0：不保护
         */
        private Integer protectReceiverName = 1;
        /**
         * 保护收件人手机号
         */
        private Integer protectReceiverPhone = 1;
        /**
         * 保护发件人姓名
         */
        private Integer protectSenderName = 0;
        /**
         * 保护发件人手机号
         */
        private Integer protectSenderPhone = 0;

        public Integer getProtectReceiverName() {
            return protectReceiverName;
        }

        public void setProtectReceiverName(Integer protectReceiverName) {
            this.protectReceiverName = protectReceiverName;
        }

        public Integer getProtectReceiverPhone() {
            return protectReceiverPhone;
        }

        public void setProtectReceiverPhone(Integer protectReceiverPhone) {
            this.protectReceiverPhone = protectReceiverPhone;
        }

        public Integer getProtectSenderName() {
            return protectSenderName;
        }

        public void setProtectSenderName(Integer protectSenderName) {
            this.protectSenderName = protectSenderName;
        }

        public Integer getProtectSenderPhone() {
            return protectSenderPhone;
        }

        public void setProtectSenderPhone(Integer protectSenderPhone) {
            this.protectSenderPhone = protectSenderPhone;
        }
    }

    /**
     * 匹配的规则
     */
    public static class Rule {

        private static final long serialVersionUID = 1L;
        /**
         * 宝贝匹配规则是否开启 1：开启 0：不开启
         */
        private Integer goodRule = 0;
        /**
         * 留言备注匹配规则是否开启
         */
        private Integer remarkRule = 0;

        public Integer getGoodRule() {
            return goodRule;
        }

        public void setGoodRule(Integer goodRule) {
            this.goodRule = goodRule;
        }

        public Integer getRemarkRule() {
            return remarkRule;
        }

        public void setRemarkRule(Integer remarkRule) {
            this.remarkRule = remarkRule;
        }
    }

    public CnPrivacySetVo buildDefaultCnPrivacySetVo(Long taobaoId) {
        CnPrivacySetVo cnPrivacySetVo = new CnPrivacySetVo();
        cnPrivacySetVo.setTaobaoId(taobaoId);
        cnPrivacySetVo.setGuardContent(new ProtectContent());
        cnPrivacySetVo.setRule(new Rule());
        return cnPrivacySetVo;
    }
}
