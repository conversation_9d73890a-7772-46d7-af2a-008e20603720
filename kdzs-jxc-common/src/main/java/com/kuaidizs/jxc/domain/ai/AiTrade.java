package com.kuaidizs.jxc.domain.ai;


import com.kuaidizs.jxc.common.enums.SellerFlag;
import com.kuaidizs.jxc.common.enums.TradeTypeEnum;
import com.kuaidizs.jxc.common.util.StringUtil;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

@Data
public class AiTrade extends AiBaseDomain {

    /**
     * 主订单号
     */
    private String tid;

    /**
     * 订单状态
     */
    private String status;
    /**
     * 买家名称
     */
    private String buyerNick;
    /**
     * 买家openid
     */
    private String buyerOpenId;
    /**
     * 收件人名称
     */
    private String receiverName;

    /**
     * 收件人手机号
     */
    private String receiverMobile;

    /**
     * 收件人省份
     */
    private String receiverProvince;

    /**
     * 收件人城市
     */
    private String receiverCity;

    /**
     * 收件人区县
     */
    private String receiverDistrict;

    /**
     * 收件人详细地址
     */
    private String receiverAddress;

    /**
     * 实付金额
     */
    private String payment;

    /**
     * 付款时间
     */
    private Date payTime;

    /**
     * 期望发货时间
     */
    private Date expectedShippingTime;

    /**
     * 卖家备注旗帜
     */
    private SellerFlag sellerFlag;

    /**
     * 卖家备注
     */
    private String sellerMemo;

    /**
     * 买家留言
     */
    private String buyerMessage;

    /**
     * 淘宝订单创建时间
     */
    private Date tbCreated;

    /**
     * 淘宝订单更新时间
     */
    private Date tbModified;

    /**
     * oaid
     */
    private String oaid;

    /**
     * 是否可合并订单
     */
    private Boolean isMergeTrade;

    /**
     * 可合并的订单，该值一致，目前取可合并订单中的“最小”订单号
     */
    private String mergeKey;

    /**
     * 订单商品信息
     */
    private List<AiOrder> orders;

    /**
     * 数据版本
     */
    private Long version;

    /**
     * 订单类型
     */
    private List<TradeTypeEnum> tradeTypes;


    /**
     * 订单类型的64位编码
     */
    private Long tradeTypeCode;

    /**
     * 卖家备注旗帜编码
     */
    private String sellerFlagCode;


    public void initFieldsBeforeUpdate() {
        if (CollectionUtils.isNotEmpty(this.tradeTypes)) {
            this.tradeTypeCode = TradeTypeEnum.bitMaskOfOr(this.tradeTypes);
        }
        if (this.sellerFlag != null) {
            this.sellerFlagCode = this.sellerFlag.getCode();
        }
    }

    public void initFieldsForResult() {
        if (this.tradeTypeCode != null) {
            this.tradeTypes = TradeTypeEnum.getByBitMask(this.tradeTypeCode);
        }
        if (StringUtils.isNotBlank(this.sellerFlagCode)) {
            this.sellerFlag = SellerFlag.getByCode(this.sellerFlagCode);
        }
    }

    public void initFieldsBeforeInsert() {
        //update需要初始化的，insert肯定都要
        this.initFieldsBeforeUpdate();

        //处理一下默认值的情况
        if (CollectionUtils.isEmpty(this.tradeTypes)) {
            this.tradeTypeCode = 0L;
        }
        if (this.getSellerFlag() == null) {
            this.sellerFlag = SellerFlag.NONE;
        }
        //一些不是很重要的字段，按数据库分配的长度截断一下
        this.sellerMemo = StringUtil.frontSubstring(this.sellerMemo, 512);
        this.buyerMessage = StringUtil.frontSubstring(this.buyerMessage, 512);
    }

}
