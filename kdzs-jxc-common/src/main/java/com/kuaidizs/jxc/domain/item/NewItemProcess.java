package com.kuaidizs.jxc.domain.item;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/4/8.
 * @time 14:17.
 */
public class NewItemProcess implements Serializable {

    /**
     *序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long taobaoId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date modifyTime;
    /**
     * 0 默认   1：失败  2：成功
     */
    private Integer progress;
    /**
     * 同步失败时的错误信息
     */
    private String errorMsg;
    /**
     * 迁移标记 0 默认。 1 ：迁移失败  2：迁移成功
     */
    private Integer moveFlag;


    /**
     * @return id 主键
     */
    public Long getId() {
        return id;
    }
    /**
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return taobaoId 用户ID
     */
    public Long getTaobaoId() {
        return taobaoId;
    }
    /**
     * @param taobaoId 用户ID
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return createTime 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }
    /**
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return modifyTime 修改时间
     */
    public Date getModifyTime() {
        return modifyTime;
    }
    /**
     * @param modifyTime 修改时间
     */
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * 0 默认   1：失败  2：成功
     * @return progress
     */
    public Integer getProgress() {
        return progress;
    }
    /**
     * @param progress 0 默认   1：失败  2：成功
     */
    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    /**
     * @return errorMsg 同步失败时的错误信息
     */
    public String getErrorMsg() {
        return errorMsg;
    }
    /**
     * @param errorMsg 同步失败时的错误信息
     */
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    /**
     * @return moveFlag 迁移标记 0 默认。 1 ：迁移失败  2：迁移成功
     */
    public Integer getMoveFlag() {
        return moveFlag;
    }
    /**
     * @param moveFlag 迁移标记 0 默认。 1 ：迁移失败  2：迁移成功
     */
    public void setMoveFlag(Integer moveFlag) {
        this.moveFlag = moveFlag;
    }

}
