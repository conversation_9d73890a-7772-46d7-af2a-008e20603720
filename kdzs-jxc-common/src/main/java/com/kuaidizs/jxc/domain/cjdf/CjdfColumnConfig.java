package com.kuaidizs.jxc.domain.cjdf;

import lombok.Data;

import java.util.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * Date    2022-03-02
 */
@Data
public class CjdfColumnConfig implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    private Long taobaoId;
    /**
     * 主键
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date modifyTime;
    /**
     * 逻辑删除 1：可用 0 不可用
     */
    private Integer enableStatus;
    /**
     * 列配置
     */
    private String columnConfig;

}