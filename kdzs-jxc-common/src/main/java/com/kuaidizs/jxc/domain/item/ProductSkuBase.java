package com.kuaidizs.jxc.domain.item;

import java.util.*;
import java.math.BigDecimal;
import java.io.Serializable;

/**
 * <AUTHOR> (<EMAIL>)
 * @date 2016-07-22
 */
public class ProductSkuBase implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 产品sku记录表id
     */
    private Long sid;
    /**
     * 所属用户id
     */
    private Long taobaoId;
    /**
     * 库商品id
     */
    private Long pid;
    /**
     * 宝贝在平台上的sukid
     */
    private String skuId;
    /**
     * outer_id
     */
    private String outerId;
    /**
     * short_title
     */
    private String shortTitle;
    /**
     * sku的销售属性组合字符串（颜色，大小，等等，可通过类目API获取某类目下的销售属性）,格式是p1:v1;p2:v2
     */
    private String properties;
    /**
     * sku所对应的销售属性的中文名字串，格式如：pid1:vid1:pid_name1:vid_name1;pid2:vid2:pid_name2:vid_name2……
     */
    private String propertiesName;
    /**
     * sku的价格(没有规格的产品此项存产品的价格)
     */
    private BigDecimal price;
    /**
     * sku条形码(没有规格的产品此项存产品的条形码)
     */
    private String barcode;
    /**
     * 是否是默认的sku，用来标明多个合并以后sku的名称取哪个,1是，0否
     */
    private Integer isDef;
    /**
     * 是否是组合商品
     */
    private Integer isCombination;
    /**
     * weight
     */
    private Double weight;
    /**
     * cost_price
     */
    private BigDecimal costPrice;
    /**
     * 当前状态(1:启用，0:删除)
     */
    private Boolean enableStatus;
    /**
     * 库商品sku的状态 1：正常0:隐藏
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 修改人
     */
    private Long updateBy;

    /**
     * 货位
     */
    private String goodsAllocation;

    /**
     * 分表标识
     */
    private String tableName;

    /**
     * 销量（ps:采购单使用）
     */
    private int saleNum;

    public int getSaleNum() {
        return saleNum;
    }

    public void setSaleNum(int saleNum) {
        this.saleNum = saleNum;
    }

    public String getGoodsAllocation() {
        return goodsAllocation;
    }

    public void setGoodsAllocation(String goodsAllocation) {
        this.goodsAllocation = goodsAllocation;
    }

    /**
     * @return sid 产品sku记录表id
     */
    public Long getSid() {
        return sid;
    }

    /**
     * @param sid 产品sku记录表id
     */
    public void setSid(Long sid) {
        this.sid = sid;
    }

    /**
     * @return taobaoId 所属用户id
     */
    public Long getTaobaoId() {
        return taobaoId;
    }

    /**
     * @param taobaoId 所属用户id
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return pid 库商品id
     */
    public Long getPid() {
        return pid;
    }

    /**
     * @param pid 库商品id
     */
    public void setPid(Long pid) {
        this.pid = pid;
    }

    /**
     * @return skuId 宝贝在平台上的sukid
     */
    public String getSkuId() {
        return skuId;
    }

    /**
     * @param skuId 宝贝在平台上的sukid
     */
    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    /**
     * @return outerId outer_id
     */
    public String getOuterId() {
        return outerId;
    }

    /**
     * @param outerId outer_id
     */
    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    /**
     * @return shortTitle short_title
     */
    public String getShortTitle() {
        return shortTitle;
    }

    /**
     * @param shortTitle short_title
     */
    public void setShortTitle(String shortTitle) {
        this.shortTitle = shortTitle;
    }

    /**
     * @return properties sku的销售属性组合字符串（颜色，大小，等等，可通过类目API获取某类目下的销售属性）,格式是p1:v1;p2:v2
     */
    public String getProperties() {
        return properties;
    }

    /**
     * @param properties sku的销售属性组合字符串（颜色，大小，等等，可通过类目API获取某类目下的销售属性）,格式是p1:v1;p2:v2
     */
    public void setProperties(String properties) {
        this.properties = properties;
    }

    /**
     * @return propertiesName sku所对应的销售属性的中文名字串，格式如：pid1:vid1:pid_name1:vid_name1;pid2:vid2:pid_name2:vid_name2……
     */
    public String getPropertiesName() {
        return propertiesName;
    }

    /**
     * @param propertiesName sku所对应的销售属性的中文名字串，格式如：pid1:vid1:pid_name1:vid_name1;pid2:vid2:pid_name2:vid_name2……
     */
    public void setPropertiesName(String propertiesName) {
        this.propertiesName = propertiesName;
    }

    /**
     * @return price sku的价格(没有规格的产品此项存产品的价格)
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * @param price sku的价格(没有规格的产品此项存产品的价格)
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * @return barcode sku条形码(没有规格的产品此项存产品的条形码)
     */
    public String getBarcode() {
        return barcode;
    }

    /**
     * @param barcode sku条形码(没有规格的产品此项存产品的条形码)
     */
    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    /**
     * @return isDef 是否是默认的sku，用来标明多个合并以后sku的名称取哪个,1是，0否
     */
    public Integer getIsDef() {
        return isDef;
    }

    /**
     * @param isDef 是否是默认的sku，用来标明多个合并以后sku的名称取哪个,1是，0否
     */
    public void setIsDef(Integer isDef) {
        this.isDef = isDef;
    }

    /**
     * @return isCombination 是否是组合商品
     */
    public Integer getIsCombination() {
        return isCombination;
    }

    /**
     * @param isCombination 是否是组合商品
     */
    public void setIsCombination(Integer isCombination) {
        this.isCombination = isCombination;
    }

    /**
     * @return weight weight
     */
    public Double getWeight() {
        return weight;
    }

    /**
     * @param weight weight
     */
    public void setWeight(Double weight) {
        this.weight = weight;
    }

    /**
     * @return costPrice cost_price
     */
    public BigDecimal getCostPrice() {
        return costPrice;
    }

    /**
     * @param costPrice cost_price
     */
    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    /**
     * @return enableStatus 当前状态(1:启用，0:删除)
     */
    public Boolean getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 当前状态(1:启用，0:删除)
     */
    public void setEnableStatus(Boolean enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return status 库商品sku的状态 1：正常0:隐藏
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * @param status 库商品sku的状态 1：正常0:隐藏
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return createBy 创建人
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * @param createBy 创建人
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * @return updateBy 修改人
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * @param updateBy 修改人
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
}