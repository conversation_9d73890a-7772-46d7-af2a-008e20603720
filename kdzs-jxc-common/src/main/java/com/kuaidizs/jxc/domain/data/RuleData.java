package com.kuaidizs.jxc.domain.data;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class RuleData {

    private boolean openIdIsSame; // 买家 openId 是否一样

    private boolean isMerge; // 是否合单

    private boolean provinceIsSame; // 省份是否相同

    private boolean cityIsSame; // 市是否相同

    private boolean districtIsSame; // 区是否相同

    private boolean streetIsSame; // 街道是否相同

    private int proportionOfProductId; // 相同商品id占比

    private int proportionOfSkuId; // 相同规格id占比

    private int similarityOfProduct; // 商品名称相似度占比

    private int similarityOfSku; // 规格名称相似度占比

    private int similarityOfOuterId; // 商家编码相似度占比

    private int similarityOfSkuAndOuterId; // 规格商家编码相似度占比

    private int similarityOfShortTitle; // 商品简称相似度占比

    private int similarityOfSkuAlias; // 规格别名相似度占比

    private boolean marketIsSame; // 市场是否相同

    private boolean stallIsSame; // 档口是否相同

    private int similarityOfSellerMemo; // 卖家备注相似度占比

    private int similarityOfBuyerMessage; // 买家留言相似度占比

    private boolean flagIsSame; // 旗帜是否相同

}
