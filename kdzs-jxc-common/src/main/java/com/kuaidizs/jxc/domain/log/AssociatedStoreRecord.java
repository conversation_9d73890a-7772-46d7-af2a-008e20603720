package com.kuaidizs.jxc.domain.log;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/9/5.
 * @time 10:30.
 */
public class AssociatedStoreRecord implements Serializable {


    private static final long serialVersionUID = 2187247307242549638L;
    /**
     * 主键
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date modifyTime;
    /**
     * 限制过的店铺分组id
     */
    private Long groupId;


    /**
     * @return id 主键
     */
    public Long getId() {
        return id;
    }
    /**
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return createTime 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }
    /**
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return modifyTime 修改时间
     */
    public Date getModifyTime() {
        return modifyTime;
    }
    /**
     * @param modifyTime 修改时间
     */
    public void setModifyTime(Date modifyTime) {
        this.modifyTime = modifyTime;
    }

    /**
     * @return groupId 限制过的店铺分组id
     */
    public Long getGroupId() {
        return groupId;
    }
    /**
     * @param groupId 限制过的店铺分组id
     */
    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

}
