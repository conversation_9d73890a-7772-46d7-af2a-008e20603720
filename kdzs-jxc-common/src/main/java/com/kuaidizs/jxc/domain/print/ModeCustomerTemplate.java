package com.kuaidizs.jxc.domain.print;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;

import java.util.*;
import java.io.Serializable;

/**
 *
 * <AUTHOR> @date    2016-07-26
 */
public class ModeCustomerTemplate implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 自增主键ID
     */
    @JSONField(name="Id",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer id;
	/**
     * 模板主键ID
     */
    @JSONField(name="Exshowid",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Integer exshowId;
	/**
     * 用户ID
     */
    @JSONField(name="Exuserid",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Long taobaoId;
	/**
     * 创建时间
     */
    @JSONField(name="Addtime",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Date created;
	/**
     * 修改时间
     */
    @JSONField(name="Updatetime",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private Date modified;
	/**
     * 逻辑状态
     */
    private Boolean enableStatus;
	/**
     * 加密代码
     */
    @JSONField(name="Configcode",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String configCode;
	/**
     * 预留字段1
     */
    @JSONField(name="Reserve1",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String reserve1;
	/**
     * 预留字段1
     */
    @JSONField(name="Reserve2",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String reserve2;
	/**
     * 预留字段1
     */
    @JSONField(name="Reserve3",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String reserve3;
	/**
     * 预留字段1
     */
    @JSONField(name="Reserve4",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String reserve4;
	/**
     * 服务类型Key 暂存
     */
    @JSONField(name="Typekey",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String typeKey;
	/**
     * 服务类型名称
     */
    @JSONField(name="Svctypename",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String svcTypeName;
	/**
     * 服务类型ID
     */
    @JSONField(name="Svctypecode",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String svcTypeCode;
	/**
     * 服务项的值
     */
    @JSONField(name="ServiceValue",serialzeFeatures = {SerializerFeature.WriteMapNullValue,SerializerFeature.WriteNullStringAsEmpty})
    private String svcValue;
    /**
     * 打印设置对象
     */
    private ModeTempPrintCfg modeTempPrintCfg;
    /**
     * 服务项List对象
     */
    private List<ModeServiceItem> modeServiceItemList;
    /**
     * 自定义区选项List对象
     */
    private List<ModeTemplateItem> modeTemplateItemList;
	
   /**
    * @return id 自增主键ID
    */
    public Integer getId() {
       return id;
    }
   /**
    * @param id 自增主键ID
    */
    public void setId(Integer id) {
       this.id = id;
    }
	
   /**
    * @return exshowId 模板主键ID
    */
    public Integer getExshowId() {
       return exshowId;
    }
   /**
    * @param exshowId 模板主键ID
    */
    public void setExshowId(Integer exshowId) {
       this.exshowId = exshowId;
    }
	
   /**
    * @return taobaoId 用户ID
    */
    public Long getTaobaoId() {
       return taobaoId;
    }
   /**
    * @param taobaoId 用户ID
    */
    public void setTaobaoId(Long taobaoId) {
       this.taobaoId = taobaoId;
    }
	
   /**
    * @return created 创建时间
    */
    public Date getCreated() {
       return created;
    }
   /**
    * @param created 创建时间
    */
    public void setCreated(Date created) {
       this.created = created;
    }
	
   /**
    * @return modified 修改时间
    */
    public Date getModified() {
       return modified;
    }
   /**
    * @param modified 修改时间
    */
    public void setModified(Date modified) {
       this.modified = modified;
    }
	
   /**
    * @return enableStatus 逻辑状态
    */
    public Boolean getEnableStatus() {
       return enableStatus;
    }
   /**
    * @param enableStatus 逻辑状态
    */
    public void setEnableStatus(Boolean enableStatus) {
       this.enableStatus = enableStatus;
    }
	
   /**
    * @return configCode 加密代码
    */
    public String getConfigCode() {
       return configCode;
    }
   /**
    * @param configCode 加密代码
    */
    public void setConfigCode(String configCode) {
       this.configCode = configCode;
    }
	
   /**
    * @return reserve1 预留字段1
    */
    public String getReserve1() {
       return reserve1;
    }
   /**
    * @param reserve1 预留字段1
    */
    public void setReserve1(String reserve1) {
       this.reserve1 = reserve1;
    }
	
   /**
    * @return reserve2 预留字段1
    */
    public String getReserve2() {
       return reserve2;
    }
   /**
    * @param reserve2 预留字段1
    */
    public void setReserve2(String reserve2) {
       this.reserve2 = reserve2;
    }
	
   /**
    * @return reserve3 预留字段1
    */
    public String getReserve3() {
       return reserve3;
    }
   /**
    * @param reserve3 预留字段1
    */
    public void setReserve3(String reserve3) {
       this.reserve3 = reserve3;
    }
	
   /**
    * @return reserve4 预留字段1
    */
    public String getReserve4() {
       return reserve4;
    }
   /**
    * @param reserve4 预留字段1
    */
    public void setReserve4(String reserve4) {
       this.reserve4 = reserve4;
    }
	
   /**
    * @return typeKey 服务类型Key 暂存
    */
    public String getTypeKey() {
       return typeKey;
    }
   /**
    * @param typeKey 服务类型Key 暂存
    */
    public void setTypeKey(String typeKey) {
       this.typeKey = typeKey;
    }
	
   /**
    * @return svcTypeName 服务类型名称
    */
    public String getSvcTypeName() {
       return svcTypeName;
    }
   /**
    * @param svcTypeName 服务类型名称
    */
    public void setSvcTypeName(String svcTypeName) {
       this.svcTypeName = svcTypeName;
    }
	
   /**
    * @return svcTypeCode 服务类型ID
    */
    public String getSvcTypeCode() {
       return svcTypeCode;
    }
   /**
    * @param svcTypeCode 服务类型ID
    */
    public void setSvcTypeCode(String svcTypeCode) {
       this.svcTypeCode = svcTypeCode;
    }
	
   /**
    * @return svcValue 服务项的值
    */
    public String getSvcValue() {
       return svcValue;
    }
   /**
    * @param svcValue 服务项的值
    */
    public void setSvcValue(String svcValue) {
       this.svcValue = svcValue;
    }

    public ModeTempPrintCfg getModeTempPrintCfg() {
        return modeTempPrintCfg;
    }

    public void setModeTempPrintCfg(ModeTempPrintCfg modeTempPrintCfg) {
        this.modeTempPrintCfg = modeTempPrintCfg;
    }

    public List<ModeServiceItem> getModeServiceItemList() {
        return modeServiceItemList;
    }

    public void setModeServiceItemList(List<ModeServiceItem> modeServiceItemList) {
        this.modeServiceItemList = modeServiceItemList;
    }

    public List<ModeTemplateItem> getModeTemplateItemList() {
        return modeTemplateItemList;
    }

    public void setModeTemplateItemList(List<ModeTemplateItem> modeTemplateItemList) {
        this.modeTemplateItemList = modeTemplateItemList;
    }
}