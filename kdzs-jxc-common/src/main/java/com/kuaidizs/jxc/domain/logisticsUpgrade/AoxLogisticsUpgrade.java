package com.kuaidizs.jxc.domain.logisticsUpgrade;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/14.
 * @time 14:18.
 */
@Data
public class AoxLogisticsUpgrade implements Serializable {
    private static final long serialVersionUID = -6076573206550693097L;

    /**
     * 订单号
     */
    private Long tid;

    /**
     * asdp_biz_type ：value=aox 为翱象订单
     */
    private String asdpBizType;

    /**
     * 里面多个值时用英文逗号隔开
     * value=201为按需配送服务
     * value=202为顺丰配送服务
     * value=203为承诺发货时效服务
     * value=204为承诺送达时效服务
     * value=205为预售极速达服务（商家决策模式）
     * value=206为预计到货时效服务
     * value=207为配送线路异常服务
     */
    private String asdpAds;

    /**
     * 是否主订单
     */
    private Boolean isMain;

    private String sendExName;
    private String sendExCode;

    /**
     * 最晚送达时间
     */
    private Date signTime ;

    /**
     * ERP应推单时间(主单)
     */
    private Date pushTime;

    private Date collectTime;


    private Date promiseCollectTime;
    /**
     * 承诺/最晚送达时间，日期，格式2019-04-12 16:00:00
     */
    private Date promiseSignTime;

    /**
     * 最晚发货时间
     */
    private Date deliveryTime;

    /**
     * 订单推荐配送类型 *
     * 0：子单无配建议；ERP按照自己的逻辑进行择配。 *
     * 1：子单有推荐配list，erp可按需参考。 *
     * 2：子单有推荐配list，erp必须在推荐配list中选择配品牌。 *
     * 3：子单有禁用配list，erp需要过滤配品牌。
     */
//    private Long bizDeliveryType;


    /**
     * 承诺/最晚出库时间，日期，格式2019-04-12 16:00:00
     */
    private String promiseOutboundTime;

    public Boolean getMain() {
        return isMain;
    }

    public void setMain(Boolean main) {
        isMain = main;
    }

    /**
     * 物流服务标签，一个主单不超过10个标签；每个标签不超过8个字；
     */
    private List<String> logisticsServiceMsg;

    /**
     * 子单物流信息
     */
    private LogisticsInfoVo aoxLogisticsInfo;

}
