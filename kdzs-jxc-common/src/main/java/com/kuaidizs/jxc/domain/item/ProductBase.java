package com.kuaidizs.jxc.domain.item;

import java.math.BigDecimal;
import java.util.*;
import java.io.Serializable;

/**
 * <AUTHOR> (<EMAIL> )
 * @date 2016-07-21
 */
public class ProductBase implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 商品主键id
     */
    private Long pid;
    /**
     * 所属用户id
     */
    private Long taobaoId;
    /**
     * 库产品主标题
     */
    private String title;
    /**
     * 库存产品主图id
     */
    private String picUrl;
    /**
     * short_title
     */
    private String shortTitle;
    /**
     * 商家编码
     */
    private String outerIid;
    /**
     * 宝贝原始id
     */
    private String defPid;
    /**
     * 平台类型1：淘宝
     */
    private Integer platformType = 1;
    /**
     * 是否是在售 1：在售 0：仓库 2：删除
     */
    private Integer isSale;
    /**
     * 是否是组合商品
     */
    private Integer isCombination = 0;
    /**
     * 货位
     */
    private String goodsAllocation;
    /**
     * 当前状态(1:启用，0:删除)
     */
    private Boolean enableStatus = true;
    /**
     * 库商品的来源 1：平台商品复制0：手动添加
     */
    private Integer proFrom = 1;
    /**
     * 库存状态（0：正常，1：预警）
     */
    private Integer stockStatus;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间(根据当前时间戳更新)
     */
    private Date modified;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 修改人
     */
    private Long updateBy;

    /**
     * 商品的成本
     */
    private BigDecimal costPrice;

    /**
     * 商品的重量
     */
    private Double weight;

    /**
     * 库商品所属的规格列表
     */
    private List<ProductSkuBase> productSkuBaseList;
    /**
     * 操作的额库商品skuid集合
     */
    private List<Long> sids;
    /**
     * 分表的表的名称标识
     */
    private String tableName;
    /**
     * 销量（ps:采购单使用）
     */
    private int saleNum;

    public int getSaleNum() {
        return saleNum;
    }

    public void setSaleNum(int saleNum) {
        this.saleNum = saleNum;
    }

    public List<Long> getSids() {
        return sids;
    }

    public void setSids(List<Long> sids) {
        this.sids = sids;
    }

    public List<ProductSkuBase> getProductSkuBaseList() {
        return productSkuBaseList;
    }

    public void setProductSkuBaseList(List<ProductSkuBase> productSkuBaseList) {
        this.productSkuBaseList = productSkuBaseList;
    }

    /**
     * @return pid 商品主键id
     */
    public Long getPid() {
        return pid;
    }

    /**
     * @param pid 商品主键id
     */
    public void setPid(Long pid) {
        this.pid = pid;
    }

    /**
     * @return taobaoId 所属用户id
     */
    public Long getTaobaoId() {
        return taobaoId;
    }

    /**
     * @param taobaoId 所属用户id
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return title 库产品主标题
     */
    public String getTitle() {
        return title;
    }

    /**
     * @param title 库产品主标题
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * @return picUrl 库存产品主图id
     */
    public String getPicUrl() {
        return picUrl;
    }

    /**
     * @param picUrl 库存产品主图id
     */
    public void setPicUrl(String picUrl) {
        this.picUrl = picUrl;
    }

    /**
     * @return shortTitle short_title
     */
    public String getShortTitle() {
        return shortTitle;
    }

    /**
     * @param shortTitle short_title
     */
    public void setShortTitle(String shortTitle) {
        this.shortTitle = shortTitle;
    }

    /**
     * @return outerIid 商家编码
     */
    public String getOuterIid() {
        return outerIid;
    }

    /**
     * @param outerIid 商家编码
     */
    public void setOuterIid(String outerIid) {
        this.outerIid = outerIid;
    }

    /**
     * @return defPid 宝贝原始id
     */
    public String getDefPid() {
        return defPid;
    }

    /**
     * @param defPid 宝贝原始id
     */
    public void setDefPid(String defPid) {
        this.defPid = defPid;
    }

    /**
     * @return platformType 平台类型1：淘宝
     */
    public Integer getPlatformType() {
        return platformType;
    }

    /**
     * @param platformType 平台类型1：淘宝
     */
    public void setPlatformType(Integer platformType) {
        this.platformType = platformType;
    }

    /**
     * @return isSale 是否是在售 1：在售 0：仓库 2：删除
     */
    public Integer getIsSale() {
        return isSale;
    }

    /**
     * @param isSale 是否是在售 1：在售 0：仓库 2：删除
     */
    public void setIsSale(Integer isSale) {
        this.isSale = isSale;
    }

    /**
     * @return isCombination 是否是组合商品
     */
    public Integer getIsCombination() {
        return isCombination;
    }

    /**
     * @param isCombination 是否是组合商品
     */
    public void setIsCombination(Integer isCombination) {
        this.isCombination = isCombination;
    }

    /**
     * @return goodsAllocation 货位
     */
    public String getGoodsAllocation() {
        return goodsAllocation;
    }

    /**
     * @param goodsAllocation 货位
     */
    public void setGoodsAllocation(String goodsAllocation) {
        this.goodsAllocation = goodsAllocation;
    }

    /**
     * @return enableStatus 当前状态(1:启用，0:删除)
     */
    public Boolean getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 当前状态(1:启用，0:删除)
     */
    public void setEnableStatus(Boolean enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return proFrom 库商品的来源 1：平台商品复制0：手动添加
     */
    public Integer getProFrom() {
        return proFrom;
    }

    /**
     * @param proFrom 库商品的来源 1：平台商品复制0：手动添加
     */
    public void setProFrom(Integer proFrom) {
        this.proFrom = proFrom;
    }

    /**
     * @return stockStatus 库存状态（0：正常，1：预警）
     */
    public Integer getStockStatus() {
        return stockStatus;
    }

    /**
     * @param stockStatus 库存状态（0：正常，1：预警）
     */
    public void setStockStatus(Integer stockStatus) {
        this.stockStatus = stockStatus;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间(根据当前时间戳更新)
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间(根据当前时间戳更新)
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return createBy 创建人
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * @param createBy 创建人
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * @return updateBy 修改人
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * @param updateBy 修改人
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public Double getWeight() {
        return weight;
    }

    public void setWeight(Double weight) {
        this.weight = weight;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
}