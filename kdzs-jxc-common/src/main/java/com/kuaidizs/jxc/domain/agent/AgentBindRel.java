package com.kuaidizs.jxc.domain.agent;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 绑定关系
 *
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AgentBindRel.java
 * @createTime 2022年12月12日 10:20:00
 */
@Data
public class AgentBindRel implements Serializable {

	/**
	 * 主键
	 */
	private Long id;

	/**
	 * 创建时间
	 */
	private Date created;

	/**
	 * 修改时间
	 */
	private Date modified;

	/**
	 * 厂家id taobaoid
	 */
	private String factoryUserId;

	/**
	 * 厂家名称
	 */
	private String factoryUserName;

	/**
	 * 商家id taobaoid
	 */
	private String mallUserId;

	/**
	 * 商家名
	 */
	private String mallUserName;

	/**
	 * 代发省
	 */
	private String senderProvince;

	/**
	 * 代发市
	 */
	private String senderCity;

	/**
	 * 代发区
	 */
	private String senderDistrict;

	/**
	 * 代发详细地址
	 */
	private String senderAddress;

	/**
	 * 代发姓名
	 */
	private String senderName;

	/**
	 * 代发手机号
	 */
	private String senderMobile;

	/**
	 * 绑定状态（0 未绑定 1 已绑定）
	 */
	private Integer bindStatus;

	/**
	 * 绑定时间
	 */
	private Date bindTime;

	/**
	 * 解绑时间
	 */
	private Date unbindTime;

	/**
	 * 有效期
	 */
	private Date endTime;

	/**
	 * 顺序
	 */
	private Integer sort;

	/**
	 * 扩展
	 */
	private String extra;
}
