package com.kuaidizs.jxc.domain.oaid;

import java.io.Serializable;

/***
 * program:
 * description: 
 * author: z<PERSON><PERSON><PERSON>@raycloud.com
 * create: 2021-03-04 14:14
 **/
public class OaidDecryptRequest implements Serializable {


    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * oaid
     */
    private String oaid;

    /**
     * 该oaid对应的订单号
     */
    private String tid;

    private String sceneCode;

    public String getOaid() {
        return oaid;
    }

    public void setOaid(String oaid) {
        this.oaid = oaid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getSceneCode() {
        return sceneCode;
    }

    public void setSceneCode(String sceneCode) {
        this.sceneCode = sceneCode;
    }
}
