package com.kuaidizs.jxc.domain.item;

import java.util.*;
import java.io.Serializable;

/**
 * <AUTHOR> (<EMAIL>)
 * @date 2016-07-22
 */
public class ProductSkuRelation implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 所属用户id
     */
    private Long taobaoId;
    /**
     * 库商品id
     */
    private Long sid;
    /**
     * 平台商品id
     */
    private Long soid;
    /**
     * 库商品id
     */
    private Long pid;
    /**
     * 平台商品id
     */
    private Long poid;
    /**
     * 当前状态(1:启用，0:删除)
     */
    private Boolean enableStatus;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 修改人
     */
    private Long updateBy;

    /**
     * 关联的库商品sku信息
     */
    private ProductSkuBase productSkuBase;

    /**
     * 关联的平台商品sku信息
     */
    private ProductSkuOnline productSkuOnline;

    /**
     * 分表标识
     */
    private String tableName;


    public ProductSkuBase getProductSkuBase() {
        return productSkuBase;
    }

    public void setProductSkuBase(ProductSkuBase productSkuBase) {
        this.productSkuBase = productSkuBase;
    }

    public ProductSkuOnline getProductSkuOnline() {
        return productSkuOnline;
    }

    public void setProductSkuOnline(ProductSkuOnline productSkuOnline) {
        this.productSkuOnline = productSkuOnline;
    }

    /**
     * @return id 主键
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id 主键
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return taobaoId 所属用户id
     */
    public Long getTaobaoId() {
        return taobaoId;
    }

    /**
     * @param taobaoId 所属用户id
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return sid 库商品id
     */
    public Long getSid() {
        return sid;
    }

    /**
     * @param sid 库商品id
     */
    public void setSid(Long sid) {
        this.sid = sid;
    }

    /**
     * @return soid 平台商品id
     */
    public Long getSoid() {
        return soid;
    }

    /**
     * @param soid 平台商品id
     */
    public void setSoid(Long soid) {
        this.soid = soid;
    }

    /**
     * @return pid 库商品id
     */
    public Long getPid() {
        return pid;
    }

    /**
     * @param pid 库商品id
     */
    public void setPid(Long pid) {
        this.pid = pid;
    }

    /**
     * @return poid 平台商品id
     */
    public Long getPoid() {
        return poid;
    }

    /**
     * @param poid 平台商品id
     */
    public void setPoid(Long poid) {
        this.poid = poid;
    }

    /**
     * @return enableStatus 当前状态(1:启用，0:删除)
     */
    public Boolean getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 当前状态(1:启用，0:删除)
     */
    public void setEnableStatus(Boolean enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return createBy 创建人
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * @param createBy 创建人
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * @return updateBy 修改人
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * @param updateBy 修改人
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
}