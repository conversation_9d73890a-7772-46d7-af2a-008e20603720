package com.kuaidizs.jxc.domain.print.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2024/6/11 16:40
 * 功能性限制 白名单类型
 **/
@Getter
@AllArgsConstructor
public enum FuncationLimitWhiteTypeEnum {
    FREE_VERSION_LIMIT(1, "免费版本限制白名单"),
    BEGINNING_YEAR_ACT_DRAWN(2, "开年活动已抽过奖的用户"),
    ESOM_WHITE_USER(20, "易甚白名单名单"),
    PRODUCT_UPDATES_DO_NOT_DISPLAY_WHITELIST(21, "产品动态不展示白名单"),
    INACTIVE_USER_RDS_DISABLED_WHITE(22, "非活跃用户被禁用rds的白名单，即允许再次注册rds"),
    ;

    private final int code;
    private final String desc;

    public static FuncationLimitWhiteTypeEnum ofCode(int code) {
        for (FuncationLimitWhiteTypeEnum anEnum : FuncationLimitWhiteTypeEnum.values()) {
            if (anEnum.code == code) {
                return anEnum;
            }
        }
        return null;
    }
}
