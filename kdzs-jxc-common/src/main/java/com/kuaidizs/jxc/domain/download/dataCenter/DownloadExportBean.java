package com.kuaidizs.jxc.domain.download.dataCenter;

import com.kuaidizs.jxc.domain.trade.TradeExportConfig;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @auther xudaomeng
 * @since 2020-07-09 15:27
 */
public class DownloadExportBean {

    /**
     * 店铺旺旺
     */
    @TradeExportConfig.ExportField(name = "店铺旺旺", value = 1, order = 1)
    private String taobaoNick;

    /**
     * 客户备注
     */
    @TradeExportConfig.ExportField(name = "客户备注", value = 1, order = 2)
    private String memo;

    /**
     * 快递名称
     */
    @TradeExportConfig.ExportField(name = "快递公司", value = 1, order = 3)
    private String exName;

    /**
     * 网点名称
     */
    @TradeExportConfig.ExportField(name = "网点名称", value = 1, order = 4)
    private String branchName;

    /**
     * 网点编码
     */
    @TradeExportConfig.ExportField(name = "网点编码", value = 1, order = 5)
    private String branchCode;

    /**
     * 快递单号
     */
    @TradeExportConfig.ExportField(name = "运单号", value = 1, order = 6)
    private String ydNo;

    /**
     * 订单编号
     */
    @TradeExportConfig.ExportField(name = "订单编号", value = 1, order = 7)
    private String tid;

    /**
     * 订单所在店铺
     */
    @TradeExportConfig.ExportField(name = "使用单号店铺", value = 1, order = 8)
    private String operator;

    /**
     * 收件地址
     */
    @TradeExportConfig.ExportField(name = "目的地", value = 1, order = 9)
    private String receiveAddr;

    /**
     * 重量
     */
    @TradeExportConfig.ExportField(name = "重量(KG)", value = 1, order = 10)
    private BigDecimal weight;

    /**
     * 运费
     */
    @TradeExportConfig.ExportField(name = "运费(元)", value = 1, order = 11)
    private BigDecimal freight;

    /**
     * 收件人
     */
   /* @TradeExportConfig.ExportField(name = "收件人", value = 1, order = 12)
    private String receiver;*/

    /**
     * 单号状态
     */
    @TradeExportConfig.ExportField(name = "单号状态", value = 1, order = 12)
    private String noStatus;

    /**
     * 平台
     */
    @TradeExportConfig.ExportField(name = "平台", value = 1, order = 13)
    private String platform;

    /**
     * 单号申请时间
     */
    @TradeExportConfig.ExportField(name = "单号申请时间", value = 1, order = 14)
    private Date modified;

    public String getTaobaoNick() {
        return taobaoNick;
    }

    public void setTaobaoNick(String taobaoNick) {
        this.taobaoNick = taobaoNick;
    }

    public String getMemo() {
        return memo;
    }

    public void setMemo(String memo) {
        this.memo = memo;
    }

    public String getExName() {
        return exName;
    }

    public void setExName(String exName) {
        this.exName = exName;
    }

    public String getBranchName() {
        return branchName;
    }

    public void setBranchName(String branchName) {
        this.branchName = branchName;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getYdNo() {
        return ydNo;
    }

    public void setYdNo(String ydNo) {
        this.ydNo = ydNo;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getReceiveAddr() {
        return receiveAddr;
    }

    public void setReceiveAddr(String receiveAddr) {
        this.receiveAddr = receiveAddr;
    }

    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    public BigDecimal getFreight() {
        return freight;
    }

    public void setFreight(BigDecimal freight) {
        this.freight = freight;
    }

    public String getNoStatus() {
        return noStatus;
    }

    public void setNoStatus(String noStatus) {
        this.noStatus = noStatus;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }
}
