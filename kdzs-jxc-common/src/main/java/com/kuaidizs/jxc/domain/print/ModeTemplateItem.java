package com.kuaidizs.jxc.domain.print;

import java.util.*;
import java.io.Serializable;

/**
 * 菜鸟官方模板用户自定义区模板勾选项
 *
 * <AUTHOR> @date 2016-07-26
 */
public class ModeTemplateItem implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    private Integer id;
    /**
     * 用户ID
     */
    private Long taobaoId;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * 逻辑删除
     */
    private Boolean enableStatus;
    /**
     * 编辑快递单时标签页的value 等于
     */
    private Integer exshowId;
    /**
     * 选项key   需要定义一个规则
     */
    private String dataKey;
    /**
     * 选项code
     */
    private String dataName;
    /**
     * 类型:默认kdd(快递单)
     */
    private String itemType;
    /**
     * 前文字
     */
    private String preStr;
    /**
     * 后文字
     */
    private String sufStr;
    /**
     * 预留字段1
     */
    private String reserve1;
    /**
     * 预留字段2
     */
    private String reserve2;
    /**
     * 预留字段3
     */
    private String reserve3;
    /**
     * 预留字段4
     */
    private String reserve4;
    /**
     * 字体
     */
    private String fontName;
    /**
     * 字号
     */
    private Integer fontSize;
    /**
     * 是否加粗 1:加粗 0：不加粗
     */
    private Boolean isB;
    /**
     * x坐标
     */
    private Integer x;
    /**
     * y坐标
     */
    private Integer y;
    /**
     * 宽
     */
    private Integer w;
    /**
     * 高
     */
    private Integer h;


    /**
     * @return id 自增ID
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id 自增ID
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * @return taobaoId 用户ID
     */
    public Long getTaobaoId() {
        return taobaoId;
    }

    /**
     * @param taobaoId 用户ID
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return enableStatus 逻辑删除
     */
    public Boolean getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 逻辑删除
     */
    public void setEnableStatus(Boolean enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return exshowId 编辑快递单时标签页的value 等于
     */
    public Integer getExshowId() {
        return exshowId;
    }

    /**
     * @param exshowId 编辑快递单时标签页的value 等于
     */
    public void setExshowId(Integer exshowId) {
        this.exshowId = exshowId;
    }

    /**
     * @return dataKey 选项key   需要定义一个规则
     */
    public String getDataKey() {
        return dataKey;
    }

    /**
     * @param dataKey 选项key   需要定义一个规则
     */
    public void setDataKey(String dataKey) {
        this.dataKey = dataKey;
    }

    /**
     * @return dataName 选项code
     */
    public String getDataName() {
        return dataName;
    }

    /**
     * @param dataName 选项code
     */
    public void setDataName(String dataName) {
        this.dataName = dataName;
    }

    /**
     * @return itemType 类型:默认kdd(快递单)
     */
    public String getItemType() {
        return itemType;
    }

    /**
     * @param itemType 类型:默认kdd(快递单)
     */
    public void setItemType(String itemType) {
        this.itemType = itemType;
    }

    /**
     * @return preStr 前文字
     */
    public String getPreStr() {
        return preStr;
    }

    /**
     * @param preStr 前文字
     */
    public void setPreStr(String preStr) {
        this.preStr = preStr;
    }

    /**
     * @return sufStr 后文字
     */
    public String getSufStr() {
        return sufStr;
    }

    /**
     * @param sufStr 后文字
     */
    public void setSufStr(String sufStr) {
        this.sufStr = sufStr;
    }

    /**
     * @return reserve1 预留字段1
     */
    public String getReserve1() {
        return reserve1;
    }

    /**
     * @param reserve1 预留字段1
     */
    public void setReserve1(String reserve1) {
        this.reserve1 = reserve1;
    }

    /**
     * @return reserve2 预留字段2
     */
    public String getReserve2() {
        return reserve2;
    }

    /**
     * @param reserve2 预留字段2
     */
    public void setReserve2(String reserve2) {
        this.reserve2 = reserve2;
    }

    /**
     * @return reserve3 预留字段3
     */
    public String getReserve3() {
        return reserve3;
    }

    /**
     * @param reserve3 预留字段3
     */
    public void setReserve3(String reserve3) {
        this.reserve3 = reserve3;
    }

    /**
     * @return reserve4 预留字段4
     */
    public String getReserve4() {
        return reserve4;
    }

    /**
     * @param reserve4 预留字段4
     */
    public void setReserve4(String reserve4) {
        this.reserve4 = reserve4;
    }

    /**
     * @return fontName 字体
     */
    public String getFontName() {
        return fontName;
    }

    /**
     * @param fontName 字体
     */
    public void setFontName(String fontName) {
        this.fontName = fontName;
    }

    /**
     * @return fontSize 字号
     */
    public Integer getFontSize() {
        return fontSize;
    }

    /**
     * @param fontSize 字号
     */
    public void setFontSize(Integer fontSize) {
        this.fontSize = fontSize;
    }

    /**
     * @return isB 是否加粗 1:加粗 0：不加粗
     */
    public Boolean getIsB() {
        return isB;
    }

    /**
     * @param isB 是否加粗 1:加粗 0：不加粗
     */
    public void setIsB(Boolean isB) {
        this.isB = isB;
    }

    /**
     * @return x x坐标
     */
    public Integer getX() {
        return x;
    }

    /**
     * @param x x坐标
     */
    public void setX(Integer x) {
        this.x = x;
    }

    /**
     * @return y y坐标
     */
    public Integer getY() {
        return y;
    }

    /**
     * @param y y坐标
     */
    public void setY(Integer y) {
        this.y = y;
    }

    /**
     * @return w 宽
     */
    public Integer getW() {
        return w;
    }

    /**
     * @param w 宽
     */
    public void setW(Integer w) {
        this.w = w;
    }

    /**
     * @return h 高
     */
    public Integer getH() {
        return h;
    }

    /**
     * @param h 高
     */
    public void setH(Integer h) {
        this.h = h;
    }

}