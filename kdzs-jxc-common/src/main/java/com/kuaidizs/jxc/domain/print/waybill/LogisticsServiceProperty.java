package com.kuaidizs.jxc.domain.print.waybill;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2018/6/26
 */
public class LogisticsServiceProperty implements Serializable {

    /**
     * 是否需要订购
     * 0 不需要 1需要
     */
    private Integer needPurchase;

    /**
     * 是否需要审核
     * 0 不需要 1需要
     */
    private Integer needVerify;

    /**
     * 是否不可操作
     * 默认：false，可操作勾选
     */
    private boolean isDisabled;

    /**
     * 是否默认选中
     * 默认：false，不选中
     */
    private boolean isDefault;

    /**
     * 是否是必须的
     * 默认:false，不必须
     */
    private boolean required;

    public Integer getNeedPurchase() {
        return needPurchase;
    }

    public void setNeedPurchase(Integer needPurchase) {
        this.needPurchase = needPurchase;
    }

    public Integer getNeedVerify() {
        return needVerify;
    }

    public void setNeedVerify(Integer needVerify) {
        this.needVerify = needVerify;
    }

    public boolean isDisabled() {
        return isDisabled;
    }

    public void setDisabled(boolean disabled) {
        isDisabled = disabled;
    }

    public boolean isDefault() {
        return isDefault;
    }

    public void setDefault(boolean aDefault) {
        isDefault = aDefault;
    }

    public boolean isRequired() {
        return required;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }
}
