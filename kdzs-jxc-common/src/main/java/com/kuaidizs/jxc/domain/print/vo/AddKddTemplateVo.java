package com.kuaidizs.jxc.domain.print.vo;

import com.alibaba.fastjson.annotation.JSONField;
import com.kuaidizs.jxc.domain.print.ModeLogisticsItem;

import java.io.Serializable;
import java.util.List;

/**
 * add kdd template vo
 *
 * <AUTHOR>
 * @date 2018/8/1
 */
public class AddKddTemplateVo implements Serializable {

    /**
     * 快递公司ID
     */
    @JSONField(name = "ExCompanyId")
    private Integer exCompanyId;
    /**
     * 快递面单类型
     * {@link com.kuaidizs.jxc.domain.print.enums.EnumExpressType}
     */
    @JSONField(name = "KddType")
    private Integer kddType;
    /**
     * 底图ID
     * 由exCompanyId、KddType、styleId  --> modeList 确定得到exId（基础模版id） --> modeListImgCommon  得到模版底图
     * 模版底图路径:ZTO11.jpg
     */
    private Integer bgImgId;
    /**
     * 底图路径
     * 模版底图id:ZTO11.jpg
     */
    private String bgImg;
    /**
     * 纸张高度
     */
    @JSONField(name = "Height")
    private Integer height;
    /**
     * 样式ID
     */
    @JSONField(name = "StyleId")
    private Integer styleId;
    /**
     * 服务类型
     */
    @JSONField(name = "ExServiceType")
    private List<ExServiceTypeVO> exServiceTypeList;
    /**
     * 服务选项集合
     */
    @JSONField(name = "ExServiceItems")
    private List<ExServiceItemVO> exServiceItemList;
    /**
     * 增值服务
     */
    @JSONField(name = "AdvancedServices")
    private List<ExServiceItemVO> advancedServiceList;
    /**
     * 电子面单物流服务
     */
    @JSONField(name = "ModeLogisticsItems")
    private List<ModeLogisticsItem> modeLogisticsItemList;

    public Integer getExCompanyId() {
        return exCompanyId;
    }

    public void setExCompanyId(Integer exCompanyId) {
        this.exCompanyId = exCompanyId;
    }

    public Integer getKddType() {
        return kddType;
    }

    public void setKddType(Integer kddType) {
        this.kddType = kddType;
    }

    public Integer getBgImgId() {
        return bgImgId;
    }

    public void setBgImgId(Integer bgImgId) {
        this.bgImgId = bgImgId;
    }

    public String getBgImg() {
        return bgImg;
    }

    public void setBgImg(String bgImg) {
        this.bgImg = bgImg;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public Integer getStyleId() {
        return styleId;
    }

    public void setStyleId(Integer styleId) {
        this.styleId = styleId;
    }

    public List<ExServiceTypeVO> getExServiceTypeList() {
        return exServiceTypeList;
    }

    public void setExServiceTypeList(List<ExServiceTypeVO> exServiceTypeList) {
        this.exServiceTypeList = exServiceTypeList;
    }

    public List<ExServiceItemVO> getExServiceItemList() {
        return exServiceItemList;
    }

    public void setExServiceItemList(List<ExServiceItemVO> exServiceItemList) {
        this.exServiceItemList = exServiceItemList;
    }

    public List<ExServiceItemVO> getAdvancedServiceList() {
        return advancedServiceList;
    }

    public void setAdvancedServiceList(List<ExServiceItemVO> advancedServiceList) {
        this.advancedServiceList = advancedServiceList;
    }

    public List<ModeLogisticsItem> getModeLogisticsItemList() {
        return modeLogisticsItemList;
    }

    public void setModeLogisticsItemList(List<ModeLogisticsItem> modeLogisticsItemList) {
        this.modeLogisticsItemList = modeLogisticsItemList;
    }

    public static class ExServiceTypeVO implements Serializable {
        @JSONField(name = "Id")
        private Long id;
        @JSONField(name = "Key")
        private String key;
        @JSONField(name = "Name")
        private String name;
        @JSONField(name = "Value")
        private String value;
        @JSONField(name = "Tips")
        private String tips;

        @Override
        public String toString() {
            return "ExServiceTypeVO{" +
                    "id=" + id +
                    ", key='" + key + '\'' +
                    ", name='" + name + '\'' +
                    ", value='" + value + '\'' +
                    ", tips='" + tips + '\'' +
                    '}';
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String getTips() {
            return tips;
        }

        public void setTips(String tips) {
            this.tips = tips;
        }
    }

    public static class ExServiceItemVO implements Serializable {
        @JSONField(name = "Id")
        private Long id;
        @JSONField(name = "Key")
        private String key;
        @JSONField(name = "Name")
        private String name;
        @JSONField(name = "Value")
        private String value;
        @JSONField(name = "Tips")
        private String tips;
        @JSONField(name = "DataName")
        private String dataName;
        @JSONField(name = "IsEnable")
        private String isEnable;
        @JSONField(name = "IsSetVal")
        private String isSetVal;
        @JSONField(name = "Unit")
        private String unit;

        @Override
        public String toString() {
            return "ExServiceItemVO{" +
                    "id=" + id +
                    ", key='" + key + '\'' +
                    ", name='" + name + '\'' +
                    ", value='" + value + '\'' +
                    ", tips='" + tips + '\'' +
                    ", dataName='" + dataName + '\'' +
                    ", isEnable='" + isEnable + '\'' +
                    ", isSetVal='" + isSetVal + '\'' +
                    ", unit='" + unit + '\'' +
                    '}';
        }

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getValue() {
            return value;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public String getTips() {
            return tips;
        }

        public void setTips(String tips) {
            this.tips = tips;
        }

        public String getDataName() {
            return dataName;
        }

        public void setDataName(String dataName) {
            this.dataName = dataName;
        }

        public String getIsEnable() {
            return isEnable;
        }

        public void setIsEnable(String isEnable) {
            this.isEnable = isEnable;
        }

        public String getIsSetVal() {
            return isSetVal;
        }

        public void setIsSetVal(String isSetVal) {
            this.isSetVal = isSetVal;
        }

        public String getUnit() {
            return unit;
        }

        public void setUnit(String unit) {
            this.unit = unit;
        }
    }

}
