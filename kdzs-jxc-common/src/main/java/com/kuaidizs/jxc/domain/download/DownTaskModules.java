package com.kuaidizs.jxc.domain.download;

/**
 * 下载中心平台
 */
public enum DownTaskModules {

    MODULES_TB("TB", "淘宝订单"),
    MODULES_BG("BG", "底单"),
    MODULES_KDD("KDD", "快递单"),
    MODULES_FHD("FHD", "发货单"),
    MODULES_FH_RECORD("FH_RECORD", "发货记录"),
    MODULES_HAND("HAND", "手工订单"),
    MODULES_BHD_NHM("BHD_NHM", "备货单拿货码"),
    MODULES_BHD_NHM_V2("BHD_NHM_V2", "备货单拿货码");


    private String platform;

    private String platformDes;

    DownTaskModules(String platform, String platformDes) {
        this.platform = platform;
        this.platformDes = platformDes;
    }

    public static String getPlatformDes(String platform) throws Exception {
        DownTaskModules[] values = DownTaskModules.values();
        for (DownTaskModules value : values) {
            if (value.getPlatform().equals(platform)) {
                return value.platformDes;
            }
        }
        return null;
    }

    public String getPlatform() {
        return platform;
    }

    public void setPlatform(String platform) {
        this.platform = platform;
    }

    public String getPlatformDes() {
        return platformDes;
    }

    public void setPlatformDes(String platformDes) {
        this.platformDes = platformDes;
    }
}
