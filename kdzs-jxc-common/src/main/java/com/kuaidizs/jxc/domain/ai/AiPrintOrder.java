package com.kuaidizs.jxc.domain.ai;

import com.kuaidizs.jxc.common.util.StringUtil;
import java.util.Date;
import lombok.Data;

@Data
public class AiPrintOrder {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 打印记录ID
     */
    private Long printRecordId;
    /**
     * 商品ID
     */
    private String itemId;
    /**
     * 商品名称
     */
    private String itemName;
    /**
     * 商品商家编码
     */
    private String itemOuterId;
    /**
     * 商品简称
     */
    private String itemShortName;
    /**
     * 商品图片链接
     */
    private String itemPicUrl;
    /**
     * 规格ID
     */
    private String skuId;
    /**
     * 规格名称
     */
    private String skuName;
    /**
     * 规格商家编码
     */
    private String skuOuterId;
    /**
     * 规格别名
     */
    private String skuAlias;
    /**
     * 规格图片链接
     */
    private String skuPicUrl;
    /**
     * 商品数量
     */
    private Integer num;
    /**
     * 创建时间
     */
    private Date created;

    public void initFieldsBeforeInsert() {
        //一些不是很重要的字段，按数据库分配的长度截断一下
        this.itemName = StringUtil.frontSubstring(this.itemName, 256);
        this.itemShortName = StringUtil.frontSubstring(this.itemShortName, 128);
        this.skuName = StringUtil.frontSubstring(this.skuName, 256);
        this.skuAlias = StringUtil.frontSubstring(this.skuAlias, 128);
    }
}
