package com.kuaidizs.jxc.domain.cnprivacy;

import java.util.*;
import java.io.Serializable;

/**
 * 菜鸟隐私面单设置
 */
public class CnPrivacySet implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键自增id
     */
    private Long id;
    /**
     * 用户淘宝id
     */
    private Long taobaoId;
    /**
     * 是否开启菜鸟隐私面单：1:开启0:关闭 默认0
     */
    private Integer privacySwitch;

    /**
     * 是否允许其他店铺使用改帐号打印隐私面单，1:开启，0:关闭 ，默认0
     */
    private Integer otherSwitch;

    /**
     * 保护内容（ProtectContent 对象的json字符串）
     */
    private String protectContent;
    /**
     * 匹配规则 0:没选中任何一项 ，1:淘宝隐私加密，2:手工单隐私加密，默认 0
     */
    private Integer matchRule;
    /**
     * 规则内容（Rule 对象的json字符串）
     */
    private String ruleContent;
    /**
     * 状态：1:有效0:无效 默认1
     */
    private Integer status;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;


    /**
     * @return id 主键自增id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id 主键自增id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return taobaoId 用户淘宝id
     */
    public Long getTaobaoId() {
        return taobaoId;
    }

    /**
     * @param taobaoId 用户淘宝id
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return privacySwitch 是否开启菜鸟隐私面单：1:开启0:关闭 默认0
     */
    public Integer getPrivacySwitch() {
        return privacySwitch;
    }

    /**
     * @param privacySwitch 是否开启菜鸟隐私面单：1:开启0:关闭 默认0
     */
    public void setPrivacySwitch(Integer privacySwitch) {
        this.privacySwitch = privacySwitch;
    }

    public Integer getOtherSwitch() {
        return otherSwitch;
    }

    public void setOtherSwitch(Integer otherSwitch) {
        this.otherSwitch = otherSwitch;
    }

    /**
     * @return protectContent 保护内容（json字符串）
     */
    public String getProtectContent() {
        return protectContent;
    }

    /**
     * @param protectContent 保护内容（json字符串）
     */
    public void setProtectContent(String protectContent) {
        this.protectContent = protectContent;
    }

    /**
     * @return matchRule 匹配规则1:淘宝，2:手工单，默认0
     */
    public Integer getMatchRule() {
        return matchRule;
    }

    /**
     * @param matchRule 匹配规则1:全部，2:部分，默认1
     */
    public void setMatchRule(Integer matchRule) {
        this.matchRule = matchRule;
    }

    /**
     * @return ruleContent 规则内容（json字符串）
     */
    public String getRuleContent() {
        return ruleContent;
    }

    /**
     * @param ruleContent 规则内容（json字符串）
     */
    public void setRuleContent(String ruleContent) {
        this.ruleContent = ruleContent;
    }

    /**
     * @return status 状态：1:有效0:无效 默认1
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * @param status 状态：1:有效0:无效 默认1
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

}