package com.kuaidizs.jxc.domain.oaid;

import java.io.Serializable;
import java.util.Date;

/***
 * program: kdzs-jxc
 * description: 根据收件人信息查询交易订单号请求
 * 多个条件之间是OR关系。receiver_name、receiver_mobile、receiver_phone至少有一个值不为空。
 * author: <PERSON><PERSON><PERSON><PERSON>@raycloud.com
 * create: 2021-03-08 16:58
 **/
public class OrderQueryRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 查询三个月内交易创建时间开始。格式:yyyy-MM-dd HH:mm:ss
     */
    private Date startCreated;

    /**
     * 查询交易创建时间结束。格式:yyyy-MM-dd HH:mm:ss
     */
    private Date endCreated;

    /**
     * 收件人的姓名
     */
    private String receiverName;

    /**
     * 收件人的手机号
     */
    private String receiverMobile;


    /**
     * 收件人的电话号码
     */
    private String receiverPhone;


    public Date getStartCreated() {
        return startCreated;
    }

    public void setStartCreated(Date startCreated) {
        this.startCreated = startCreated;
    }

    public Date getEndCreated() {
        return endCreated;
    }

    public void setEndCreated(Date endCreated) {
        this.endCreated = endCreated;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getReceiverMobile() {
        return receiverMobile;
    }

    public void setReceiverMobile(String receiverMobile) {
        this.receiverMobile = receiverMobile;
    }

    public String getReceiverPhone() {
        return receiverPhone;
    }

    public void setReceiverPhone(String receiverPhone) {
        this.receiverPhone = receiverPhone;
    }
}
