package com.kuaidizs.jxc.domain.guoguo;

import java.io.Serializable;
import java.util.List;

/**
 * @auther zy
 * @since 2021-03-10
 */
public class GuoguoOrderRes implements Serializable {
    private static final long serialVersionUID = -3428577448466546626L;

    private Long totalCount;

    private Long totalPage;

    private Long pageSize;

    private Long pageNo;

    private List<GuoguoOrderData> guoguoOrderDataList;

    public Long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Long totalCount) {
        this.totalCount = totalCount;
    }

    public Long getTotalPage() {
        return totalPage;
    }

    public void setTotalPage(Long totalPage) {
        this.totalPage = totalPage;
    }

    public Long getPageSize() {
        return pageSize;
    }

    public void setPageSize(Long pageSize) {
        this.pageSize = pageSize;
    }

    public Long getPageNo() {
        return pageNo;
    }

    public void setPageNo(Long pageNo) {
        this.pageNo = pageNo;
    }

    public List<GuoguoOrderData> getGuoguoOrderDataList() {
        return guoguoOrderDataList;
    }

    public void setGuoguoOrderDataList(List<GuoguoOrderData> guoguoOrderDataList) {
        this.guoguoOrderDataList = guoguoOrderDataList;
    }

    public static class GuoguoOrderData implements Serializable {

        /**
         * 序列化ID
         */
        private static final long serialVersionUID = 1L;

        /**
         * 快递模版
         */
        private String exTemplate;

        /**
         * 下单时间
         */
        private String buyTime;

        /**
         * 揽收时间
         */
        private String actualGotTime;

        /**
         * 运单号
         */
        private String mailNo;

        /**
         * 寄件地址
         */
        private String sendAddress;

        /**
         * 收件地址
         */
        private String receiveAddress;

        /**
         * 重量
         */
        private String weight;

        /**
         * 运费/价格
         */
        private String orderTotalPrice;

        /**
         * 付款状态(待支付，已支付)
         */
        private String status;

        /**
         * 裹裹id
         */
        private String guoguoId;

        /**
         * 订单编号(我们系统中的订单编号，通过裹裹id去guoguoOrderLog表中查询出来)
         */
        private String tid;

        /**
         * 操作人
         */
        private String opName;


        public String getExTemplate() {
            return exTemplate;
        }

        public void setExTemplate(String exTemplate) {
            this.exTemplate = exTemplate;
        }

        public String getBuyTime() {
            return buyTime;
        }

        public void setBuyTime(String buyTime) {
            this.buyTime = buyTime;
        }

        public String getActualGotTime() {
            return actualGotTime;
        }

        public void setActualGotTime(String actualGotTime) {
            this.actualGotTime = actualGotTime;
        }

        public String getMailNo() {
            return mailNo;
        }

        public void setMailNo(String mailNo) {
            this.mailNo = mailNo;
        }

        public String getSendAddress() {
            return sendAddress;
        }

        public void setSendAddress(String sendAddress) {
            this.sendAddress = sendAddress;
        }

        public String getReceiveAddress() {
            return receiveAddress;
        }

        public void setReceiveAddress(String receiveAddress) {
            this.receiveAddress = receiveAddress;
        }

        public String getWeight() {
            return weight;
        }

        public void setWeight(String weight) {
            this.weight = weight;
        }

        public String getOrderTotalPrice() {
            return orderTotalPrice;
        }

        public void setOrderTotalPrice(String orderTotalPrice) {
            this.orderTotalPrice = orderTotalPrice;
        }

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getGuoguoId() {
            return guoguoId;
        }

        public void setGuoguoId(String guoguoId) {
            this.guoguoId = guoguoId;
        }

        public String getTid() {
            return tid;
        }

        public void setTid(String tid) {
            this.tid = tid;
        }

        public String getOpName() {
            return opName;
        }

        public void setOpName(String opName) {
            this.opName = opName;
        }
    }
}
