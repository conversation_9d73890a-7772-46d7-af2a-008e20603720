package com.kuaidizs.jxc.domain.download;

import org.apache.commons.lang3.StringUtils;

/**
 * 下载中心下载结果  0等待导出，1导出中，2导出成功，3导出失败
 */
public enum DownExportResult {

    WAIT_EXPORT(0, "等待导出"),
    EXPORTING(1, "导出中"),
    EXPORT_SUCESS(2, "导出成功"),
    EXPORT_FAIL(3, "导出失败");

    private Integer exportResult;

    private String ExportResultDes;

    DownExportResult(Integer exportResult, String exportResultDes) {
        this.exportResult = exportResult;
        ExportResultDes = exportResultDes;
    }

    public static String getDes(Integer exportResult) {
        if (exportResult == null) {
            return StringUtils.EMPTY;
        }
        DownExportResult[] values = DownExportResult.values();
        for (DownExportResult value : values) {
            if (value.getExportResult().equals(exportResult)) {
                return value.ExportResultDes;
            }
        }
        return null;
    }

    public Integer getExportResult() {
        return exportResult;
    }

    public void setExportResult(Integer exportResult) {
        this.exportResult = exportResult;
    }

    public String getExportResultDes() {
        return ExportResultDes;
    }

    public void setExportResultDes(String exportResultDes) {
        ExportResultDes = exportResultDes;
    }
}
