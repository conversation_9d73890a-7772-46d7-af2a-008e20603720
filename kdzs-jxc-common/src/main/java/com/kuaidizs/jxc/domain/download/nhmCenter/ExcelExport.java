package com.kuaidizs.jxc.domain.download.nhmCenter;

import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.util.IOUtils;

import java.io.ByteArrayOutputStream;
import java.net.URL;
import java.util.*;
import com.raycloud.bizlogger.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2023/4/11.
 * @time 13:43.
 */
public class ExcelExport {

    private static final Logger logger = Logger.getLogger(ExcelExport.class);
    // 图片链接解析
    private static final Pattern urlPattern = Pattern.compile("((https|http)?://\\S+\\.(?:png|gif|jpg))");



    private static HSSFWorkbook wb;

    private static CellStyle titleStyle; // 标题行样式
    private static Font titleFont; // 标题行字体
    private static CellStyle dateStyle; // 日期行样式
    private static Font dateFont; // 日期行字体
    private static CellStyle headStyle; // 表头行样式
    private static Font headFont; // 表头行字体
    private static CellStyle contentStyle; // 内容行样式
    private static Font contentFont; // 内容行字体

    /**
     * 导出文件
     */
    public static boolean export2File(ExcelExportData setInfo,
                                      String outputExcelFileName) throws Exception {
        return ExcelFileUtil.write(outputExcelFileName, export2ByteArray(setInfo),
                true, true);
    }

    public static byte[] export2FileBytes(ExcelExportData setInfo) throws Exception {
        return export2ByteArray(setInfo);
    }
    public static byte[] export2FileBytesByMap(ExcelExportData setInfo) throws Exception {
        return export2ByteArrayByMap(setInfo);
    }

    /**
     * 导出到byte数组
     */
    public static byte[] export2ByteArray(ExcelExportData setInfo)
            throws Exception {
        return export2Stream(setInfo).toByteArray();
    }

    public static byte[] export2ByteArrayByMap(ExcelExportData setInfo)
            throws Exception {
        return export2StreamByMap(setInfo).toByteArray();
    }

    /**
     * 导出到流，通过对象传入，使用映射来对应数据，能够合并单元格
     */
    public static ByteArrayOutputStream export2Stream(ExcelExportData setInfo)
            throws Exception {
        init();
        //logger.error("export2Stream ---> " + JSONObject.toJSONString(setInfo));

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        Set<Map.Entry<String, List<?>>> set = setInfo.getDataMap().entrySet();
        String[] sheetNames = new String[setInfo.getDataMap().size()];
        int sheetNameNum = 0;
        for (Map.Entry<String, List<?>> entry : set) {
            sheetNames[sheetNameNum] = entry.getKey();
            sheetNameNum++;
        }
        HSSFSheet[] sheets = getSheets(setInfo.getDataMap().size(), sheetNames);
        int sheetNum = 0;
        int k = 0;

        for (Map.Entry<String, List<?>> entry : set) {
            // Sheet
            List<?> objs = entry.getValue();

            // 标题行
            //createTableTitleRow(setInfo, sheets, sheetNum);

            // 商品明细和订单明细标题栏
            //createTableDateRow(setInfo, sheets, sheetNum);

            // 表头
            creatTableHeadRow(setInfo, sheets, sheetNum, setInfo.getHasXuhao());

            // 表体
            String[] fieldNames = setInfo.getFieldNames().get(sheetNum);

            int rowNum = 1;
            for (Object obj : objs) {
                HSSFRow contentRow = sheets[sheetNum].createRow(rowNum);
                contentRow.setHeight((short) 300);
                HSSFCell[] cells = getCells(contentRow, setInfo.getFieldNames().get(sheetNum).length, setInfo.getHasXuhao());
                int cellNum = 0; // 去掉一列序号，因此从1开始
                if (fieldNames != null) {
                    for (int num = 0; num < fieldNames.length; num++) {
                        Object value = ExcelReflection.invokeGetterMethod(obj,fieldNames[num]);
                        cells[cellNum].setCellValue(value == null ? "" : value.toString());
                        cellNum++;
                    }
                }
                rowNum++;
            }

            k++;
            String[] groupColumns = null;
            if(setInfo.getGroupColumn().size()!=0){
                if(setInfo.getGroupColumn().size() >= k){
                    groupColumns = setInfo.getGroupColumn().get(sheetNum);
                }
            }

            if(groupColumns!=null){
                // 这里的n代表第几列合并，从0开始是第一列，和j的数值一样
                int n=0;
                for (int i = 0; i < groupColumns.length; i++) {

                    String[] fieldName = setInfo.getFieldNames().get(sheetNum);
                    for (int j = 0; j < fieldName.length; j++) {
                        if(groupColumns[i].equals(fieldName[j])){
                            // j++是因为一开始第一列是添加的序号列，不算在内所以后面都加1
                            // 后面去除序号列之后就需要注释
                            // j++;
                            n=j;
                            break;
                        }
                    }
                    int x = 0;
                    int y = 0;
                    int z = 3;
                    int m = objs.size();
                    boolean flag = false;
                    Object val = null;
                    CellRangeAddress dateRange = null;
//                    boolean firstHasOne = true;
                    // false：中间没有不能合并的   true：中间有不能合并的单行
                    boolean middleHasOne = false;
                    for (Object obj : objs) {
                        y++;
                        Object value = ExcelReflection.invokeGetterMethod(obj,groupColumns[i]);
                        if(x==0){
                            x++;
                            val=value;
                        }else if(val.toString().equals(value.toString())){
//                            if (x == 1) {
//                                firstHasOne = false;
//                                System.out.println("第一行能合并");
//                            }
                            x++;
                            if(m==y){
                                try {
                                    dateRange = new CellRangeAddress(z, x+3, n, n);
                                } catch (Exception ex) {
                                    //logger.error("01-----> z:" + z + ",x:" + x);
                                }
                                sheets[sheetNum].addMergedRegion(dateRange);
                            }
                        }else{
                            val=value;
                            if(flag){
                                try {
                                    dateRange = new CellRangeAddress(z, x+3, n, n);
                                } catch (Exception ex) {
                                    //logger.error("02-----> z:" + z + ",x:" + x);
                                }

                                z=x+4;
                                x=x+1;
                            }else{
                                try {
                                    dateRange = new CellRangeAddress(z, x+2, n, n);
                                } catch (Exception ex) {
                                    //logger.error("03-----> z:" + z + ",x:" + x);
                                }
                                // startRowIndex, endRowIndex,startColumnIndex,endColumnIndex

                                z=x+3;
                            }

                            // 只有一个单元格的时候不合并
                            if (dateRange.getFirstRow() == dateRange.getLastRow() && dateRange.getFirstColumn() == dateRange.getLastColumn()) {
                                if (x > 1 && !middleHasOne) {
                                    middleHasOne = true;
                                }
                                // 第二行如果不能合并的话，row需要+1
                                if (x == 2) {
                                    x++;
                                }
                                // 除了第二行，中间有一个不能合并的话需要+1
                                if (!middleHasOne) {
                                    x++;
                                }

//                                if (middleHasOne && firstHasOne) {
//                                    x++;
//                                }
                            } else {
                                sheets[sheetNum].addMergedRegion(dateRange);
                                flag=true;
                            }

                        }
                    }
                }
            }

//            CellRangeAddress dateRange = new CellRangeAddress(3, 10, 1, 1);
//            sheets[sheetNum].addMergedRegion(dateRange);
//
//            CellRangeAddress aa = new CellRangeAddress(11, 15, 1, 1);
//            sheets[sheetNum].addMergedRegion(aa);
//
//            CellRangeAddress bb = new CellRangeAddress(3, 5, 2, 2);
//            sheets[sheetNum].addMergedRegion(bb);

//
//            CellRangeAddress aaa = new CellRangeAddress(16, 18, 1, 1);
//            sheets[sheetNum].addMergedRegion(aaa);

            adjustColumnSize(sheets, sheetNum, fieldNames); // 自动调整列宽
            sheetNum++;
        }
        wb.write(outputStream);
        return outputStream;
    }

    /**
     * 通过map来实现动态列名，不需要通过对象来传入
     * @param setInfo
     * @return
     * @throws Exception
     */
    public static ByteArrayOutputStream export2StreamByMap(ExcelExportData setInfo)
            throws Exception {
        init();
        //logger.error("export2Stream ---> " + JSONObject.toJSONString(setInfo));

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        Set<Map.Entry<String, LinkedList<LinkedHashMap<String, String>>>> set = setInfo.getDataMapByMap().entrySet();
        String[] sheetNames = new String[setInfo.getDataMapByMap().size()];
        int sheetNameNum = 0;
        for (Map.Entry<String, LinkedList<LinkedHashMap<String, String>>> entry : set) {
            sheetNames[sheetNameNum] = entry.getKey();
            sheetNameNum++;
        }
        HSSFSheet[] sheets = getSheets(setInfo.getDataMapByMap().size(), sheetNames);
        int sheetNum = 0;
        int k = 0;

        for (Map.Entry<String, LinkedList<LinkedHashMap<String, String>>> entry : set) {
            // Sheet
            LinkedList<LinkedHashMap<String, String>> objs = entry.getValue();

            // 标题行
            //createTableTitleRow(setInfo, sheets, sheetNum);

            // 商品明细和订单明细标题栏
            //createTableDateRow(setInfo, sheets, sheetNum);

            // 表头
            creatTableHeadRow(setInfo, sheets, sheetNum, setInfo.getHasXuhao());

            // 个数
            String[] columns = setInfo.getColumnNames().get(sheetNum);

            int rowNum = 1;
            for (LinkedHashMap<String, String> obj : objs) {
                HSSFRow contentRow = sheets[sheetNum].createRow(rowNum);
                contentRow.setHeight((short) 300);
                HSSFCell[] cells = null;
                // 不需要序号，因此从0开始
                int cellNum = 0;
                if (setInfo.getHasXuhao()) {
                    cells = getCells(contentRow, setInfo.getColumnNames().get(sheetNum).length + 1, setInfo.getHasXuhao());
                    // 去掉一列序号，因此从1开始
                    cellNum = 1;
                } else {
                    cells = getCells(contentRow, setInfo.getColumnNames().get(sheetNum).length, setInfo.getHasXuhao());
                }
                if (columns != null) {
                    // 最大行高，每一行的处理以最后一次的设置高度为准，求出最大的高度再设置
                    int maxRowHeigh = 300;
                    for (int num = 0; num < columns.length; num++) {
                        // 获取值
                        String value = obj.get(columns[num]);
                        if (value == null) value = "";
                        // \n换行的个数为count - 1，一个换行需要加300高度
                        int count = value.split("\n").length;
                        if (value.contains("http") && (value.contains("png") || value.contains("jpg"))) {
                            // 处理图片
                            Matcher matcher = urlPattern.matcher(value);
                            // 图片地址
                            String picUrl = "";
                            // 文字
                            String textValue = "";
                            if (matcher.find()) {
                                picUrl = matcher.group(1);
                            }
                            if (StringUtils.isNotBlank(picUrl)) {
                                textValue = value.replace(picUrl, " ");
                            }
                            delPic(sheets[sheetNum], picUrl, cellNum, rowNum, cells, textValue);
                            maxRowHeigh = 900 + 300 * (count - 1);
                            contentRow.setHeight((short) maxRowHeigh);
                        } else {
                            cells[cellNum].setCellValue(value);
                            if (count > 1 && maxRowHeigh < 300 * count) {
                                maxRowHeigh = 300 * count;
                                contentRow.setHeight((short) (300 * count));
                            } else {
                                contentRow.setHeight((short) maxRowHeigh);
                            }
                        }
                        cellNum++;
                    }
                }
                rowNum++;
            }
            k++;
            // 自动调整列宽
            adjustColumnSize(sheets, sheetNum, columns);

            sheetNum++;
        }

        wb.write(outputStream);
        return outputStream;
    }


    /**
     * 将图片和文字插入excel中
     * 文字要置于底部，不然会被图片所覆盖
     * @param sheet
     * @param imageUrl 图片链接
     * @param col 第几列
     * @param row 第几行
     * @param cells
     * @param value 值
     */
    public static void delPic(HSSFSheet sheet, String imageUrl, int col, int row, HSSFCell[] cells, String value) {
        try {
            HSSFPatriarch patriarch = sheet.createDrawingPatriarch();
            HSSFRichTextString richString = new HSSFRichTextString(value);


            if (StringUtils.isNotBlank(imageUrl)) {
                try {
                    URL url = new URL(imageUrl);
                    byte[] bytes = IOUtils.toByteArray(url.openStream());
                    int pictureIdx = 0;
                    if (imageUrl.endsWith("png")) {
                        pictureIdx = wb.addPicture(bytes, Workbook.PICTURE_TYPE_PNG);
                    } else if (imageUrl.endsWith("jpg")) {
                        pictureIdx = wb.addPicture(bytes, Workbook.PICTURE_TYPE_JPEG);
                    }
                    // 创建一个Anchor对象，用于指定图片的位置和大小
                    // col 起始列 ，结束列 = col
                    // row 起始行   结束行 = row
                    // dx1 -> dx2  0 - 1023 代表最左侧到最右侧
                    // dy1 -> dy2  0 - 120  代表高度一半
                    // 此处代表将图片置于左上角
                    HSSFClientAnchor anchor = new HSSFClientAnchor(0, 0, 500, 120,(short) col, row, (short)col, row);
                    //anchor.setAnchorType(2); // 设置Anchor的类型为2（相对于单元格的位置）
                    anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_DONT_RESIZE); // 设置Anchor的类型为2（相对于单元格的位置）


                    // 创建一个Picture对象，并将其插入到Patriarch中
                    HSSFPicture pict = patriarch.createPicture(anchor, pictureIdx);
                    sheet.autoSizeColumn(col);
                } catch (Exception ex) {
                    logger.error("图片解析错误，地址：" + imageUrl);
                }
            }
            HSSFCellStyle style = wb.createCellStyle();
            style.setVerticalAlignment(VerticalAlignment.BOTTOM);
            cells[col].setCellStyle(style);
            cells[col].setCellValue(richString);
//            HSSFCellStyle style = wb.createCellStyle();
//            style.setVerticalAlignment(HSSFCellStyle.VERTICAL_BOTTOM);
//            cells[col].setCellStyle(style);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * @Description: 初始化
     */
    private static void init() {
        wb = new HSSFWorkbook();

        titleFont = wb.createFont();
        titleStyle = wb.createCellStyle();
        dateStyle = wb.createCellStyle();
        dateFont = wb.createFont();
        headStyle = wb.createCellStyle();
        headFont = wb.createFont();
        contentStyle = wb.createCellStyle();
        contentFont = wb.createFont();

        initTitleCellStyle();
        initTitleFont();
        initDateCellStyle();
        initDateFont();
        initHeadCellStyle();
        initHeadFont();
        initContentCellStyle();
        initContentFont();
    }

    /**
     * @Description: 自动调整列宽
     */
    private static void adjustColumnSize(HSSFSheet[] sheets, int sheetNum,
                                         String[] fieldNames) {
        for (int i = 0; i < fieldNames.length + 1; i++) {
            // 只能解决英文，数字列宽自适应, 中文出现列宽不足
            sheets[sheetNum].autoSizeColumn(i, true);
            // 解决自动设置列宽中文失效的问题
            sheets[sheetNum].setColumnWidth(i, sheets[sheetNum].getColumnWidth(i) * 17 / 10);

        }
    }


    /**
     * @Description: 创建标题行(需合并单元格)
     */
    private static void createTableTitleRow(ExcelExportData setInfo, HSSFSheet[] sheets, int sheetNum) {
        CellRangeAddress titleRange = new CellRangeAddress(0, 0, 0, setInfo.getFieldNames().get(sheetNum).length - 1);
        sheets[sheetNum].addMergedRegion(titleRange);
        HSSFRow titleRow = sheets[sheetNum].createRow(0);
        titleRow.setHeight((short) 800);
        HSSFCell titleCell = titleRow.createCell(0);
        titleCell.setCellStyle(titleStyle);
        titleCell.setCellValue(setInfo.getTitles()[sheetNum]);
    }

    /**
     * @Description: 创建日期行(需合并单元格)
     */
    private static void createTableDateRow(ExcelExportData setInfo,HSSFSheet[] sheets, int sheetNum) {
        CellRangeAddress dateRange = new CellRangeAddress(1, 1, 0, setInfo.getFieldNames().get(sheetNum).length - 1);
        sheets[sheetNum].addMergedRegion(dateRange);
        HSSFRow dateRow = sheets[sheetNum].createRow(1);
        dateRow.setHeight((short) 350);
        HSSFCell dateCell = dateRow.createCell(0);
        dateCell.setCellStyle(dateStyle);
        //dateCell.setCellValue(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        if (sheetNum == 0) {
            // 商品维度
            dateCell.setCellValue(setInfo.getItemRemark());
        } else if (sheetNum == 1) {
            // 订单维度
            dateCell.setCellValue(setInfo.getOrderRemark());
        }
    }

    /**
     * @Description: 创建表头行(需合并单元格)
     */
    private static void creatTableHeadRow(ExcelExportData setInfo,
                                          HSSFSheet[] sheets, int sheetNum, Boolean hasXuhao) {
        // 表头
        HSSFRow headRow = sheets[sheetNum].createRow(0);
        headRow.setHeight((short) 350);
        if (hasXuhao) {
            // 序号列
            HSSFCell snCell = headRow.createCell(0);
            snCell.setCellStyle(headStyle);
            snCell.setCellValue("序号");
            // 列头名称
            for (int num = 1, len = setInfo.getColumnNames().get(sheetNum).length; num <= len; num++) {
                HSSFCell headCell = headRow.createCell(num);
                headCell.setCellStyle(headStyle);
                headCell.setCellValue(setInfo.getColumnNames().get(sheetNum)[num - 1]);
            }
        } else {
            // 列头名称
            for (int num = 0, len = setInfo.getColumnNames().get(sheetNum).length; num <= len -1; num++) {
                HSSFCell headCell = headRow.createCell(num);
                headCell.setCellStyle(headStyle);
                headCell.setCellValue(setInfo.getColumnNames().get(sheetNum)[num]);
            }
        }
    }

    /**
     * @Description: 创建所有的Sheet
     */
    private static HSSFSheet[] getSheets(int num, String[] names) {
        HSSFSheet[] sheets = new HSSFSheet[num];
        for (int i = 0; i < num; i++) {
            sheets[i] = wb.createSheet(names[i]);
        }
        return sheets;
    }

    /**
     * @Description: 创建内容行的每一列(附加一列序号)
     */
    private static HSSFCell[] getCells(HSSFRow contentRow, int num, Boolean hasXuhao) {
        HSSFCell[] cells = new HSSFCell[num];

        for (int i = 0, len = cells.length; i < len; i++) {
            cells[i] = contentRow.createCell(i);
            cells[i].setCellStyle(contentStyle);
        }

        // 设置序号列值，如果之前还有几行，就减去几行
        if (hasXuhao) {
            cells[0].setCellValue(contentRow.getRowNum());
        }
        return cells;
    }

    /**
     * @Description: 初始化标题行样式
     */
    private static void initTitleCellStyle() {
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        titleStyle.setFont(titleFont);
        titleStyle.setFillBackgroundColor(IndexedColors.BLACK.getIndex());
    }

    /**
     * @Description: 初始化日期行样式
     */
    private static void initDateCellStyle() {
        //dateStyle.setAlignment(CellStyle.ALIGN_CENTER_SELECTION);
        dateStyle.setAlignment(HorizontalAlignment.CENTER_SELECTION);
        dateStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dateStyle.setFont(dateFont);
        dateStyle.setFillBackgroundColor(IndexedColors.RED.getIndex());
    }

    /**
     * @Description: 初始化表头行样式
     */
    private static void initHeadCellStyle() {
        headStyle.setAlignment(HorizontalAlignment.CENTER);
        headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headStyle.setFont(headFont);
        headStyle.setFillBackgroundColor(IndexedColors.YELLOW.getIndex());
        //headStyle.setBorderTop(CellStyle.BORDER_MEDIUM);
        headStyle.setBorderTop(BorderStyle.MEDIUM);
        headStyle.setBorderBottom(BorderStyle.THIN);
        headStyle.setBorderLeft(BorderStyle.THIN);
        headStyle.setBorderRight(BorderStyle.THIN);
        headStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        headStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        headStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        headStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
    }

    /**
     * @Description: 初始化内容行样式
     */
    private static void initContentCellStyle() {
        contentStyle.setAlignment(HorizontalAlignment.CENTER);
        contentStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        contentStyle.setFont(contentFont);
        contentStyle.setBorderTop(BorderStyle.THIN);
        contentStyle.setBorderBottom(BorderStyle.THIN);
        contentStyle.setBorderLeft(BorderStyle.THIN);
        contentStyle.setBorderRight(BorderStyle.THIN);
        contentStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        contentStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        contentStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        contentStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        contentStyle.setWrapText(true); // 字段换行
    }

    /**
     * @Description: 初始化标题行字体
     */
    private static void initTitleFont() {
        titleFont.setFontName("宋体");
        titleFont.setFontHeightInPoints((short) 20);
        //titleFont.setBoldweight(Font.BOLDWEIGHT_BOLD);
        titleFont.setBold(true);
        titleFont.setCharSet(Font.DEFAULT_CHARSET);
        titleFont.setColor(IndexedColors.BLACK.getIndex());
    }

    /**
     * @Description: 初始化日期行字体
     */
    private static void initDateFont() {
        dateFont.setFontName("宋体");
        dateFont.setFontHeightInPoints((short) 10);
        //dateFont.setBoldweight(Font.BOLDWEIGHT_BOLD);
        dateFont.setBold(true);
        dateFont.setCharSet(Font.DEFAULT_CHARSET);
        dateFont.setColor(IndexedColors.BLACK.getIndex());
    }

    /**
     * @Description: 初始化表头行字体
     */
    private static void initHeadFont() {
        headFont.setFontName("宋体");
        headFont.setFontHeightInPoints((short) 10);
        //headFont.setBoldweight(Font.BOLDWEIGHT_BOLD);
        headFont.setBold(true);
        headFont.setCharSet(Font.DEFAULT_CHARSET);
        headFont.setColor(IndexedColors.BLACK.getIndex());
    }

    /**
     * @Description: 初始化内容行字体
     */
    private static void initContentFont() {
        contentFont.setFontName("宋体");
        contentFont.setFontHeightInPoints((short) 10);
        //contentFont.setBoldweight(Font.BOLDWEIGHT_NORMAL);
        contentFont.setBold(true);
        contentFont.setCharSet(Font.DEFAULT_CHARSET);
        contentFont.setColor(IndexedColors.BLACK.getIndex());
    }

    /**
     * Excel导出数据类
     * <AUTHOR>
     */
    public static class ExcelExportData {

        /**
         * 导出数据 key:String 表示每个Sheet的名称 value:List<?> 表示每个Sheet里的所有数据行
         */
        private LinkedHashMap<String, List<?>> dataMap;

        /**
         * 导出数据 key:String 表示每个Sheet的名称，value： List<Map<String, String>> 每一行数据，map中代表属性和值的对应
         * 不使用实体类而用map是因为列名是动态的
         */
        private LinkedHashMap<String, LinkedList<LinkedHashMap<String, String>>> dataMapByMap;

        /**
         * 每个Sheet里的顶部大标题
         */
        private String[] titles;

        /**
         * 单个sheet里的数据列标题
         */
        private List<String[]> columnNames;

        /**
         * 单个sheet里每行数据的列对应的对象属性名称
         */
        private List<String[]> fieldNames;

        private List<String[]> groupColumn;

        /**
         * 商品明细备注
         */
        private String itemRemark;

        /**
         * 订单明细备注
         */
        private String orderRemark;


        /**
         * 是否包含序号  默认false，true：有序号
         */
        private Boolean hasXuhao = false;

        public Boolean getHasXuhao() {
            return hasXuhao;
        }

        public void setHasXuhao(Boolean hasXuhao) {
            this.hasXuhao = hasXuhao;
        }

        public LinkedHashMap<String, LinkedList<LinkedHashMap<String, String>>> getDataMapByMap() {
            return dataMapByMap;
        }

        public void setDataMapByMap(LinkedHashMap<String, LinkedList<LinkedHashMap<String, String>>> dataMapByMap) {
            this.dataMapByMap = dataMapByMap;
        }

        public String getItemRemark() {
            return itemRemark;
        }

        public void setItemRemark(String itemRemark) {
            this.itemRemark = itemRemark;
        }

        public String getOrderRemark() {
            return orderRemark;
        }

        public void setOrderRemark(String orderRemark) {
            this.orderRemark = orderRemark;
        }

        public List<String[]> getFieldNames() {
            return fieldNames;
        }

        public void setFieldNames(List<String[]> fieldNames) {
            this.fieldNames = fieldNames;
        }

        public String[] getTitles() {
            return titles;
        }

        public void setTitles(String[] titles) {
            this.titles = titles;
        }

        public List<String[]> getColumnNames() {
            return columnNames;
        }

        public void setColumnNames(List<String[]> columnNames) {
            this.columnNames = columnNames;
        }

        public LinkedHashMap<String, List<?>> getDataMap() {
            return dataMap;
        }

        public void setDataMap(LinkedHashMap<String, List<?>> dataMap) {
            this.dataMap = dataMap;
        }

        public List<String[]> getGroupColumn() {
            return groupColumn;
        }

        public void setGroupColumn(List<String[]> groupColumn) {
            this.groupColumn = groupColumn;
        }
    }
}

