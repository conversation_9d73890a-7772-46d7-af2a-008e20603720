package com.kuaidizs.jxc.domain.log;

import java.io.Serializable;
import java.util.Date;

public class SendMsgLog implements Serializable {
    private static final long serialVersionUID = -2563475495793457680L;

    private Long id;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * 逻辑删除状态0表示删除 1表示未删除
     */
    private Boolean enableStatus;
    /**
     * 淘宝id
     */
    private Long taobaoId;
    /**
     * version 可以是发送的时间或者版本
     */
    private String version;

    /**
     * userType = 1; //5天后即将过期的非专业版用户
     * userType = 2; //1天后即将过期的非专业版用户
     * userType = 3; //过期3天后即将过期的非专业版用户
     * userType = 4; //过期7天后即将过期的非专业版用户
     * userType = 5; //过期15天后即将过期的非专业版用户
     * userType = 6; //过期30天后即将过期的非专业版用户
     * userType = 7; //5天后即将过期的专业版用户
     * userType = 8; //1天后即将过期的专业版用户
     * userType = 9; //过期3天后即将过期的专业版用户
     * userType = 10; //过期7天后即将过期的专业版用户
     * userType = 11; //过期15天后即将过期的专业版用户
     * userType = 12; //过期30天后即将过期的专业版用户
     */
    private Integer type;

    private String mobile;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public Boolean getEnableStatus() {
        return enableStatus;
    }

    public void setEnableStatus(Boolean enableStatus) {
        this.enableStatus = enableStatus;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
}
