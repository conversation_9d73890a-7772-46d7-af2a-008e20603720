package com.kuaidizs.jxc.domain.ai;

import com.kuaidizs.jxc.common.util.StringUtil;
import lombok.Data;

@Data
public class AiSortTradeStatisticsOfSku {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 订单同步的唯一标识
     */
    private String syncKey;
    /**
     * 规格所属用户ID
     */
    private Long userId;
    /**
     * 商品ID
     */
    private String itemId;
    /**
     * 规格ID
     */
    private String skuId;
    /**
     * 规格名称
     */
    private String skuName;
    /**
     * 规格别名
     */
    private String skuAlias;
    /**
     * 规格图片
     */
    private String skuPicUrl;
    /**
     * 订单数量
     */
    private Integer tradeNum;
    /**
     * 规格数量
     */
    private Integer skuNum;

    public void initFieldsBeforeInsert() {
        //一些不是很重要的字段，按数据库分配的长度截断一下
        this.skuName = StringUtil.frontSubstring(this.skuName, 256);
        this.skuAlias = StringUtil.frontSubstring(this.skuAlias, 256);
    }
}
