package com.kuaidizs.jxc.domain.permission;

import java.util.*;
import java.io.Serializable;

/**
 *
 * <AUTHOR> @date    2016-08-26
 */
public class UserSubAccount implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 自增列ID
     */
    private Long id;
	/**
     * 子账号所属的主账号的唯一标识
     */
    private Long taobaoId;
	/**
     * 主账号昵称
     */
    private String taobaoNick;
	/**
     * 子账号用户名
     */
    private String subTaobaoNick;
	/**
     * 子账号姓名
     */
    private String subTaobaoName;
	/**
     * 子账号Id
     */
    private Long subTaobaoId;
	/**
     * 是否参与分流：1不参与、2参与
     */
    private Long isOnline;
	/**
     * 子账号当前状态：1正常、-1删除、2冻结
     */
    private Long status;
	/**
     * 添加时间
     */
    private Date created;
	/**
     * 更新时间
     */
    private Date modified;
	/**
     * 逻辑删除
     */
    private Boolean enableStatus;

	
   /**
    * @return id 自增列ID
    */
    public Long getId() {
       return id;
    }
   /**
    * @param id 自增列ID
    */
    public void setId(Long id) {
       this.id = id;
    }
	
   /**
    * @return taobaoId 子账号所属的主账号的唯一标识
    */
    public Long getTaobaoId() {
       return taobaoId;
    }
   /**
    * @param taobaoId 子账号所属的主账号的唯一标识
    */
    public void setTaobaoId(Long taobaoId) {
       this.taobaoId = taobaoId;
    }
	
   /**
    * @return taobaoNick 主账号昵称
    */
    public String getTaobaoNick() {
       return taobaoNick;
    }
   /**
    * @param taobaoNick 主账号昵称
    */
    public void setTaobaoNick(String taobaoNick) {
       this.taobaoNick = taobaoNick;
    }
	
   /**
    * @return subTaobaoNick 子账号用户名
    */
    public String getSubTaobaoNick() {
       return subTaobaoNick;
    }
   /**
    * @param subTaobaoNick 子账号用户名
    */
    public void setSubTaobaoNick(String subTaobaoNick) {
       this.subTaobaoNick = subTaobaoNick;
    }
	
   /**
    * @return subTaobaoName 子账号姓名
    */
    public String getSubTaobaoName() {
       return subTaobaoName;
    }
   /**
    * @param subTaobaoName 子账号姓名
    */
    public void setSubTaobaoName(String subTaobaoName) {
       this.subTaobaoName = subTaobaoName;
    }
	
   /**
    * @return subTaobaoId 子账号Id
    */
    public Long getSubTaobaoId() {
       return subTaobaoId;
    }
   /**
    * @param subTaobaoId 子账号Id
    */
    public void setSubTaobaoId(Long subTaobaoId) {
       this.subTaobaoId = subTaobaoId;
    }
	
   /**
    * @return isOnline 是否参与分流：1不参与、2参与
    */
    public Long getIsOnline() {
       return isOnline;
    }
   /**
    * @param isOnline 是否参与分流：1不参与、2参与
    */
    public void setIsOnline(Long isOnline) {
       this.isOnline = isOnline;
    }
	
   /**
    * @return status 子账号当前状态：1正常、-1删除、2冻结
    */
    public Long getStatus() {
       return status;
    }
   /**
    * @param status 子账号当前状态：1正常、-1删除、2冻结
    */
    public void setStatus(Long status) {
       this.status = status;
    }
	
   /**
    * @return created 添加时间
    */
    public Date getCreated() {
       return created;
    }
   /**
    * @param created 添加时间
    */
    public void setCreated(Date created) {
       this.created = created;
    }
	
   /**
    * @return modified 更新时间
    */
    public Date getModified() {
       return modified;
    }
   /**
    * @param modified 更新时间
    */
    public void setModified(Date modified) {
       this.modified = modified;
    }
	
   /**
    * @return enableStatus 逻辑删除
    */
    public Boolean getEnableStatus() {
       return enableStatus;
    }
   /**
    * @param enableStatus 逻辑删除
    */
    public void setEnableStatus(Boolean enableStatus) {
       this.enableStatus = enableStatus;
    }

}