package com.kuaidizs.jxc.domain.data;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

@Setter
@Getter
public class UserTradeItemLatitude {

    private Long id; // 主键id
    private Long userId; // 淘宝id
    private String userName; // 用户名称
    private String itemId; // 商品id
    private String skuId; // sku_id
    private String tid; // 订单号
    private String oid;//子订单号
    private Date tradeCreated; // 下单时间
    private Date payTime; // 付款时间
    private Date sendTime; // 发货时间
    private Date printTime; // 打印时间
    private String batch; // 打印批次
    private String buyerNick; // 买家nick
    private String buyerOpenUid; // 买家openUid
    private String receiverName; // 收件人姓名
    private String receiverProvince; // 收件省
    private String receiverCity; // 收件市
    private String receiverDistrict; // 收件区
    private String receiverTown; // 收件街道
    private String receiverAddress; // 收件详细地址
    private String title; // 商品标题
    private String skuName; // 规格名称
    private Integer skuNum; // 规格数量
    private Integer itemNum; // 商品数量
    private String payment; // 实付金额
    private String outerId; // 商家编码
    private String skuOuterId; // 规格商家编码
    private String shortTitle; // 商品简称
    private String skuAlias; // 规格别名
    private String sellerMemo; // 卖家备注
    private String buyerMessage; // 买家留言
    private String flag; // 旗帜
    private String market; // 市场
    private String stall; // 档口
    private String kdzsMemo; // 助手备注
    private String operatorNick; // 操作员nick
    private Integer splitFlag;//0：未拆单，1：拆单
    private Date created; // 记录添加时间
    private Date modified; // 记录修改时间
    private String kdNo;//快递单号
    private String kdName;//快递名称
    private Integer mergeFlag;//0:非合单，1：合单
    private Integer obTag; //1:催发货,0:不是催发货
    private Long groupUserId;//打单的主店铺id


}
