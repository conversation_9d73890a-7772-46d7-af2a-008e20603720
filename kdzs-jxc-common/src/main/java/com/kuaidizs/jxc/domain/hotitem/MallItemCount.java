package com.kuaidizs.jxc.domain.hotitem;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections.CollectionUtils;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */

@Data
@NoArgsConstructor
public class MallItemCount implements Serializable {
    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 商品ID
     */
    private String numId;
    /**
     * 商品标题
     */
    private String title;
    /**
     * 商品数量
     */
    private Long itemCount = 0L;
    /**
     * 规格ID
     */
    private String skuId;
    /**
     * 规格属性
     */
    private String skuProperties;
    /**
     * 规格数量
     */
    private Long skuCount = 0L;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 开始时间
     */

    private String countDate;

    private String fkId;

    /**
     * 图片地址
     */
    private String picPath;

    /**
     * 商品简称
     */
    private String goodShort;

    /**
     * 商家编码
     */
    private String outerId;
    /**
     * 以","作为分隔符
     **/
    private String mergeGoodsId;

    private List<MallSku> mallSkuList;

    public static void mergeHandle(MallItemCount main, MallItemCount other) {
        main.setItemCount(main.getItemCount() + other.getItemCount());
        main.setSkuCount(main.getSkuCount() + other.getSkuCount());
        main.setMergeGoodsId(main.getMergeGoodsId() != null
                ? main.getMergeGoodsId() + "," + other.getNumId()
                : main.getNumId() + "," + other.getNumId());
        //商品合并 sku列表也要合并
        List<MallSku> otherSkuList = other.getMallSkuList();
        if (CollectionUtils.isNotEmpty(otherSkuList)){
            main.getMallSkuList().addAll(otherSkuList);
        }
    }

    public String defKey() {
        return "Null_" + this.getNumId();
    }
}
