package com.kuaidizs.jxc.domain.multiShop;

import java.io.Serializable;

import static com.kuaidizs.jxc.domain.trade.PrintVersionRecord.TOTAL_PRINT_NUM;

/**
 * RelaNick
 * Created by meiweifeng on 16/7/22.
 */
public class RelaNick implements Serializable {

    private static final long serialVersionUID = 1363635254862013058L;

    private Long taobaoId;
    private String taobaoNick;
    private int isAdmin; //是否管理员 0否
    private int isSee; //是否可见 1可见 0 不可见
    private Long groupId; //组id
    private int is3C;  //1 是 0 不是
    private Integer isFenxiao;//是否为分销，不做存储
    private String remark; // 备注
    private String groupName;//分组名称
    // 是不是新的免费版
    private Integer isNewFreeUser;
    /**
     * 排序
     */
    private Integer innerSort;

    public Integer getInnerSort() {
        return innerSort;
    }

    public void setInnerSort(Integer innerSort) {
        this.innerSort = innerSort;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    /**
     * 打印单数
     */
    private Long printNum;
    /**
     * 到期时间
     */
    private String expireDate;
    /**
     * 版本信息
     */
    private String version;

    /**
     * 版本信息整型值
     */
    private int vip;

    /**
     * 总打印数量
     */
    private Long totalPrintNum = TOTAL_PRINT_NUM;
    /**
     * 是否允许订购库存服务
     */
    private Boolean allowSubscribe;
    /**
     * 库存服务到期时间
     */
    private String stockExpireDate;
    /**
     * 库存服务状态，true标识已订购未到期，false标识未订购或已经到期
     */
    private Boolean stockExpireStatus;

    /**
     * 库存初始化状态：true 已经初始化 ， false 为初始化
     */
    private Boolean initStockStatus;
    /**
     * 初始化商品的状态：true 已经初始化 ， false 为初始化
     */
    private Boolean initProductStatus;
    /**
     * 是否可设置运单号数
     */
    private Boolean settingYdNum;
    /**
     * 运单号
     */
    private Integer ydNum;
    /**
     * 网点用户限制的相关三级关联组
     */
    private Long subGroupId;

    /**
     * taobaoNick对应的汉字转为拼音首字母
     */
    private String searchStr;
    /**
     * taobaoNick对应的汉字转为拼音全拼
     */
    private String pinyinStr;

    /**
     * 是否统计对应的3000单数据
     */
    private Integer standardPrintNumsFlag;

    /**
     * 版本类型
     */
    private Integer versionType;

    /**
     * 是否共享电子面单 0：否 1：是 默认1
     */
    private Integer shareWayBill;

    /**
     * 是否是关联代发，0：不是 1：是
     */
    private Integer isDaiFaShop = 0;

    public Integer getIsDaiFaShop() {
        return isDaiFaShop;
    }

    public void setIsDaiFaShop(Integer isDaiFaShop) {
        this.isDaiFaShop = isDaiFaShop;
    }

    public Integer getStandardPrintNumsFlag() {
        return standardPrintNumsFlag;
    }

    public void setStandardPrintNumsFlag(Integer standardPrintNumsFlag) {
        this.standardPrintNumsFlag = standardPrintNumsFlag;
    }

    public String getSearchStr() {
        return searchStr;
    }

    public void setSearchStr(String searchStr) {
        this.searchStr = searchStr;
    }

    public String getPinyinStr() {
        return pinyinStr;
    }

    public void setPinyinStr(String pinyinStr) {
        this.pinyinStr = pinyinStr;
    }

    public Long getSubGroupId() {
        return subGroupId;
    }

    public void setSubGroupId(Long subGroupId) {
        this.subGroupId = subGroupId;
    }

    /**
     * standard_print_nums  标准版用户单量记录
     */
    private Long standardPrintNums;

    public Long getStandardPrintNums() {
        return standardPrintNums;
    }

    public void setStandardPrintNums(Long standardPrintNums) {
        this.standardPrintNums = standardPrintNums;
    }

    public Boolean getSettingYdNum() {
        return settingYdNum;
    }

    public void setSettingYdNum(Boolean settingYdNum) {
        this.settingYdNum = settingYdNum;
    }

    public Integer getYdNum() {
        return ydNum;
    }

    public void setYdNum(Integer ydNum) {
        this.ydNum = ydNum;
    }

    public Boolean getInitStockStatus() {
        return initStockStatus;
    }

    public void setInitStockStatus(Boolean initStockStatus) {
        this.initStockStatus = initStockStatus;
    }

    public Boolean getInitProductStatus() {
        return initProductStatus;
    }

    public void setInitProductStatus(Boolean initProductStatus) {
        this.initProductStatus = initProductStatus;
    }

    public Boolean getAllowSubscribe() {
        return allowSubscribe;
    }

    public void setAllowSubscribe(Boolean allowSubscribe) {
        this.allowSubscribe = allowSubscribe;
    }

    public String getStockExpireDate() {
        return stockExpireDate;
    }

    public void setStockExpireDate(String stockExpireDate) {
        this.stockExpireDate = stockExpireDate;
    }

    public Boolean getStockExpireStatus() {
        return stockExpireStatus;
    }

    public void setStockExpireStatus(Boolean stockExpireStatus) {
        this.stockExpireStatus = stockExpireStatus;
    }

    public int getIs3C() {
        return is3C;
    }

    public RelaNick setIs3C(int is3C) {
        this.is3C = is3C;
        return this;
    }

    public Long getPrintNum() {
        return printNum;
    }

    public void setPrintNum(Long printNum) {
        this.printNum = printNum;
    }

    public String getExpireDate() {
        return expireDate;
    }

    public void setExpireDate(String expireDate) {
        this.expireDate = expireDate;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Long getTotalPrintNum() {
        return totalPrintNum;
    }

    public void setTotalPrintNum(Long totalPrintNum) {
        this.totalPrintNum = totalPrintNum;
    }

    public Integer getIsFenxiao() {
        return isFenxiao;
    }

    public void setIsFenxiao(Integer isFenxiao) {
        this.isFenxiao = isFenxiao;
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public int getIsSee() {
        return isSee;
    }

    public void setIsSee(int isSee) {
        this.isSee = isSee;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public String getTaobaoNick() {
        return taobaoNick;
    }

    public void setTaobaoNick(String taobaoNick) {
        this.taobaoNick = taobaoNick;
    }

    public int getIsAdmin() {
        return isAdmin;
    }

    public void setIsAdmin(int isAdmin) {
        this.isAdmin = isAdmin;
    }

    public int getVip() {
        return vip;
    }

    public void setVip(int vip) {
        this.vip = vip;
    }

    public Integer getVersionType() {
        return versionType;
    }

    public void setVersionType(Integer versionType) {
        this.versionType = versionType;
    }

    public Integer getIsNewFreeUser() {
        return isNewFreeUser;
    }

    public void setIsNewFreeUser(Integer isNewFreeUser) {
        this.isNewFreeUser = isNewFreeUser;
    }

    public Integer getShareWayBill() {
        return shareWayBill;
    }

    public void setShareWayBill(Integer shareWayBill) {
        this.shareWayBill = shareWayBill;
    }
}
