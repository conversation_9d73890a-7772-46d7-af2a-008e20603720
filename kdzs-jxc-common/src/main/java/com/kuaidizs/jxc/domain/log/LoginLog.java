package com.kuaidizs.jxc.domain.log;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;
import java.io.Serializable;

/**
 * <AUTHOR>
 * Date    2023-05-16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class LoginLog implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * 是否可用 0：不可用 1：可用 默认1
     */
    private Integer enableStatus;
    /**
     * ip地址
     */
    private String ip;
    /**
     * 淘宝ID
     */
    private Long taobaoId;
    /**
     * 登录账号
     */
    private String loginAccount;
    /**
     * 登录时间
     */
    private Date loginTime;
    /**
     * 是否TJ登录 0：否 1：是 默认0
     */
    private Integer isTj;
    /**
     * 分库标识
     */
    private String fkId;
    /**
     * 分表标识
     */
    private String tableName;

}