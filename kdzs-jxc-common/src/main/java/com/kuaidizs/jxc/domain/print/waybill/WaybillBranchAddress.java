package com.kuaidizs.jxc.domain.print.waybill;

import com.alibaba.fastjson.annotation.JSONField;

import java.io.Serializable;

/**
 * 电子面单网点地址信息
 *
 * <AUTHOR>    Date:2016/7/30  14:52
 */
public class WaybillBranchAddress implements Serializable {

    private static final long serialVersionUID = -1L;

    /**
     * 网点地址id
     */
    @JSONField(name = "WaybillAddressId")
    private Long waybillAddressId;

    /**
     * 网点编码
     */
    @JSONField(name = "BranchCode")
    private String branchCode;

    /**
     * 发件人姓名
     */
    @JSONField(name = "SendName")
    private String sendName;

    /**
     * 发件人手机
     */
    @JSONField(name = "SendMobile")
    private String sendMobile;

    /**
     * 发件人电话
     */
    @JSONField(name = "SendPhone")
    private String sendPhone;

    /**
     * 发件人邮编
     */
    @JSONField(name = "SendZip")
    private String sendZip;

    /**
     * 发件人省
     */
    @JSONField(name = "SendProvince")
    private String sendProvince;

    /**
     * 发件人市
     */
    @JSONField(name = "SendCity")
    private String sendCity;

    /**
     * 发件人区
     */
    @JSONField(name = "SendDistrict")
    private String sendDistrict;

    /**
     * 发件人县镇
     */
    @JSONField(name = "SendTown")
    private String sendTown;

    /**
     * 发件人地址
     */
    @JSONField(name = "SendAddress")
    private String sendAddress;

    public Long getWaybillAddressId() {
        return waybillAddressId;
    }

    public void setWaybillAddressId(Long waybillAddressId) {
        this.waybillAddressId = waybillAddressId;
    }

    public String getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(String branchCode) {
        this.branchCode = branchCode;
    }

    public String getSendName() {
        return sendName;
    }

    public void setSendName(String sendName) {
        this.sendName = sendName;
    }

    public String getSendMobile() {
        return sendMobile;
    }

    public void setSendMobile(String sendMobile) {
        this.sendMobile = sendMobile;
    }

    public String getSendPhone() {
        return sendPhone;
    }

    public void setSendPhone(String sendPhone) {
        this.sendPhone = sendPhone;
    }

    public String getSendZip() {
        return sendZip;
    }

    public void setSendZip(String sendZip) {
        this.sendZip = sendZip;
    }

    public String getSendProvince() {
        return sendProvince;
    }

    public void setSendProvince(String sendProvince) {
        this.sendProvince = sendProvince;
    }

    public String getSendCity() {
        return sendCity;
    }

    public void setSendCity(String sendCity) {
        this.sendCity = sendCity;
    }

    public String getSendDistrict() {
        return sendDistrict;
    }

    public void setSendDistrict(String sendDistrict) {
        this.sendDistrict = sendDistrict;
    }

    public String getSendTown() {
        return sendTown;
    }

    public void setSendTown(String sendTown) {
        this.sendTown = sendTown;
    }

    public String getSendAddress() {
        return sendAddress;
    }

    public void setSendAddress(String sendAddress) {
        this.sendAddress = sendAddress;
    }
}
