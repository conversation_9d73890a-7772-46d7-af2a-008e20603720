package com.kuaidizs.jxc.domain.log;

import java.util.*;
import java.io.Serializable;

/**
 * <AUTHOR> <PERSON>jun
 * Date    2019-02-25
 */
public class OperateLog implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;
    /**
     * 淘宝id
     */
    private Long taobaoId;
    /**
     * 操作内容类型 0:快递单打印信息，1：订单修改，2：订单合并
     */
    private Integer type;
    /**
     * 订单号
     */
    private String tid;
    /**
     * 日志内容/查询条件
     */
    private String content;
    /**
     * 0:淘宝，1:手工单 2:QAP
     */
    private Integer source;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 操作者ip
     */
    private String ip;
    /**
     * 1:可用,0:删除
     */
    private Integer enableStatus;
    /**
     * 添加时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;


    /**
     * @return id id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return taobaoId 淘宝id
     */
    public Long getTaobaoId() {
        return taobaoId;
    }

    /**
     * @param taobaoId 淘宝id
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return type 操作内容类型 0:快递单打印信息，1：订单修改，2：订单合并
     */
    public Integer getType() {
        return type;
    }

    /**
     * @param type 操作内容类型 0:快递单打印信息，1：订单修改，2：订单合并
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * @return tid 订单号
     */
    public String getTid() {
        return tid;
    }

    /**
     * @param tid 订单号
     */
    public void setTid(String tid) {
        this.tid = tid;
    }

    /**
     * @return content 日志内容/查询条件
     */
    public String getContent() {
        return content;
    }

    /**
     * @param content 日志内容/查询条件
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * @return source 0:淘宝，1:手工单
     */
    public Integer getSource() {
        return source;
    }

    /**
     * @param source 0:淘宝，1:手工单
     */
    public void setSource(Integer source) {
        this.source = source;
    }

    /**
     * @return operator 操作人
     */
    public String getOperator() {
        return operator;
    }

    /**
     * @param operator 操作人
     */
    public void setOperator(String operator) {
        this.operator = operator;
    }

    /**
     * @return ip 操作者ip
     */
    public String getIp() {
        return ip;
    }

    /**
     * @param ip 操作者ip
     */
    public void setIp(String ip) {
        this.ip = ip;
    }

    /**
     * @return enableStatus 1:可用,0:删除
     */
    public Integer getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 1:可用,0:删除
     */
    public void setEnableStatus(Integer enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return created 添加时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 添加时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * 快递模版id
     */
    private String templateId;

    /**
     * 快递模版名称
     */
    private String templateName;

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public String getTemplateName() {
        return templateName;
    }

    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    /**
     * 分表使用
     */
    private String tableName;

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    /**
     * 分库标识
     */
    private String fkId;

    public String getFkId() {
        return fkId;
    }

    public void setFkId(String fkId) {
        this.fkId = fkId;
    }
}