package com.kuaidizs.jxc.domain.cjdf;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/4/21.
 * @time 17:54.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CjdfDistributeLogExport implements Serializable {
    private static final long serialVersionUID = 1329860519030540803L;

    /**
     * 订单编号
     */
    private String oid;

    /**
     * 代发订单编号
     */
    private String scpTid;

    /**
     * 供应商平台名称
     */
    private String supplierName;

    /**
     * 分配状态 2：已分配 3：取消分配 4：分配失败(不展示，enable_status置为0) 5：取消分配失败(不展示，enable_status置为0)
     */
    private String distributeStatus;

    /**
     * 操作时间
     */
    private String modifyTime;

    /**
     * 操作人淘宝昵称，精确到子账号
     */
    private String operatorNick;
}
