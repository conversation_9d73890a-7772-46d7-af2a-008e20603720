package com.kuaidizs.jxc.domain.print.enums;

/**
 * 电子面单的直营型、加盟型
 *
 * <AUTHOR>    Date:2016/7/30  11:06
 */
public enum EnumCpType {

    /**
     * 直营型
     */
    Direct(1),

    /**
     * 加盟型
     */
    Affiliate(2);

    private long value;

    EnumCpType(long value) {
        this.value = value;
    }

    public long getValue() {
        return value;
    }

    public void setValue(int value) {
        this.value = value;
    }
}
