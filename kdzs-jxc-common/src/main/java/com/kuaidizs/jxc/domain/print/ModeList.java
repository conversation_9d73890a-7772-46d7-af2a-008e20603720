package com.kuaidizs.jxc.domain.print;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;

import java.util.*;
import java.io.Serializable;

/**
 * <AUTHOR> @date 2016-07-25
 */
public class ModeList implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @JSONField(name = "Exid", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer exid;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * 逻辑状态
     */
    private Boolean enableStatus;
    /**
     * 快递类型
     */
    @JSONField(name = "Excode", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String excode;
    /**
     * 快递名称
     */
    @JSONField(name = "ExcodeName", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String excodeName;
    /**
     * 快递名称简称
     */
    @JSONField(name = "Excodename_small", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String excodeNameSmall;
    /**
     * 纸张宽度
     */
    @JSONField(name = "WidthPaper", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer widthPaper;
    /**
     * 纸张高度
     */
    @JSONField(name = "HeightPaper", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer heightPaper;
    /**
     * 图片宽度
     */
    @JSONField(name = "WidthImg", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer widthImg;
    /**
     * 图片高度
     */
    @JSONField(name = "HeightImg", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer heightImg;
    /**
     * 排序id
     */
    @JSONField(name = "Orderid", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer orderId;
    /**
     * 快递类型对应平台的快递类型id
     */
    @JSONField(name = "PartnerId", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String partnerId;
    /**
     * 快递单类型 1普通面单;2网店账号面单;3云站电子面单;
     */
    @JSONField(name = "KddType", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer kddType;
    /**
     * 上线状态，共三种：OFF_LINE表示已经下线，FREEZE表示冻结中，ON_LINE表示使用中
     */

    private String onlineStatus;
    /**
     * 样式id
     */
    @JSONField(name = "StyleId", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer modeKddStyleId;
    /**
     * 快递公司id
     */
    @JSONField(name = "CompanyId", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer exCompanyId;

    /**
     * 是否为默认尺寸，如果是默认尺寸则选中
     */
    @JSONField(name = "isDefault", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer isDefault;

    /**
     * 是否为默认尺寸，如果是默认尺寸则选中
     */
    @JSONField(name = "templateUrl", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String templateUrl;

    /**
     * 模版显示的位子;1、PC;2、小程序
     */
    private Integer source;

    /**
     * 是否是快运公司,如果是，那么就给这个字段赋值为true，如果不是，那么可以不赋值或者赋值为false
     */
    private Boolean kuaiYun;

    /**
     * @return exid 自增ID
     */
    public Integer getExid() {
        return exid;
    }

    /**
     * @param exid 自增ID
     */
    public void setExid(Integer exid) {
        this.exid = exid;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return enableStatus 逻辑状态
     */
    public Boolean getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 逻辑状态
     */
    public void setEnableStatus(Boolean enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return excode 快递类型
     */
    public String getExcode() {
        return excode;
    }

    /**
     * @param excode 快递类型
     */
    public void setExcode(String excode) {
        this.excode = excode;
    }

    /**
     * @return excodeName 快递名称
     */
    public String getExcodeName() {
        return excodeName;
    }

    /**
     * @param excodeName 快递名称
     */
    public void setExcodeName(String excodeName) {
        this.excodeName = excodeName;
    }

    /**
     * @return excodeNameSmall 快递名称简称
     */
    public String getExcodeNameSmall() {
        return excodeNameSmall;
    }

    /**
     * @param excodeNameSmall 快递名称简称
     */
    public void setExcodeNameSmall(String excodeNameSmall) {
        this.excodeNameSmall = excodeNameSmall;
    }

    /**
     * @return widthPaper 纸张宽度
     */
    public Integer getWidthPaper() {
        return widthPaper;
    }

    /**
     * @param widthPaper 纸张宽度
     */
    public void setWidthPaper(Integer widthPaper) {
        this.widthPaper = widthPaper;
    }

    /**
     * @return heightPaper 纸张高度
     */
    public Integer getHeightPaper() {
        return heightPaper;
    }

    /**
     * @param heightPaper 纸张高度
     */
    public void setHeightPaper(Integer heightPaper) {
        this.heightPaper = heightPaper;
    }

    /**
     * @return widthImg 图片宽度
     */
    public Integer getWidthImg() {
        return widthImg;
    }

    /**
     * @param widthImg 图片宽度
     */
    public void setWidthImg(Integer widthImg) {
        this.widthImg = widthImg;
    }

    /**
     * @return heightImg 图片高度
     */
    public Integer getHeightImg() {
        return heightImg;
    }

    /**
     * @param heightImg 图片高度
     */
    public void setHeightImg(Integer heightImg) {
        this.heightImg = heightImg;
    }

    /**
     * @return orderId 排序id
     */
    public Integer getOrderId() {
        return orderId;
    }

    /**
     * @param orderId 排序id
     */
    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    /**
     * @return partnerId 快递类型对应平台的快递类型id
     */
    public String getPartnerId() {
        return partnerId;
    }

    /**
     * @param partnerId 快递类型对应平台的快递类型id
     */
    public void setPartnerId(String partnerId) {
        this.partnerId = partnerId;
    }

    /**
     * @return kddType 快递单类型 1普通面单;2网店账号面单;3云站电子面单;
     */
    public Integer getKddType() {
        return kddType;
    }

    /**
     * @param kddType 快递单类型 1普通面单;2网店账号面单;3云站电子面单;
     */
    public void setKddType(Integer kddType) {
        this.kddType = kddType;
    }

    /**
     * @return onlineStatus 上线状态，共三种：OFF_LINE表示已经下线，FREEZE表示冻结中，ON_LINE表示使用中
     */
    public String getOnlineStatus() {
        return onlineStatus;
    }

    /**
     * @param onlineStatus 上线状态，共三种：OFF_LINE表示已经下线，FREEZE表示冻结中，ON_LINE表示使用中
     */
    public void setOnlineStatus(String onlineStatus) {
        this.onlineStatus = onlineStatus;
    }

    /**
     * @return modeKddStyleId 样式id
     */
    public Integer getModeKddStyleId() {
        return modeKddStyleId;
    }

    /**
     * @param modeKddStyleId 样式id
     */
    public void setModeKddStyleId(Integer modeKddStyleId) {
        this.modeKddStyleId = modeKddStyleId;
    }

    /**
     * @return exCompanyId 快递公司id
     */
    public Integer getExCompanyId() {
        return exCompanyId;
    }

    /**
     * @param exCompanyId 快递公司id
     */
    public void setExCompanyId(Integer exCompanyId) {
        this.exCompanyId = exCompanyId;
    }

    public Integer getIsDefault() {
        return isDefault;
    }

    public void setIsDefault(Integer isDefault) {
        this.isDefault = isDefault;
    }

    public String companyIdKddTye() {
        return exCompanyId + "-" + kddType;
    }

    public String companyIdKddTyeStyleId() {
        return exCompanyId + "-" + kddType + "-" + modeKddStyleId;
    }

    public String companyIdKddTyeStyleIdHeightPaper() {
        return exCompanyId + "-" + kddType + "-" + modeKddStyleId + "-" + heightPaper;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

    public String getTemplateUrl() {
        return templateUrl;
    }

    public void setTemplateUrl(String templateUrl) {
        this.templateUrl = templateUrl;
    }

    public Boolean getKuaiYun() {
        return kuaiYun;
    }

    public void setKuaiYun(Boolean kuaiYun) {
        this.kuaiYun = kuaiYun;
    }
}