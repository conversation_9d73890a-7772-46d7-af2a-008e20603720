package com.kuaidizs.jxc.domain.download.dataCenter;

import com.kuaidizs.jxc.domain.trade.TradeExportConfig;

/**
 * @auther xudaomeng
 * @since 2021-01-04 16:15
 */
public class ShareCountTotalExportBean {

    /**
     * 统计时间 详细记录返回
     */
    @TradeExportConfig.ExportField(name = "统计时间", value = 1, order = 1)
    private String countDate;

    /**
     * 分享数量
     */
    @TradeExportConfig.ExportField(name = "分享数量", value = 1, order = 6)
    private String shareNumber;

    /**
     * 使用数量
     */
    @TradeExportConfig.ExportField(name = "使用数量", value = 1, order = 7)
    private Integer shareUsedNumber;

    /**
     * 回收数量
     */
    @TradeExportConfig.ExportField(name = "回收数量", value = 1, order = 8)
    private Integer shareCancelNumber;

    public String getCountDate() {
        return countDate;
    }

    public void setCountDate(String countDate) {
        this.countDate = countDate;
    }

    public String getShareNumber() {
        return shareNumber;
    }

    public void setShareNumber(String shareNumber) {
        this.shareNumber = shareNumber;
    }

    public Integer getShareUsedNumber() {
        return shareUsedNumber;
    }

    public void setShareUsedNumber(Integer shareUsedNumber) {
        this.shareUsedNumber = shareUsedNumber;
    }

    public Integer getShareCancelNumber() {
        return shareCancelNumber;
    }

    public void setShareCancelNumber(Integer shareCancelNumber) {
        this.shareCancelNumber = shareCancelNumber;
    }
}
