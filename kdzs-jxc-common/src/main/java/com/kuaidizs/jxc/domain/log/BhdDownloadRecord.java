package com.kuaidizs.jxc.domain.log;

import java.io.Serializable;
import java.util.Date;

/**
 *
 */
public class BhdDownloadRecord implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 主键自增id
     */
    private Long id;
    /**
     * 用户淘宝id
     */
    private Long taobaoId;
    /**
     * 查询内容（json字符串）
     */
    private String queryContent;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;

    /**
     * 查询条件（文字版）
     */
    private String queryCondition;

    /**
     * 分表使用
     */
    private String tableName;

    /**
     * 分库标识
     */
    private String fkId;



    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getFkId() {
        return fkId;
    }

    public void setFkId(String fkId) {
        this.fkId = fkId;
    }


    /**
     * @return id 主键自增id
     */
    public Long getId() {
        return id;
    }

    /**
     * @param id 主键自增id
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * @return taobaoId 用户淘宝id
     */
    public Long getTaobaoId() {
        return taobaoId;
    }

    /**
     * @param taobaoId 用户淘宝id
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return queryContent 查询内容（json字符串）
     */
    public String getQueryContent() {
        return queryContent;
    }

    /**
     * @param queryContent 查询内容（json字符串）
     */
    public void setQueryContent(String queryContent) {
        this.queryContent = queryContent;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    public String getQueryCondition() {
        return queryCondition;
    }

    public void setQueryCondition(String queryCondition) {
        this.queryCondition = queryCondition;
    }
}