package com.kuaidizs.jxc.domain.print;

import java.util.*;
import java.io.Serializable;

/**
 *
 * <AUTHOR> @date    2016-07-26
 */
public class ModeListshowCommon implements Serializable{

    /**
	 *序列化ID
	 */
	private static final long serialVersionUID = 1L;

	/**
     * 主键id
     */
    private Integer modeListshowId;
	/**
     * 添加时间
     */
    private Date created;
	/**
     * 修改时间
     */
    private Date modified;
	/**
     * 逻辑删除
     */
    private Boolean enableStatus;
	/**
     * ex_id
     */
    private Integer exid;
	/**
     * 快递单显示名称
     */
    private String excodeName;
	/**
     * 整体偏移X
     */
    private Integer allX;
	/**
     * 整体偏移Y
     */
    private Integer allY;
	/**
     * 是否默认模板 0非默认 1默认
     */
    private Integer isDef;
	/**
     * 快递单还是发货单 kdd/fhd
     */
    private String modeId;
	/**
     * 当前模板对应的快递单号
     */
    private String exNumber;
	/**
     * 图片地址
     */
    private String imgSrc;
	/**
     * 底图id(model_listimg 主键id )
     */
    private Integer eximgId;
	/**
     * 新用户默认数据
     */
    private Integer newuserOrderId;

	
   /**
    * @return modeListshowId 主键id
    */
    public Integer getModeListshowId() {
       return modeListshowId;
    }
   /**
    * @param modeListshowId 主键id
    */
    public void setModeListshowId(Integer modeListshowId) {
       this.modeListshowId = modeListshowId;
    }
	
   /**
    * @return created 添加时间
    */
    public Date getCreated() {
       return created;
    }
   /**
    * @param created 添加时间
    */
    public void setCreated(Date created) {
       this.created = created;
    }
	
   /**
    * @return modified 修改时间
    */
    public Date getModified() {
       return modified;
    }
   /**
    * @param modified 修改时间
    */
    public void setModified(Date modified) {
       this.modified = modified;
    }
	
   /**
    * @return enableStatus 逻辑删除
    */
    public Boolean getEnableStatus() {
       return enableStatus;
    }
   /**
    * @param enableStatus 逻辑删除
    */
    public void setEnableStatus(Boolean enableStatus) {
       this.enableStatus = enableStatus;
    }
	
   /**
    * @return exid exid
    */
    public Integer getExid() {
       return exid;
    }
   /**
    * @param exid exid
    */
    public void setExid(Integer exid) {
       this.exid = exid;
    }
	
   /**
    * @return excodeName 快递单显示名称
    */
    public String getExcodeName() {
       return excodeName;
    }
   /**
    * @param excodeName 快递单显示名称
    */
    public void setExcodeName(String excodeName) {
       this.excodeName = excodeName;
    }
	
   /**
    * @return allX 整体偏移X
    */
    public Integer getAllX() {
       return allX;
    }
   /**
    * @param allX 整体偏移X
    */
    public void setAllX(Integer allX) {
       this.allX = allX;
    }
	
   /**
    * @return allY 整体偏移Y
    */
    public Integer getAllY() {
       return allY;
    }
   /**
    * @param allY 整体偏移Y
    */
    public void setAllY(Integer allY) {
       this.allY = allY;
    }
	
   /**
    * @return isDef 是否默认模板 0非默认 1默认
    */
    public Integer getIsDef() {
       return isDef;
    }
   /**
    * @param isDef 是否默认模板 0非默认 1默认
    */
    public void setIsDef(Integer isDef) {
       this.isDef = isDef;
    }
	
   /**
    * @return modeId 快递单还是发货单 kdd/fhd
    */
    public String getModeId() {
       return modeId;
    }
   /**
    * @param modeId 快递单还是发货单 kdd/fhd
    */
    public void setModeId(String modeId) {
       this.modeId = modeId;
    }
	
   /**
    * @return exNumber 当前模板对应的快递单号
    */
    public String getExNumber() {
       return exNumber;
    }
   /**
    * @param exNumber 当前模板对应的快递单号
    */
    public void setExNumber(String exNumber) {
       this.exNumber = exNumber;
    }
	
   /**
    * @return imgSrc 图片地址
    */
    public String getImgSrc() {
       return imgSrc;
    }
   /**
    * @param imgSrc 图片地址
    */
    public void setImgSrc(String imgSrc) {
       this.imgSrc = imgSrc;
    }
	
   /**
    * @return eximgId 底图id(model_listimg 主键id )
    */
    public Integer getEximgId() {
       return eximgId;
    }
   /**
    * @param eximgId 底图id(model_listimg 主键id )
    */
    public void setEximgId(Integer eximgId) {
       this.eximgId = eximgId;
    }
	
   /**
    * @return newuserOrderId 新用户默认数据
    */
    public Integer getNewuserOrderId() {
       return newuserOrderId;
    }
   /**
    * @param newuserOrderId 新用户默认数据
    */
    public void setNewuserOrderId(Integer newuserOrderId) {
       this.newuserOrderId = newuserOrderId;
    }

}