package com.kuaidizs.jxc.domain.log;

import lombok.Data;

/**
 * <AUTHOR>
 * @create 2025/02/17 16:56
 * @description:
 */
@Data
public class UserApiAnalysis {

    private Long id;                       // 自增的主键
    private String dateStr;                // 日期字符串
    private Long taobaoId;               // 淘宝ID
    private String taobaoNick;             // 淘宝昵称
    private String version;                 // 版本类型（免费、基础、高级、专业）
    private Integer tradeFullinfo;          // 交易完整信息
    private Integer tradeSoldget;          // 交易成交数
    private Integer tradeSoldIncrement;     // 交易增量
    private Integer itemOnsale;            // 上架商品数
    private Integer itemInventory;         // 商品库存
    private Integer itemSellerList;         // 卖家列表
    private Integer itemSellerGet;         // 卖家成交数
    private Integer printNum;              // 一天打印单数，默认0
    private Integer sendNum;               // 一天发货单数，默认0
    private Integer orderQueryCount;       // 批打查询次数，默认0

}
