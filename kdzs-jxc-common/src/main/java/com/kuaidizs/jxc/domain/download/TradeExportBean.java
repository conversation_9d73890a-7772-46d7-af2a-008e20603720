package com.kuaidizs.jxc.domain.download;

import com.kuaidizs.jxc.domain.trade.TradeExportConfig.ExportField;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * TradeExportBean
 */
public class TradeExportBean implements Serializable {

    private static final long serialVersionUID = -477295828317086147L;

    /**
     * 订单编号
     */
    @ExportField(name = "订单编号", value = 1, order = 1)
    private String tids;

    /**
     * 下单时间
     */
    @ExportField(name = "下单时间", value = 1, order = 2)
    private String xdTime;

    /**
     * 付款时间
     */
    @ExportField(name = "付款时间", order = 3)
    private String fkTime;

    /**
     * 打印时间
     */
    @ExportField(name = "打印时间", order = 4)
    private String dyTime;

    /**
     * 发货时间
     */
    @ExportField(name = "发货时间", order = 5)
    private String fhTime;

    /**
     * 买家旺旺
     */
    @ExportField(name = "买家旺旺", value = 1, order = 6)
    private String buyerNick;

    /**
     * 收件人
     */
    @ExportField(name = "收件人", value = 1, order = 7)
    private String receiverName;

    /**
     * 联系电话
     */
    @ExportField(name = "联系电话", value = 1, order = 8)
    private String receiverMobile;

    /**
     * 收件地址
     */
    @ExportField(name = "收件地址", value = 1, order = 9)
    private String receiverAddress;

    /**
     * 商品信息
     */
    @ExportField(name = "商品信息", value = 1, order = 10)
    private String itemInfos;

    /**
     * 实付金额
     */
    @ExportField(name = "实付金额", value = 1, order = 16)
    private String payment;

    /**
     * 买家留言
     */
    @ExportField(name = "买家留言", order = 17)
    private String buyerMessage;

    /**
     * 卖家备注
     */
    @ExportField(name = "卖家备注", order = 18)
    private String sellerMemo;

    /**
     * 发货内容
     */
    @ExportField(name = "发货内容", order = 19)
    private String info;

    /**
     * 快递单号
     */
    @ExportField(name = "快递单号", order = 20)
    private String ydNos;

    /**
     * 旗帜
     */
    @ExportField(name = "旗帜", order = 21)
    private String sellerFlag;

    /**
     * 快递公司
     */
    @ExportField(name = "快递公司", order = 22)
    private String kdName;

    /**
     * 运单号
     */
    @ExportField(name = "运单号", order = 23)
    private String ydNo;

    @ExportField(name = "店铺名", order = 0)
    private String shopName;

    /**
     * 订单状态
     */
    @ExportField(name = "订单状态", order = 24)
    private String orderStatus;

    @ExportField(name="宝贝数量", order = 29)
    private String num;

    @ExportField(name="助手备注", order = 30)
    private String kdzsMemo;

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    private String totalFees;

    public static Map<String, String> flagMap = new HashMap<String, String>();

    static {
        if (flagMap.size() == 0) {
            flagMap.put("1", "红");
            flagMap.put("2", "黄");
            flagMap.put("3", "绿");
            flagMap.put("4", "蓝");
            flagMap.put("5", "紫");
            flagMap.put("0", "灰");
        }
    }

    public String getKdName() {
        return kdName;
    }

    public void setKdName(String kdName) {
        this.kdName = kdName;
    }

    public String getYdNo() {
        return ydNo;
    }

    public void setYdNo(String ydNo) {
        this.ydNo = ydNo;
    }

    public String getSellerFlag() {
        return sellerFlag;
    }

    public void setSellerFlag(String sellerFlag) {
        this.sellerFlag = sellerFlag;
    }

    public String getYdNos() {
        return ydNos;
    }

    public void setYdNos(String ydNos) {
        this.ydNos = ydNos;
    }

    public String getPayment() {
        return payment;
    }

    public void setPayment(String payment) {
        this.payment = payment;
    }

    public String getTids() {
        return tids;
    }

    public void setTids(String tids) {
        this.tids = tids;
    }

    public String getBuyerNick() {
        return buyerNick;
    }

    public void setBuyerNick(String buyerNick) {
        this.buyerNick = buyerNick;
    }

    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    public String getReceiverAddress() {
        return receiverAddress;
    }

    public void setReceiverAddress(String receiverAddress) {
        this.receiverAddress = receiverAddress;
    }

    public String getReceiverMobile() {
        return receiverMobile;
    }

    public void setReceiverMobile(String receiverMobile) {
        this.receiverMobile = receiverMobile;
    }

    public String getItemInfos() {
        return itemInfos;
    }

    public void setItemInfos(String itemInfos) {
        this.itemInfos = itemInfos;
    }

    public String getTotalFees() {
        return totalFees;
    }

    public void setTotalFees(String totalFees) {
        this.totalFees = totalFees;
    }

    public String getBuyerMessage() {
        return buyerMessage;
    }

    public void setBuyerMessage(String buyerMessage) {
        this.buyerMessage = buyerMessage;
    }

    public String getSellerMemo() {
        return sellerMemo;
    }

    public void setSellerMemo(String sellerMemo) {
        this.sellerMemo = sellerMemo;
    }

    public String getXdTime() {
        return xdTime;
    }

    public void setXdTime(String xdTime) {
        this.xdTime = xdTime;
    }

    public String getFkTime() {
        return fkTime;
    }

    public void setFkTime(String fkTime) {
        this.fkTime = fkTime;
    }

    public String getFhTime() {
        return fhTime;
    }

    public void setFhTime(String fhTime) {
        this.fhTime = fhTime;
    }

    public String getDyTime() {
        return dyTime;
    }

    public void setDyTime(String dyTime) {
        this.dyTime = dyTime;
    }

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }

    public String getNum() {
        return num;
    }

    public void setNum(String num) {
        this.num = num;
    }

    public String getKdzsMemo() {
        return kdzsMemo;
    }

    public void setKdzsMemo(String kdzsMemo) {
        this.kdzsMemo = kdzsMemo;
    }
}

