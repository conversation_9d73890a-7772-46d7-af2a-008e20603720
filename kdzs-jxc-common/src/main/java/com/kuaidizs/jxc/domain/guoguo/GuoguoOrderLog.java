package com.kuaidizs.jxc.domain.guoguo;

import java.util.Date;

/**
 * @auther xudaomeng
 * @since 2020-02-12 15:23
 */
public class GuoguoOrderLog {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;
    /**
     * 自增id
     */
    private Long id;
    /**
     * 订单id
     */
    private String tid;
    /**
     * 裹裹id
     */
    private Long guoguoId;

    /**
     * 对货码
     */
    private String dhNo;
    /**
     * 寄件地址
     */
    private String sendAddress;
    /**
     * 取件码
     */
    private String gotCode;

    /**
     * 联系人
     */
    private String linkman;
    /**
     * 联系方式
     */
    private String phone;

    /**
     * 运单号
     */
    private String ydId;

    /**
     * 打印方式
     * 1：为自己打印
     * 2：为快递员打印
     */
    private Integer printWay;

    private Long taobaoId;

    /**
     * 操作人
     */
    private String opName;

    /**
     * 0：未取消
     * 1：取消
     */
    private Integer status;

    private Date created;

    private Date modified;


    private String tableName;

    private String fkId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public Long getGuoguoId() {
        return guoguoId;
    }

    public void setGuoguoId(Long guoguoId) {
        this.guoguoId = guoguoId;
    }


    public String getDhNo() {
        return dhNo;
    }

    public void setDhNo(String dhNo) {
        this.dhNo = dhNo;
    }

    public String getSendAddress() {
        return sendAddress;
    }

    public void setSendAddress(String sendAddress) {
        this.sendAddress = sendAddress;
    }


    public String getGotCode() {
        return gotCode;
    }

    public void setGotCode(String gotCode) {
        this.gotCode = gotCode;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getFkId() {
        return fkId;
    }

    public void setFkId(String fkId) {
        this.fkId = fkId;
    }

    public Integer getPrintWay() {
        return printWay;
    }

    public void setPrintWay(Integer printWay) {
        this.printWay = printWay;
    }

    public String getLinkman() {
        return linkman;
    }

    public void setLinkman(String linkman) {
        this.linkman = linkman;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Date getCreated() {
        return created;
    }

    public void setCreated(Date created) {
        this.created = created;
    }

    public Date getModified() {
        return modified;
    }

    public void setModified(Date modified) {
        this.modified = modified;
    }

    public String getYdId() {
        return ydId;
    }

    public void setYdId(String ydId) {
        this.ydId = ydId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public String getOpName() {
        return opName;
    }

    public void setOpName(String opName) {
        this.opName = opName;
    }
}
