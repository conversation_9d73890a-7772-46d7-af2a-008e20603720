package com.kuaidizs.jxc.domain.print;

import com.alibaba.fastjson.annotation.JSONField;
import com.alibaba.fastjson.serializer.SerializerFeature;

import java.io.Serializable;
import java.util.Date;

/**
 * 模版基础设置
 * 注：模版的宽高、打印机等设置信息
 *
 * <AUTHOR> @date 2016-07-26
 */
public class ModeTempPrintCfg implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @JSONField(name = "Id", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer id;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * 逻辑删除
     * 默认1
     */
    private Boolean enableStatus;
    /**
     * 模板ID
     */
    @JSONField(name = "Exshowid", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer exshowId;
    /**
     * 左右偏移量
     * 默认0
     */
    @JSONField(name = "Leftright", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer leftRight;
    /**
     * 上下偏移量
     * 默认0
     */
    @JSONField(name = "Updown", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer upDown;
    /**
     * 纸张宽
     */
    @JSONField(name = "Width", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String width;
    /**
     * 纸张高
     */
    @JSONField(name = "Height", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String height;
    /**
     * 打印方向：0:打印机默认;1:横向;2:纵向;
     * 默认0
     */
    @JSONField(name = "Direction", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer direction;
    /**
     * 打印边界:0:实际边界值;1:打印机可打印区域
     * 默认0
     */
    @JSONField(name = "Border", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer border;
    /**
     * 是否需要打印签收联logo:0:不需要;1:需要
     * 默认0
     */
    @JSONField(name = "Qslogo", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer qsLogo;
    /**
     * 是否需要打印留存联logo:0:不需要;1:需要
     * 默认0
     */
    @JSONField(name = "Lclogo", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer lcLogo;
    /**
     * 预留字段1
     */
    @JSONField(name = "Reserve1", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String reserve1;
    /**
     * 预留字段2
     */
    @JSONField(name = "Reserve2", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String reserve2;
    /**
     * 预留字段3
     */
    @JSONField(name = "Reserve3", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String reserve3;
    /**
     * 预留字段4
     */
    @JSONField(name = "Reserve4", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String reserve4;
    /**
     * 默认打印机
     */
    @JSONField(name = "DefaultPrinter", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String defaultPrinter;

    /**
     * 是否拼接快递单发货单打印,默认不拼接
     */
    @JSONField(name = "IsConcatFhd", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Integer isConcatFhd;

    /**
     * 用户ID
     */
    @JSONField(name = "taobaoId", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private Long taobaoId;

    /**
     * 打印设置
     * 针对拿货小标签,从预留字段解析：reserve1
     * 注:不存库
     */
    @JSONField(name = "paperSheet", serialzeFeatures = {SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty})
    private String paperSheet;

    /**
     * @return id 自增ID
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id 自增ID
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return enableStatus 逻辑删除
     */
    public Boolean getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 逻辑删除
     */
    public void setEnableStatus(Boolean enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return exshowId 模板ID
     */
    public Integer getExshowId() {
        return exshowId;
    }

    /**
     * @param exshowId 模板ID
     */
    public void setExshowId(Integer exshowId) {
        this.exshowId = exshowId;
    }

    /**
     * @return leftRight 左右偏移量
     */
    public Integer getLeftRight() {
        return leftRight;
    }

    /**
     * @param leftRight 左右偏移量
     */
    public void setLeftRight(Integer leftRight) {
        this.leftRight = leftRight;
    }

    /**
     * @return upDown 上下偏移量
     */
    public Integer getUpDown() {
        return upDown;
    }

    /**
     * @param upDown 上下偏移量
     */
    public void setUpDown(Integer upDown) {
        this.upDown = upDown;
    }

    /**
     * @return width 纸张宽
     */
    public String getWidth() {
        return width;
    }

    /**
     * @param width 纸张宽
     */
    public void setWidth(String width) {
        this.width = width;
    }

    /**
     * @return height 纸张高
     */
    public String getHeight() {
        return height;
    }

    /**
     * @param height 纸张高
     */
    public void setHeight(String height) {
        this.height = height;
    }

    /**
     * @return direction 打印方向：0:打印机默认;1:横向;2:纵向;
     */
    public Integer getDirection() {
        return direction;
    }

    /**
     * @param direction 打印方向：0:打印机默认;1:横向;2:纵向;
     */
    public void setDirection(Integer direction) {
        this.direction = direction;
    }

    /**
     * @return border 打印边界:0:实际边界值;1:打印机可打印区域
     */
    public Integer getBorder() {
        return border;
    }

    /**
     * @param border 打印边界:0:实际边界值;1:打印机可打印区域
     */
    public void setBorder(Integer border) {
        this.border = border;
    }

    /**
     * @return qsLogo 是否需要打印签收联logo:0:不需要;1:需要
     */
    public Integer getQsLogo() {
        return qsLogo;
    }

    /**
     * @param qsLogo 是否需要打印签收联logo:0:不需要;1:需要
     */
    public void setQsLogo(Integer qsLogo) {
        this.qsLogo = qsLogo;
    }

    /**
     * @return lcLogo 是否需要打印留存联logo:0:不需要;1:需要
     */
    public Integer getLcLogo() {
        return lcLogo;
    }

    /**
     * @param lcLogo 是否需要打印留存联logo:0:不需要;1:需要
     */
    public void setLcLogo(Integer lcLogo) {
        this.lcLogo = lcLogo;
    }

    /**
     * @return reserve1 预留字段1
     */
    public String getReserve1() {
        return reserve1;
    }

    /**
     * @param reserve1 预留字段1
     */
    public void setReserve1(String reserve1) {
        this.reserve1 = reserve1;
    }

    /**
     * @return reserve2 预留字段2
     */
    public String getReserve2() {
        return reserve2;
    }

    /**
     * @param reserve2 预留字段2
     */
    public void setReserve2(String reserve2) {
        this.reserve2 = reserve2;
    }

    /**
     * @return reserve3 预留字段3
     */
    public String getReserve3() {
        return reserve3;
    }

    /**
     * @param reserve3 预留字段3
     */
    public void setReserve3(String reserve3) {
        this.reserve3 = reserve3;
    }

    /**
     * @return reserve4 预留字段4
     */
    public String getReserve4() {
        return reserve4;
    }

    /**
     * @param reserve4 预留字段4
     */
    public void setReserve4(String reserve4) {
        this.reserve4 = reserve4;
    }

    /**
     * @return defaultPrinter 默认打印机
     */
    public String getDefaultPrinter() {
        return defaultPrinter;
    }

    public void setDefaultPrinter(String defaultPrinter) {
        this.defaultPrinter = defaultPrinter;
    }

    public Integer getIsConcatFhd() {
        return isConcatFhd;
    }

    public void setIsConcatFhd(Integer isConcatFhd) {
        this.isConcatFhd = isConcatFhd;
    }

    /**
     * 用户ID
     *
     * @return
     */
    public Long getTaobaoId() {
        return taobaoId;
    }

    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    public String getPaperSheet() {
        return paperSheet;
    }

    public void setPaperSheet(String paperSheet) {
        this.paperSheet = paperSheet;
    }

}