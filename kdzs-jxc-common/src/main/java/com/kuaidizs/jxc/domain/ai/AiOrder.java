package com.kuaidizs.jxc.domain.ai;

import com.kuaidizs.jxc.common.util.StringUtil;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class AiOrder extends AiBaseDomain {
    /**
     * 主订单号
     */
    private String tid;
    /**
     * 子订单号
     */
    private String oid;
    /**
     * 子订单状态
     */
    private String status;
    /**
     * 退款状态
     */
    private String refundStatus;
    /**
     * 商品ID
     */
    private String itemId;
    /**
     * 商品名称
     */
    private String itemName;
    /**
     * 商品商家编码
     */
    private String itemOuterId;
    /**
     * 商品图片
     */
    private String itemPicUrl;
    /**
     * 规格Id
     */
    private String skuId;
    /**
     * 规格名称
     */
    private String skuName;
    /**
     * 规格图片
     */
    private String skuPicUrl;
    /**
     * 商品数量
     */
    private Integer num;
    /**
     * 商品实付金额
     */
    private BigDecimal payment;
    /**
     * 数据版本
     */
    private Long version;

    public void initFieldsBeforeInsert() {
        //一些不是很重要的字段，按数据库分配的长度截断一下
        this.itemName = StringUtil.frontSubstring(this.itemName, 256);
        this.skuName = StringUtil.frontSubstring(this.skuName, 256);
    }
}
