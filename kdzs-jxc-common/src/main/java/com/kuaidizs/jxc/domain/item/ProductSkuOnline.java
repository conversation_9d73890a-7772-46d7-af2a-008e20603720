package com.kuaidizs.jxc.domain.item;

import java.util.*;
import java.math.BigDecimal;
import java.io.Serializable;

/**
 * <AUTHOR> (<EMAIL>)
 * @date 2016-07-22
 */
public class ProductSkuOnline implements Serializable {

    /**
     * 序列化ID
     */
    private static final long serialVersionUID = 1L;

    /**
     * soid
     */
    private Long soid;
    /**
     * 所属用户id
     */
    private Long taobaoId;
    /**
     * 平台商品id
     */
    private Long poid;
    /**
     * 平台商品的skuId
     */
    private String skuId;
    /**
     * sku商家编码
     */
    private String outerId;
    /**
     * 商品的规格简称（自定义规格名称）
     */
    private String shortTitle;
    /**
     * sku的销售属性组合字符串（颜色，大小，等等，可通过类目API获取某类目下的销售属性）,格式是p1:v1;p2:v2
     */
    private String properties;
    /**
     * sku所对应的销售属性的中文名字串，格式如：pid1:vid1:pid_name1:vid_name1;pid2:vid2:pid_name2:vid_name2……
     */
    private String propertiesName;
    /**
     * 平台商品的单价
     */
    private BigDecimal price;
    /**
     * sku条形码(没有规格的产品此项存产品的条形码)
     */
    private String barcode;
    /**
     * 是否是默认的sku1是，0否
     */
    private Integer isDef;
    /**
     * 当前状态(1:启用，0:删除)
     */
    private Boolean enableStatus;
    /**
     * sku状态。 normal:正常 ；delete:删除
     */
    private String status;
    /**
     * 平台商品的原始id
     */
    private String numIid;
    /**
     * 创建时间
     */
    private Date created;
    /**
     * 修改时间
     */
    private Date modified;
    /**
     * 创建人
     */
    private Long createBy;
    /**
     * 修改人
     */
    private Long updateBy;

    /**
     * 平台商品sku关联的库商品sku
     */
    private ProductSkuBase productSkuBase;

    /**
     * 平台sku与库sku关联的id
     */
    private Long id;
    /**
     * 分表标识
     */
    private String tableName;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public ProductSkuBase getProductSkuBase() {
        return productSkuBase;
    }

    public void setProductSkuBase(ProductSkuBase productSkuBase) {
        this.productSkuBase = productSkuBase;
    }

    /**
     * @return soid soid
     */
    public Long getSoid() {
        return soid;
    }

    /**
     * @param soid soid
     */
    public void setSoid(Long soid) {
        this.soid = soid;
    }

    /**
     * @return taobaoId 所属用户id
     */
    public Long getTaobaoId() {
        return taobaoId;
    }

    /**
     * @param taobaoId 所属用户id
     */
    public void setTaobaoId(Long taobaoId) {
        this.taobaoId = taobaoId;
    }

    /**
     * @return poid 平台商品id
     */
    public Long getPoid() {
        return poid;
    }

    /**
     * @param poid 平台商品id
     */
    public void setPoid(Long poid) {
        this.poid = poid;
    }

    /**
     * @return skuId 平台商品的skuId
     */
    public String getSkuId() {
        return skuId;
    }

    /**
     * @param skuId 平台商品的skuId
     */
    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    /**
     * @return outerId sku商家编码
     */
    public String getOuterId() {
        return outerId;
    }

    /**
     * @param outerId sku商家编码
     */
    public void setOuterId(String outerId) {
        this.outerId = outerId;
    }

    /**
     * @return shortTitle 商品的规格简称（自定义规格名称）
     */
    public String getShortTitle() {
        return shortTitle;
    }

    /**
     * @param shortTitle 商品的规格简称（自定义规格名称）
     */
    public void setShortTitle(String shortTitle) {
        this.shortTitle = shortTitle;
    }

    /**
     * @return properties sku的销售属性组合字符串（颜色，大小，等等，可通过类目API获取某类目下的销售属性）,格式是p1:v1;p2:v2
     */
    public String getProperties() {
        return properties;
    }

    /**
     * @param properties sku的销售属性组合字符串（颜色，大小，等等，可通过类目API获取某类目下的销售属性）,格式是p1:v1;p2:v2
     */
    public void setProperties(String properties) {
        this.properties = properties;
    }

    /**
     * @return propertiesName sku所对应的销售属性的中文名字串，格式如：pid1:vid1:pid_name1:vid_name1;pid2:vid2:pid_name2:vid_name2……
     */
    public String getPropertiesName() {
        return propertiesName;
    }

    /**
     * @param propertiesName sku所对应的销售属性的中文名字串，格式如：pid1:vid1:pid_name1:vid_name1;pid2:vid2:pid_name2:vid_name2……
     */
    public void setPropertiesName(String propertiesName) {
        this.propertiesName = propertiesName;
    }

    /**
     * @return price 平台商品的单价
     */
    public BigDecimal getPrice() {
        return price;
    }

    /**
     * @param price 平台商品的单价
     */
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    /**
     * @return barcode sku条形码(没有规格的产品此项存产品的条形码)
     */
    public String getBarcode() {
        return barcode;
    }

    /**
     * @param barcode sku条形码(没有规格的产品此项存产品的条形码)
     */
    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    /**
     * @return isDef 是否是默认的sku1是，0否
     */
    public Integer getIsDef() {
        return isDef;
    }

    /**
     * @param isDef 是否是默认的sku1是，0否
     */
    public void setIsDef(Integer isDef) {
        this.isDef = isDef;
    }

    /**
     * @return enableStatus 当前状态(1:启用，0:删除)
     */
    public Boolean getEnableStatus() {
        return enableStatus;
    }

    /**
     * @param enableStatus 当前状态(1:启用，0:删除)
     */
    public void setEnableStatus(Boolean enableStatus) {
        this.enableStatus = enableStatus;
    }

    /**
     * @return status sku状态。 normal:正常 ；delete:删除
     */
    public String getStatus() {
        return status;
    }

    /**
     * @param status sku状态。 normal:正常 ；delete:删除
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * @return numIid 平台商品的原始id
     */
    public String getNumIid() {
        return numIid;
    }

    /**
     * @param numIid 平台商品的原始id
     */
    public void setNumIid(String numIid) {
        this.numIid = numIid;
    }

    /**
     * @return created 创建时间
     */
    public Date getCreated() {
        return created;
    }

    /**
     * @param created 创建时间
     */
    public void setCreated(Date created) {
        this.created = created;
    }

    /**
     * @return modified 修改时间
     */
    public Date getModified() {
        return modified;
    }

    /**
     * @param modified 修改时间
     */
    public void setModified(Date modified) {
        this.modified = modified;
    }

    /**
     * @return createBy 创建人
     */
    public Long getCreateBy() {
        return createBy;
    }

    /**
     * @param createBy 创建人
     */
    public void setCreateBy(Long createBy) {
        this.createBy = createBy;
    }

    /**
     * @return updateBy 修改人
     */
    public Long getUpdateBy() {
        return updateBy;
    }

    /**
     * @param updateBy 修改人
     */
    public void setUpdateBy(Long updateBy) {
        this.updateBy = updateBy;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }
}