package com.kuaidizs.jxc.api.erp;

import com.alibaba.fastjson.JSONObject;
import com.kuaidizs.jxc.api.erp.bean.ErpShopInfo;
import com.kuaidizs.jxc.common.util.HttpWebUtils;
import com.kuaidizs.jxc.common.util.Md5Util;
import com.raycloud.bizlogger.Logger;
import org.apache.commons.lang.StringUtils;

import java.util.HashMap;
import java.util.Map;

public class ErpApi {

    private static final Logger logger = Logger.getLogger(ErpApi.class);

    private static final String DOMIAN = "erpg.kuaidizs.cn";

    private static final String KEY = "kdzserp";

    private static final String GET_USER_ID_API = "/index/share/getUserInfo";

    private static final String GET_SHOP_INFO_API = "/index/share/getShopInfo";

    private static final String WAYBILL_SEARCH = "/print/center/erp/waybillSearch";

    /**
     * 获取erpUserId
     */
    public static String getUserId(String mobile, String domian) throws Exception {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("mobile", mobile);
        try {
            String userId = doPostJson(dataMap, domian, GET_USER_ID_API, jsonObject -> {
                JSONObject data = jsonObject.getJSONObject("data");
                return data.getString("userId");
            });
            if (StringUtils.isBlank(userId)) {
                throw new Exception("erp getUserId error");
            }
            return userId;
        } catch (Exception e) {
            logger.error("erp getUserId error", e);
            throw e;
        }
    }

    /**
     * 获取店铺信息
     */
    public static ErpShopInfo getShopInfo(String mobile, String shopName, String platform, String domian) throws Exception {
        Map<String, Object> dataMap = new HashMap<>();
        dataMap.put("mobile", mobile);
        dataMap.put("sellerName", shopName);
        dataMap.put("platform", platform);
        try {
            ErpShopInfo erpShopInfo = doPostJson(dataMap, domian, GET_SHOP_INFO_API,
                    jsonObject -> jsonObject.getObject("data", ErpShopInfo.class));
            return erpShopInfo;
        } catch (Exception e) {
            logger.error("erp getShopInfo error", e);
            throw e;
        }
    }

    public static interface ResultParser<T> {
        T parse(JSONObject jsonObject);
    }

    private static <T>T doPostJson(Map<String, Object> dataMap, String domian, String api, ResultParser<T> resultParser) throws Exception {
        Map<String, String> headerMap = buildHeaderMap(api);
        if (StringUtils.isBlank(domian)) {
            domian = DOMIAN;
        }
        String url = "https://" + domian + api;
        try {
            String resulJsonStr =  HttpWebUtils.doPostJson(url, dataMap, headerMap);
            JSONObject jsonObject = JSONObject.parseObject(resulJsonStr);
            Boolean success = jsonObject.getBoolean("success");
            if (!success) {
                String errorMessage = jsonObject.getString("errorMessage");
                throw new Exception(errorMessage);
            }

            T data = resultParser.parse(jsonObject);
            if (data == null) {
                throw new Exception("erp api error, api:" + api + ",res:" + resulJsonStr);
            }
            return data;
        } catch (Exception e) {
            logger.error("erp api error, api:" + api, e);
            throw e;
        }
    }


    private static Map<String, String> buildHeaderMap(String api) {
        long timestamp = System.currentTimeMillis();
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("apiName", api);
        headerMap.put("sign", Md5Util.md5(timestamp + api + KEY));
        headerMap.put("timestamp", timestamp + "");
        return headerMap;
    }



}
