package com.raycloud.jxc.shoprelate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kuaidizs.erp.service.jxc.IJxcUserDubboService;
import com.kuaidizs.jxc.common.util.*;
import com.kuaidizs.jxc.domain.User;
import com.kuaidizs.jxc.domain.sms.SmsConnectionShop;
import com.kuaidizs.jxc.domain.sms.SmsDisConnectionShop;
import com.kuaidizs.jxc.domain.twcn.TwcnUserReal;
import com.kuaidizs.jxc.domain.user.UserGroupConnection;
import com.kuaidizs.jxc.domain.user.UserGroupYd;
import com.kuaidizs.jxc.log.LogType;
import com.kuaidizs.jxc.query.twcn.TwcnUserRealQuery;
import com.kuaidizs.jxc.query.user.UserGroupYdQuery;
import com.kuaidizs.jxc.service.UserService;
import com.kuaidizs.jxc.service.twcn.TwcnUserRealService;
import com.kuaidizs.jxc.service.user.UserGroupConnectionService;
import com.kuaidizs.jxc.service.user.UserGroupYdService;
import com.kuaidizs.jxc.service.user.UserRelationService;
import com.raycloud.bizlogger.Logger;
import com.raycloud.utils.http.WebUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;

/**
 * 店铺关联
 * Created by meiweifeng on 16/10/14.
 */
@Service
public class ShopRelateBiz {

    @Autowired
    UserService userService;
    @Autowired
    UserGroupConnectionService userGroupConnectionService;
    @Autowired
    BizLogOnsDao bizLogOnsDao;
    @Autowired
    IJxcUserDubboService jxcUserDubboService;
    @Autowired
    UserGroupYdService userGroupYdService;
    @Resource
    TwcnUserRealService twcnUserRealService;

    @Resource
    private UserRelationService userRelationService;

    private final Logger logger = Logger.getLogger(ShopRelateBiz.class);


    /**
     * 关联店铺
     *
     * @param user     登录user
     * @param groupId  可见组的taobaoId,如果都不可见，那设置自己taobaoId 到user的groupId
     * @param linkUser 关联user
     * @throws Exception e
     */
    @Transactional
    public User shopRelate(User user, String groupId, User linkUser, String isCanSee) throws Exception {
        List<UserGroupConnection> userGroups = userGroupConnectionService.getUserGroupConnectionByGroupId(Long.valueOf(groupId), user.getTaobaoId());
        String params = "taobaoId:" + user.getTaobaoId() + ",groupId:" + groupId + ",linkUser:" + linkUser.getTaobaoId() + ",isCanSee:" + isCanSee;
        String groupNick = "";
        if (CollectionUtils.isEmpty(userGroups)) {
            //验证taobaoId有效
            if (!groupId.equals(linkUser.getTaobaoId() + "") && !groupId.equals(user.getTaobaoId() + "")) {
                throw new Exception("设置管理员信息不正确");
            }

            //一对一关联
            UserGroupConnection userGroup = new UserGroupConnection();
            userGroup.setCreated(new Date());
            userGroup.setModified(new Date());
            userGroup.setTaobaoId(user.getTaobaoId());
            userGroup.setGroupId(Long.valueOf(groupId));
            userGroup.setIsVerification(1);
            userGroup.setIsAuthorization(1);
            if (groupId.equals(user.getTaobaoId() + "")) {
                userGroup.setPowerType(1);
            } else {
                userGroup.setPowerType(0);
            }

            userGroup.setIsCancel(0);
            userGroup.setIsInit(0);
            userGroup.setTaobaoNick(user.getTaobaoNick());
            //保存主店铺
            userGroupConnectionService.addUserGroupConnection(userGroup);
            bizLogOnsDao.produceUserGroup(user, userGroup, LogType.SHOP_RELATE_SUCCESS, params);
        }
        for (UserGroupConnection ugc : userGroups) {
            if (Long.valueOf(groupId).longValue() == ugc.getTaobaoId().longValue()) {
                groupNick = ugc.getTaobaoNick();
                break;
            }
        }
        if (StringUtils.isEmpty(groupNick)) {
            if (Long.valueOf(groupId).longValue() == user.getTaobaoId().longValue()) {
                groupNick = user.getTaobaoNick();
            }
        }
        if (StringUtils.isEmpty(groupNick)) {
            if (Long.valueOf(groupId).longValue() == linkUser.getTaobaoId().longValue()) {
                groupNick = linkUser.getTaobaoNick();
            }
        }
        if (StringUtils.isEmpty(groupNick)) {
            User u = userService.getBaseUserByTaobaoId(Long.valueOf(groupId));
            groupNick = u.getTaobaoNick();
        }
        //多对一关联
        UserGroupConnection userGroup = new UserGroupConnection();
        userGroup.setCreated(new Date());
        userGroup.setModified(new Date());
        userGroup.setTaobaoId(linkUser.getTaobaoId());
        userGroup.setGroupId(Long.valueOf(groupId));
        userGroup.setIsVerification(1);
        userGroup.setIsAuthorization(1);
        if (Long.valueOf(groupId).longValue() == linkUser.getTaobaoId().longValue()) {
            userGroup.setPowerType(1);
        } else {
            userGroup.setPowerType(0);
        }
        userGroup.setIsCancel(0);
        if (StringUtils.isNotBlank(isCanSee) && ("0".equals(isCanSee) || "false".equals(isCanSee))) {
            userGroup.setIsCancel(1);
        }
        userGroup.setIsInit(0);
        userGroup.setTaobaoNick(linkUser.getTaobaoNick());
        try {
            if (Long.valueOf(groupId).longValue() == linkUser.getTaobaoId().longValue()) {
                jxcUserDubboService.addJxcUser(user.getTaobaoId(), user.getTaobaoNick(), Long.valueOf(groupId), groupNick);
            } else {
                jxcUserDubboService.addJxcUser(linkUser.getTaobaoId(), linkUser.getTaobaoNick(), Long.valueOf(groupId), groupNick);
            }
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(user, e, "关联店铺调用进销存异常,msg:").append(e.getMessage()).toString(), e);
            throw new RuntimeException("关联进销存用户数据异常，错误信息:" + e.getMessage());
        }
        userGroupConnectionService.addUserGroupConnection(userGroup);
        bizLogOnsDao.produceUserGroup(user, userGroup, LogType.SHOP_RELATE_SUCCESS, params);

        //关联成功后调用短信模块注册
        setGroupInfoToCrm(user, linkUser);

        setGroupInfoToWd(groupId, linkUser);

        return user;
    }

    /**
     * 同步绑定信息到网点接口
     *
     * @param groupId  关联组
     * @param linkUser 绑定用户
     */
    private void setGroupInfoToWd(String groupId, User linkUser) {
        //判断用户是否设置了关联信息
        UserGroupYd userGroupYd = userGroupYdService.getUserGroupYd(Long.parseLong(groupId), Long.parseLong(groupId));
        if (userGroupYd == null) {
            return;
        }
        try {
            String url = "http://cw.wdzs.com/consumeUser/syncGroup";
            Map<String, String> props = new HashMap<>();
            props.put("taobaoId", linkUser.getTaobaoId() + "");
            props.put("taobaoNick", linkUser.getTaobaoNick());
            props.put("groupId", groupId);
            props.put("pwd", "chou-bi!!##xiaoXiong");
            String result = WebUtils.doPost(url, props, 5000, 1000);
            logger.biz(LogHelper.buildLogHead(linkUser).append("请求网点参数:")
                    .append(JSON.toJSONString(props)).append(">>>result:").append(result).toString());
        } catch (IOException e) {
            logger.error("关联店铺请求网点参数异常, taobaoId:" + linkUser.getTaobaoId(), e);
        }
    }

    /**
     * 设置用户关联信息到CRM系统
     *
     * @param user     操作用户
     * @param linkUser 关联用户
     */
    private void setGroupInfoToCrm(User user, User linkUser) {
        String devFlag = System.getProperty("dev");
        if (StringUtils.isNotEmpty(devFlag) && "true".equals(devFlag)) {
            return;
        }

        try {
            SmsConnectionShop smsConnectionShop = new SmsConnectionShop();
            smsConnectionShop.setAdminTaobaoNick(user.getTaobaoNick());
            smsConnectionShop.setConnectTaobaoNick(linkUser.getTaobaoNick());

            String url = "http://kdsms.kuaidizs.cn/api/userconnection/connect_raycloud";
            Map<String, String> props = new HashMap<>();
            String sign = SmsSignUtil.getSign((JSONObject) JSON.toJSON(smsConnectionShop));
            Map<String, Object> values = new HashMap<String, Object>();
            values.put("connectionShop", smsConnectionShop);
            values.put("sign", sign);

            String info = DesUtil.encrypt(JSON.toJSONString(values), Constant.CRM_DES_KEY);
            props.put("info", info);

            logger.info(LogHelper.buildLogHead(user).append("请求sms参数:")
                    .append(JSON.toJSONString(props)).toString());

            String accessToken = WebUtils.doPost(url, props, 30000, 30000);

            JSONObject accessTokenJoson = JSONObject.parseObject(accessToken);
            Integer resultData = (Integer) accessTokenJoson.get("result");
            if (resultData != null && resultData == 100) {
                logger.info(LogHelper.buildLogHead(user).append("短信系统发送关联信息成功").toString());
            } else {
                logger.error(LogHelper.buildLogHead(user).append("setGroupInfoToCrm exception,accessToken:")
                        .append(accessToken).toString());
            }
        } catch (Exception e) {
            logger.error(user.getTaobaoNick() + " 通知crm用户关联异常，msg:" + e.getMessage(), e);
        }
    }

    /**
     * 设置用户取消关联信息到CRM系统
     *
     * @param adminTaobaoId      操作用户
     * @param disconnectTaobaoId 关联用户
     * @param groupId            关联ID
     */
    private void setDisGroupInfoToCrm(Long adminTaobaoId, Long disconnectTaobaoId, Long groupId) {
        String devFlag = System.getProperty("dev");
        if (StringUtils.isNotEmpty(devFlag) && "true".equals(devFlag)) {
            return;
        }
        try {
            SmsDisConnectionShop disConnectionShop = new SmsDisConnectionShop();
            disConnectionShop.setAdminTaobaoId(adminTaobaoId);
            disConnectionShop.setDisconnectTaobaoId(disconnectTaobaoId);
            disConnectionShop.setGroupId(groupId);

            String url = "http://kdsms.kuaidizs.cn/api/userconnection/disconnect_raycloud";
            Map<String, String> props = new HashMap<String, String>();
            String sign = SmsSignUtil.getSign((JSONObject) JSON.toJSON(disConnectionShop));
            Map<String, Object> values = new HashMap<String, Object>();
            values.put("disConnectionShop", disConnectionShop);
            values.put("sign", sign);

            String info = DesUtil.encrypt(JSON.toJSONString(values), Constant.CRM_DES_KEY);
            props.put("info", info);


            String accessToken = WebUtils.doPost(url, props, 30000, 30000);

            JSONObject accessTokenJoson = JSONObject.parseObject(accessToken);
            Integer resultData = (Integer) accessTokenJoson.get("result");
            if (resultData != null && resultData == 100) {
                logger.info(adminTaobaoId + "]" + "短信系统发送取消关联信息成功");
            } else {
                logger.error("[" + adminTaobaoId + "]" + " setDisGroupInfoToCrm exception,accessToken:" + accessToken);
                throw new RuntimeException("移除店铺调用短信系统异常，请稍后重试或联系客服处理！");
            }
        } catch (Exception e) {
            logger.error("[" + adminTaobaoId + "]" + "通知crm用户取消关联异常，msg:" + e.getMessage(), e);
            throw new RuntimeException("移除店铺调用短信系统异常，请稍后重试或联系客服处理!msg:" + e.getMessage());
        }
    }

    /**
     * 移除店铺
     *
     * @param user     登录user
     * @param taobaoId 移除的taobaoId
     * @throws Exception e
     */
    @Transactional
    public User shopRemoveRelate(User user, String taobaoId, Long groupId) throws Exception {
        String params = "user taobaoId:" + user.getTaobaoId() + ",taobaoId:" + taobaoId + ",groupId:" + groupId;
        List<UserGroupConnection> userGroups = userGroupConnectionService.getUserGroupConnectionByGroupId(groupId, null);
        Set<Long> taobaoIds = new HashSet<>();
        if (CollectionUtils.isNotEmpty(userGroups)) {
            for (UserGroupConnection g : userGroups) {
                taobaoIds.add(g.getTaobaoId());
            }
            //如果当前只有两个店铺，都删除
            if (userGroups.size() == 2) {
                //循环分开，防止取消后关联关系无法查询到
                for (UserGroupConnection g : userGroups) {
                    if (taobaoId.equals(g.getTaobaoId() + "")) {
                        //取消关联前先调用crm接口，通知crm。对于两两关联的，只发送一次
                        setDisGroupInfoToCrm(user.getTaobaoId(), g.getTaobaoId(), groupId);
                    }
                }
                for (UserGroupConnection g : userGroups) {
                    userGroupConnectionService.deleteByKey(g);
                    bizLogOnsDao.produceUserGroup(user, g, LogType.SHOP_UN_RELATE_SUCCESS, params);
                    //删除共享单号关联组，表：user_group_yd
                    this.deleteUserGroupYd(g.getTaobaoId());
                }
            } else {
                //只删除自己
                for (UserGroupConnection g : userGroups) {
                    if (taobaoId.equals(g.getTaobaoId() + "")) {
                        //取消关联前先调用crm接口，通知crm
                        setDisGroupInfoToCrm(user.getTaobaoId(), g.getTaobaoId(), groupId);
                        userGroupConnectionService.deleteByKey(g);
                        bizLogOnsDao.produceUserGroup(user, g, LogType.SHOP_UN_RELATE_SUCCESS, params);
                        //删除共享单号关联组，表：user_group_yd
                        this.deleteUserGroupYd(g.getTaobaoId());
                    }
                }
            }
        }

        // 移除关联关系
        removeUserRelation(user, taobaoIds, Long.valueOf(taobaoId));

        try {
            jxcUserDubboService.deleteJxcShop(Long.parseLong(taobaoId), groupId, user.getTaobaoNick());
        } catch (Exception e) {
            logger.error(LogHelper.buildErrorLog(user, e, "移除店铺调用进销存异常,msg:").append(e.getMessage()).toString(), e);
            throw new RuntimeException("移除店铺调用进销存用户数据异常，错误信息:" + e.getMessage());
        }
        return user;
    }

    /**
     * 移除关联关系
     *
     * <AUTHOR>
     * @date 2023/6/13 2:03 下午
     * @param user
     * @param taobaoIds
     * @param taobaoId
     * @return void
     */
    private void removeUserRelation(User user, Set<Long> taobaoIds, Long taobaoId) {
        if (CollectionUtils.isEmpty(taobaoIds) || !taobaoIds.contains(taobaoId)) {
            return;
        }
        for (Long tbId : taobaoIds) {
            if (Objects.equals(tbId, taobaoId)) {
                continue;
            }
            userRelationService.removeUserRelation(user, taobaoId, tbId);
            userRelationService.removeUserRelation(user, tbId, taobaoId);
        }
    }


    /**
     * 删除共享单号用户，解绑淘外菜鸟用户
     *
     * @param taobaoId taobaoId
     */
    public void deleteUserGroupYd(Long taobaoId) {
        //删除淘外菜鸟关联关系
        User newUser = userService.getBaseUserByTaobaoId(taobaoId);
        UserGroupYdQuery userGroupYdQuery = new UserGroupYdQuery();
        userGroupYdQuery.setTaobaoId(taobaoId);
        List<UserGroupYd> userGroupYdList = userGroupYdService.getUserGroupYdList(userGroupYdQuery);
        if (CollectionUtils.isNotEmpty(userGroupYdList)) {
            for (UserGroupYd item : userGroupYdList) {
                userGroupYdService.deleteByKey(null, item.getId());
                userGroupYdService.deleteCache(taobaoId, null);
                userGroupYdService.deleteCache(taobaoId, item.getGroupId());
                TwcnUserRealQuery twcnUserRealQuery = new TwcnUserRealQuery();
                twcnUserRealQuery.setTaobaoId(taobaoId);
                twcnUserRealQuery.setTwTaobaoId(item.getGroupId() + "");
                twcnUserRealQuery.setEnableStatus(1);
                twcnUserRealQuery.setState(1);
                List<TwcnUserReal> twcnUserRealList = twcnUserRealService.getTwcnUserRealList(newUser, twcnUserRealQuery);
                if (CollectionUtils.isNotEmpty(twcnUserRealList)) {
                    for (TwcnUserReal twcnUserReal : twcnUserRealList) {
                        if (twcnUserReal.getId() != null) {
                            TwcnUserReal update = new TwcnUserReal();
                            update.setId(twcnUserReal.getId());
                            update.setState(0);
                            twcnUserRealService.updateTwcnUserRealByKey(newUser, update);
                        }
                    }
                }
            }
        }


    }
}
