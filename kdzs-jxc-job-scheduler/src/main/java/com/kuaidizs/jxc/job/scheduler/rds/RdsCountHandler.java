package com.kuaidizs.jxc.job.scheduler.rds;

import cn.hutool.core.date.DateUtil;
import com.kuaidizs.jxc.common.util.Constant;
import com.kuaidizs.jxc.common.util.dingTalk.SendDingTalkClient;
import com.kuaidizs.spymemcached.extend.listener.RayMemcacheClientUtil;
import com.raycloud.bizlogger.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Component
@EnableScheduling
public class RdsCountHandler {


    @Autowired
    private RayMemcacheClientUtil rayMemcacheClientUtil;

    private static final Logger logger = Logger.getLogger(RdsCountHandler.class);

    @Scheduled(cron = "0 15 18 * * ?")
    public void execute() throws Exception {

        String today = DateUtil.formatDate(DateUtil.offsetDay(new Date(), -1));
        Object rdsStatusError = rayMemcacheClientUtil.get("12158997_RDSCOUNT_" + today + "_rdsStatusError");
        Object userBuyerNick = rayMemcacheClientUtil.get("12158997_RDSCOUNT_" + today + "_userBuyerNick");
        Object canRds = rayMemcacheClientUtil.get("12158997_RDSCOUNT_" + today + "_canRds");
        Object canNotRds = rayMemcacheClientUtil.get("12158997_RDSCOUNT_" + today + "_canNotRds");
        Object userAccessTokenNull = rayMemcacheClientUtil.get("12158997_RDSCOUNT_" + today + "_userAccessTokenNull");
        Object other = rayMemcacheClientUtil.get("12158997_RDSCOUNT_" + today + "_other");
        Object isTrialUser = rayMemcacheClientUtil.get("12158997_RDSCOUNT_" + today + "_isTrialUser");
        Object api_invoke = rayMemcacheClientUtil.get("12158997RDSCOUNT_" + today + "_api_invoke");
        // Object rds_invoke = rayMemcacheClientUtil.get("12158997RDSCOUNT_" + today + "_rds_invoke");
        Object queryCount = rayMemcacheClientUtil.get("12158997_RDSCOUNT_" + today + "_queryCount");
        Object queryEndBefore30 = rayMemcacheClientUtil.get("12158997_RDSCOUNT_" + today + "_queryEndBefore30");
        Object queryTwo = rayMemcacheClientUtil.get("12158997_RDSCOUNT_" + today + "_queryTwo");

        StringBuilder sb = new StringBuilder("(考勤)p1-p7,rds数据统计：\n");
        sb.append("走rds查询次数：").append((Integer) canRds).append("\n")
                .append("不走rds查询次数：").append((Integer) canNotRds).append("\n")
                .append("不走rds原因：").append("使用收件人查询：").append((Integer) userBuyerNick).append(",userAccess为空：").
                append(userAccessTokenNull).append(",是试用用户：").append((Integer) isTrialUser).append(",rds状态异常：").append((Integer) rdsStatusError).append(",其他原因：").append((Integer) other).append("\n")
                .append("判断是否后异步统计单量：").append((Integer) queryCount).append("\n")
                .append("开始时间大于30天前查询次数：" + (Integer) queryTwo).append("\n")
                .append("开始结束时间都大于30天：" + (Integer) queryEndBefore30).append("\n")
                .append("api调用量：").append((Integer) api_invoke).append("\n");
                //.append("rds调用量：").append((Integer) rds_invoke).append("\n");

        logger.info(sb.toString());
        SendDingTalkClient.sendDingTalkClient("https://oapi.dingtalk.com/robot/send?access_token=15141d2e792a3f8b43ffee14f739eba800176da677b19d5d5b13598bb4b268da", sb.toString());

    }
}
