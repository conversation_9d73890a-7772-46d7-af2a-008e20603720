package com.kuaidizs.jxc.job.scheduler.rds;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.kuaidizs.jxc.common.util.dingTalk.SendDingTalkClient;
import com.kuaidizs.spymemcached.extend.listener.RayMemcacheClientUtil;
import com.raycloud.bizlogger.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Component
@JobHandler("soldGetAPiCountDayHandler")
public class SoldGetAPiCountDayHandler extends IJobHandler {


    @Autowired
    private RayMemcacheClientUtil rayMemcacheClientUtil;

    private static final Logger logger = Logger.getLogger(SoldGetAPiCountDayHandler.class);


    /**
     * useBuyerOpenUid
     * expireTimeNull
     * expireTime
     * masterLogin10M
     * userBuyerNick
     * topExpired
     * newOrder
     * rdsStatusError
     * isTrialUser
     * domainNoRds
     * rdsInBlackList
     * getTradeAPIByStatus
     * StockChangeOnsaleTypeService_getTradeAPIByStatus
     * StockService_getTradeAPIByStatus
     * queryTaobaoTradeWithSoldGetCount
     * taobaoAPI_queryCount
     * ReturnChangeOrderService_getTradeList
     * queryTaobaoTradeWithSoldGetWithPage_exception
     * before30
     * other
     * Constant.APP_KEY + "_" + "APICOUNT_" + DateUtil.today() + "_" + key;
     *
     * @param today
     * @return
     * @throws Exception
     */

    @Override
    public ReturnT<String> execute(String today) throws Exception {


        String lastDay = DateUtil.formatDate(DateUtil.yesterday());
        if (StringUtils.isNotBlank(today)) {
            lastDay = DateUtil.formatDate(new Date());
        }

        String useBuyerOpenUid = "12158997_APICOUNT_" + DateUtil.today() + "_useBuyerOpenUid";
        String expireTimeNull = "12158997_APICOUNT_" + DateUtil.today() + "_expireTimeNull";
        String expireTime = "12158997_APICOUNT_" + DateUtil.today() + "_expireTime";
        String masterLogin10M = "12158997_APICOUNT_" + DateUtil.today() + "_masterLogin10M";
        String userBuyerNick = "12158997_APICOUNT_" + DateUtil.today() + "_userBuyerNick";
        String topExpired = "12158997_APICOUNT_" + DateUtil.today() + "_topExpired";
        String newOrder = "12158997_APICOUNT_" + DateUtil.today() + "_newOrder";
        String rdsStatusError = "12158997_APICOUNT_" + DateUtil.today() + "_rdsStatusError";
        String isTrialUser = "12158997_APICOUNT_" + DateUtil.today() + "_isTrialUser";
        String domainNoRds = "12158997_APICOUNT_" + DateUtil.today() + "_domainNoRds";
        String rdsInBlackList = "12158997_APICOUNT_" + DateUtil.today() + "_rdsInBlackList";
        String getTradeAPIByStatus = "12158997_APICOUNT_" + DateUtil.today() + "_getTradeAPIByStatus";
        String StockChangeOnsaleTypeService_getTradeAPIByStatus = "12158997_APICOUNT_" + DateUtil.today() + "_StockChangeOnsaleTypeService_getTradeAPIByStatus";
        String StockService_getTradeAPIByStatus = "12158997_APICOUNT_" + DateUtil.today() + "_StockService_getTradeAPIByStatus";
        String queryTaobaoTradeWithSoldGetCount = "12158997_APICOUNT_" + DateUtil.today() + "_queryTaobaoTradeWithSoldGetCount";
        String taobaoAPI_queryCount = "12158997_APICOUNT_" + DateUtil.today() + "_taobaoAPI_queryCount";
        String ReturnChangeOrderService_getTradeList = "12158997_APICOUNT_" + DateUtil.today() + "_ReturnChangeOrderService_getTradeList";
        String queryTaobaoTradeWithSoldGetWithPage_exception = "12158997_APICOUNT_" + DateUtil.today() + "_queryTaobaoTradeWithSoldGetWithPage_exception";
        String before30 = "12158997_APICOUNT_" + DateUtil.today() + "_before30";
        String other = "12158997_APICOUNT_" + DateUtil.today() + "_other";

        Object useBuyerOpenUidValue = rayMemcacheClientUtil.get(useBuyerOpenUid);
        Object expireTimeNullValue = rayMemcacheClientUtil.get(expireTimeNull);
        Object expireTimeValue = rayMemcacheClientUtil.get(expireTime);
        Object masterLogin10MValue = rayMemcacheClientUtil.get(masterLogin10M);
        Object userBuyerNickValue = rayMemcacheClientUtil.get(userBuyerNick);
        Object topExpiredValue = rayMemcacheClientUtil.get(topExpired);
        Object newOrderValue = rayMemcacheClientUtil.get(newOrder);
        Object rdsStatusErrorValue = rayMemcacheClientUtil.get(rdsStatusError);
        Object isTrialUserValue = rayMemcacheClientUtil.get(isTrialUser);
        Object domainNoRdsValue = rayMemcacheClientUtil.get(domainNoRds);
        Object rdsInBlackListValue = rayMemcacheClientUtil.get(rdsInBlackList);
        Object getTradeAPIByStatusValue = rayMemcacheClientUtil.get(getTradeAPIByStatus);
        Object StockChangeOnsaleTypeService_getTradeAPIByStatusValue = rayMemcacheClientUtil.get(StockChangeOnsaleTypeService_getTradeAPIByStatus);
        Object StockService_getTradeAPIByStatusValue = rayMemcacheClientUtil.get(StockService_getTradeAPIByStatus);
        Object queryTaobaoTradeWithSoldGetCountValue = rayMemcacheClientUtil.get(queryTaobaoTradeWithSoldGetCount);
        Object taobaoAPI_queryCountValue = rayMemcacheClientUtil.get(taobaoAPI_queryCount);
        Object ReturnChangeOrderService_getTradeListValue = rayMemcacheClientUtil.get(ReturnChangeOrderService_getTradeList);
        Object queryTaobaoTradeWithSoldGetWithPage_exceptionValue = rayMemcacheClientUtil.get(queryTaobaoTradeWithSoldGetWithPage_exception);
        Object before30Value = rayMemcacheClientUtil.get(before30);
        Object otherValue = rayMemcacheClientUtil.get(other);

        Integer total =
                Convert.convertQuietly(Integer.class, useBuyerOpenUidValue, 0) +
                        Convert.convertQuietly(Integer.class, expireTimeNullValue, 0) +
                        Convert.convertQuietly(Integer.class, expireTimeValue, 0) +
                        Convert.convertQuietly(Integer.class, masterLogin10MValue, 0) +
                        Convert.convertQuietly(Integer.class, userBuyerNickValue, 0) +
                        Convert.convertQuietly(Integer.class, topExpiredValue, 0) +
                        Convert.convertQuietly(Integer.class, newOrderValue, 0) +
                        Convert.convertQuietly(Integer.class, rdsStatusErrorValue, 0) +
                        Convert.convertQuietly(Integer.class, isTrialUserValue, 0) +
                        Convert.convertQuietly(Integer.class, domainNoRdsValue, 0) +
                        Convert.convertQuietly(Integer.class, rdsInBlackListValue, 0) +
                        Convert.convertQuietly(Integer.class, getTradeAPIByStatusValue, 0) +
                        Convert.convertQuietly(Integer.class, StockChangeOnsaleTypeService_getTradeAPIByStatusValue, 0) +
                        Convert.convertQuietly(Integer.class, StockService_getTradeAPIByStatusValue, 0) +
                        Convert.convertQuietly(Integer.class, queryTaobaoTradeWithSoldGetCountValue, 0) +
                        Convert.convertQuietly(Integer.class, taobaoAPI_queryCountValue, 0) +
                        Convert.convertQuietly(Integer.class, ReturnChangeOrderService_getTradeListValue, 0) +
                        Convert.convertQuietly(Integer.class, queryTaobaoTradeWithSoldGetWithPage_exceptionValue, 0) +
                        Convert.convertQuietly(Integer.class, before30Value, 0) +
                        Convert.convertQuietly(Integer.class, otherValue, 0);

        if (total==0){
            total=1;
        }
        Integer useBuyerOpenUidInt = Convert.convertQuietly(Integer.class, useBuyerOpenUidValue, 0);
        Integer expireTimeNullInt = Convert.convertQuietly(Integer.class, expireTimeNullValue, 0);
        Integer expireTimeInt = Convert.convertQuietly(Integer.class, expireTimeValue, 0);
        Integer masterLogin10MInt = Convert.convertQuietly(Integer.class, masterLogin10MValue, 0);
        Integer userBuyerNickInt = Convert.convertQuietly(Integer.class, userBuyerNickValue, 0);
        Integer topExpiredInt = Convert.convertQuietly(Integer.class, topExpiredValue, 0);
        Integer newOrderInt = Convert.convertQuietly(Integer.class, newOrderValue, 0);
        Integer rdsStatusErrorInt = Convert.convertQuietly(Integer.class, rdsStatusErrorValue, 0);
        Integer isTrialUserInt = Convert.convertQuietly(Integer.class, isTrialUserValue, 0);
        Integer domainNoRdsInt = Convert.convertQuietly(Integer.class, domainNoRdsValue, 0);
        Integer rdsInBlackListInt = Convert.convertQuietly(Integer.class, rdsInBlackListValue, 0);
        Integer getTradeAPIByStatusInt = Convert.convertQuietly(Integer.class, getTradeAPIByStatusValue, 0);
        Integer StockChangeOnsaleTypeService_getTradeAPIByStatusInt = Convert.convertQuietly(Integer.class, StockChangeOnsaleTypeService_getTradeAPIByStatusValue, 0);
        Integer StockService_getTradeAPIByStatusInt = Convert.convertQuietly(Integer.class, StockService_getTradeAPIByStatusValue, 0);
        Integer queryTaobaoTradeWithSoldGetCountInt = Convert.convertQuietly(Integer.class, queryTaobaoTradeWithSoldGetCountValue, 0);
        Integer taobaoAPI_queryCountInt = Convert.convertQuietly(Integer.class, taobaoAPI_queryCountValue, 0);
        Integer ReturnChangeOrderService_getTradeListInt = Convert.convertQuietly(Integer.class, ReturnChangeOrderService_getTradeListValue, 0);
        Integer queryTaobaoTradeWithSoldGetWithPage_exceptionInt = Convert.convertQuietly(Integer.class, queryTaobaoTradeWithSoldGetWithPage_exceptionValue, 0);
        Integer before30Int = Convert.convertQuietly(Integer.class, before30Value, 0);
        Integer otherInt = Convert.convertQuietly(Integer.class, otherValue, 0);
        StringBuilder sbTotal = new StringBuilder(DateUtil.yesterday().toDateStr());

        if (StringUtils.isBlank(today)) {
            sbTotal.append("【前一天总量统计】-列表走api：");
        } else {
            sbTotal.append("【当天总量统计】-列表走api：");
        }
        sbTotal.append("\n")
                .append("买家id查询：").append(useBuyerOpenUidInt).append(",占比：").append(useBuyerOpenUidInt * 100 / total).append("%").append("\n")
                .append("买家nick查询：").append(userBuyerNickInt).append(",占比：").append(userBuyerNickInt * 100 / total).append("%").append("\n")
                .append("30天前订单：").append(before30Int).append(",占比：").append(before30Int * 100 / total).append("%").append("\n")
                .append("expireTimeNull：").append(expireTimeNullInt).append(",占比：").append(expireTimeNullInt * 100 / total).append("%").append("\n")
                .append("expireTime：").append(expireTimeInt).append(",占比：").append(expireTimeInt * 100 / total).append("%").append("\n")
                .append("主账号10分钟内登录：").append(masterLogin10MInt).append(",占比：").append(masterLogin10MInt * 100 / total).append("%").append("\n")
                .append("授权已过期：").append(topExpiredInt).append(",占比：").append(topExpiredInt * 100 / total).append("%").append("\n")
                .append("用户新订购：").append(newOrderInt).append(",占比：").append(newOrderInt * 100 / total).append("%").append("\n")
                .append("rds状态错误：").append(rdsStatusErrorInt).append(",占比：").append(rdsStatusErrorInt * 100 / total).append("%").append("\n")
                .append("试用用户：").append(isTrialUserInt).append(",占比：").append(isTrialUserInt * 100 / total).append("%").append("\n")
                .append("域名为开启rds：").append(domainNoRdsInt).append(",占比：").append(domainNoRdsInt * 100 / total).append("%").append("\n")
                .append("rds黑名单：").append(rdsInBlackListInt).append(",占比：").append(rdsInBlackListInt * 100 / total).append("%").append("\n")
                .append("getTradeAPIByStatus：").append(getTradeAPIByStatusInt).append(",占比：").append(getTradeAPIByStatusInt * 100 / total).append("%").append("\n")
                .append("StockChangeOnsaleTypeService_getTradeAPIByStatus：").append(StockChangeOnsaleTypeService_getTradeAPIByStatusInt).append("\n").append(",占比：").append(StockChangeOnsaleTypeService_getTradeAPIByStatusInt * 100 / total).append("%").append("\n")
                .append("StockService_getTradeAPIByStatus：").append(StockService_getTradeAPIByStatusInt).append(",占比：").append(StockService_getTradeAPIByStatusInt * 100 / total).append("%").append("\n")
                .append("queryTaobaoTradeWithSoldGetCount：").append(queryTaobaoTradeWithSoldGetCountInt).append(",占比：").append(queryTaobaoTradeWithSoldGetCountInt * 100 / total).append("%").append("\n")
                .append("taobaoAPI_queryCount：").append(taobaoAPI_queryCountInt).append(",占比：").append(taobaoAPI_queryCountInt * 100 / total).append("%").append("\n")
                .append("ReturnChangeOrderService_getTradeList：").append(ReturnChangeOrderService_getTradeListInt).append(",占比：").append(ReturnChangeOrderService_getTradeListInt * 100 / total).append("%").append("\n")
                .append("查询rds异常，重查api：").append(queryTaobaoTradeWithSoldGetWithPage_exceptionInt).append(",占比：").append(queryTaobaoTradeWithSoldGetWithPage_exceptionInt * 100 / total).append("%").append("\n")
                .append("其他情况：").append(otherInt).append(",占比：").append(otherInt * 100 / total).append("%").append("\n")
                .append("总计：").append(total);
        logger.info(sbTotal.toString());
        SendDingTalkClient.sendDingTalkClient("https://oapi.dingtalk.com/robot/send?access_token=15141d2e792a3f8b43ffee14f739eba800176da677b19d5d5b13598bb4b268da", sbTotal.toString());

        return ReturnT.SUCCESS;
    }
}
