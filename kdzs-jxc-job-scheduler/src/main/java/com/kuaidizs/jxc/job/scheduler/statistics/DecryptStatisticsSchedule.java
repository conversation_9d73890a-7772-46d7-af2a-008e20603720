package com.kuaidizs.jxc.job.scheduler.statistics;

import com.kuaidizs.jxc.common.util.DingDingUtil;
import com.kuaidizs.jxc.response.statistics.StatisticsYesterdayDecryptInfoResponse;
import com.kuaidizs.jxc.service.statistics.DecryptStatisticsService;
import com.raycloud.bizlogger.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 解密统计定时任务
 *
 * <AUTHOR>
 * @date 2022/9/15 5:40 下午
 */
@Component
public class DecryptStatisticsSchedule {

    private static final Logger logger = Logger.getLogger(DecryptStatisticsSchedule.class);

    @Autowired
    private DecryptStatisticsService decryptStatisticsService;

    @Scheduled(cron = "0 30 9 * * ?")
    public void execute() {
        StatisticsYesterdayDecryptInfoResponse response = decryptStatisticsService.statisticsYesterdayDecryptInfo();
        String content = "统计日期：" + response.getDate() + "\n"
                + "昨日虚拟号解密接口总调用人数：" + response.getTotalUserCount() + "\n"
                + "昨日虚拟号解密接口总调用量：" + response.getTotalDecryptCount() + "\n"
                + "昨日虚拟号解密接口人均调用量：" + response.getAveDecryptCount();
        DingDingUtil.send(content, DingDingUtil.STATISTICS_YESTERDAY_DECRYPT_INFO_URL, DingDingUtil.STATISTICS_YESTERDAY_DECRYPT_INFO_SECRET);
    }
}
