package com.kuaidizs.jxc.job.scheduler.rds;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.kuaidizs.jxc.common.db.Result;
import com.kuaidizs.jxc.common.util.BizLogOnsDao;
import com.kuaidizs.jxc.common.util.Constant;
import com.kuaidizs.jxc.common.util.DateUtil;
import com.kuaidizs.jxc.common.util.LogHelper;
import com.kuaidizs.jxc.common.util.trade.PrintVersionRecordUtil;
import com.kuaidizs.jxc.dao.UserDAO;
import com.kuaidizs.jxc.domain.User;
import com.kuaidizs.jxc.domain.trade.PrintVersionRecord;
import com.kuaidizs.jxc.query.UserQuery;
import com.kuaidizs.jxc.service.trade.PrintBgKddService;
import com.kuaidizs.jxc.service.trade.PrintVersionRecordService;
import com.kuaidizs.jxc.service.wrap.UserRdsService;
import com.kuaidizs.spymemcached.extend.listener.RayMemcacheClientUtil;
import com.raycloud.bizlogger.Logger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * RDS取消用户定时订阅消息
 * Created by jinglongjun on 2018/4/18.
 */
//@Component
public class RdsUnRegisterSchedule {

    @Resource
    UserDAO userDAO;
    @Resource
    UserRdsService userRdsService;
    @Autowired
    private PrintBgKddService printBgKddService;
    @Autowired
    private PrintVersionRecordService printVersionRecordService;
    @Autowired
    private RayMemcacheClientUtil rayMemcacheClientUtil;
    @Autowired
    private BizLogOnsDao bizLogOnsDao;

    private final Logger logger = Logger.getLogger(RdsUnRegisterSchedule.class);

    /**
     * 每天1一点定时执行
     */
//    @Scheduled(cron = "0 0 1 * * ?")
//    @Async
//    @Scheduled(fixedRate = 60 * 60 * 1000, initialDelay = 100 * 1000)
    public void schedule() {
//        boolean limit = PreCheckUtils.checkBackTaskAllowed(GlobalLimitConfigKey.BACK_TASK_CLOSE_PERIOD);
//        if (limit) {
//            return;
//        }
        logger.biz("用户取消注册RDS开始");
        long start = System.currentTimeMillis();
        int total = 0;
        //清理1个月未登录的用户
        Date startDate = DateUtils.addMonths(new Date(), -1);
        UserQuery userQuery = new UserQuery();
        userQuery.setLastLoginTimeEnd(startDate);
        userQuery.setLastLoginTimeStart(DateUtils.addYears(startDate, -10));
        userQuery.setExpireTimeStart(new Date());

        int pageNo = 1;
        int pageSize = 200;
        userQuery.setPageSize(pageSize);
        Result<User> userResult = userDAO.getUserListWithPage(userQuery, false);
        while (userResult != null && CollectionUtils.isNotEmpty(userResult.getList())) {
            Map<Long, String> taobaoIdUserMap = userResult.getList().stream().collect(Collectors.toMap(User::getTaobaoId, User::getTaobaoNick, (k1, k2) -> k1));
            Map<Long, Boolean> resultMap = unRegister(userResult.getList());
            List<Map.Entry<Long, Boolean>> successList = resultMap.entrySet().stream().filter(entry -> entry.getValue()).collect(Collectors.toList());
            List<String> taobaoNickList = successList.stream().map(c -> taobaoIdUserMap.get(c.getKey())).collect(Collectors.toList());
            logger.biz("pageNo:" + pageNo + "|用户取消注册RDS集合:" + JSON.toJSONString(taobaoNickList));
            JSONObject logJson = new JSONObject();
            logJson.put("pageNo", pageNo);
            logJson.put("userIdList", successList.stream().map(c -> c.getKey()).collect(Collectors.toList()));
            bizLogOnsDao.bizLogPush(null, null, BizLogOnsDao.BACK_CANCEL_RDS_USERIDLIST, logJson);
            total += successList.size();
            if (userResult.getList().size() < pageSize) {
                break;
            }
            pageNo++;
            userQuery.setPage(pageNo);
            userResult = userDAO.getUserListWithPage(userQuery, false);
        }
        logger.biz("用户取消注册RDS结束，总共取消用户数为：" + total + "，耗时：" + (System.currentTimeMillis() - start) / 1000 + " s");
    }

    /**
     * 取消注册
     *
     * @param users user
     */
    private Map<Long, Boolean> unRegister(List<User> users) {
        Assert.isTrue(CollectionUtils.isNotEmpty(users), "users cannot be empty");
        Map<Long, Boolean> resultMap = new HashMap<>();
        for (User user : users) {
            try {
                PrintVersionRecord printVersionRecord = printVersionRecordService.getFromCache(user, user.getTaobaoId());
                //试用用户不注册
                if (PrintVersionRecordUtil.isTrialUser(printVersionRecord)) {
                    continue;
                }
                String loginRegisterTimeKey = Constant.APP_KEY + "_" + user.getTaobaoId() + "LOGIN_RE_REGISTERRDS_DATE";
                if (rayMemcacheClientUtil.get(loginRegisterTimeKey) != null) {
                    logger.biz("用户userId:{} nick:{} 七天内已注册过RDS", user.getTaobaoId(), user.getTaobaoNick());
                    continue;
                }
                boolean b = userRdsService.unRegisterRdsNew(user, (user1 -> {
                    //近一个月有打单取号有记录不同意取消
                    Date endDate = new Date();
                    Date startDate = DateUtils.addMonths(endDate, -1);
                    Long accountYdNosCount = printBgKddService.getAccountYdNosCount(user1, user1.getTaobaoId(), DateUtil.convertToStr(startDate), DateUtil.convertToStr(endDate));
                    return accountYdNosCount > 0;
                }));
                resultMap.put(user.getId(), b);
            } catch (Exception e) {
                logger.error(LogHelper.buildErrorLog(user, e, "取消RDS注册失败").toString(), e);
            }
        }
        return resultMap;
    }
}