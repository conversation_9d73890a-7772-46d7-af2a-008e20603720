package com.kuaidizs.jxc.job.scheduler.sf;

import com.kuaidizs.jxc.dao.print.SfChildMotherRelationDAO;
import com.raycloud.bizlogger.Logger;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Intro:
 * @Author: WangJiongDa(yunkai)
 * @Date: 2018/11/27
 * @Time: 下午6:41
 */
//@Component
public class SFDeleteRelationSchedule {

    private static Logger logger = Logger.getLogger(SFDeleteRelationSchedule.class);

    @Resource
    private SfChildMotherRelationDAO sfChildMotherRelationDAO;

    //@Scheduled(cron="0/10 * *  * * ? ")
    public void schedule() {
        try {
            logger.biz("定时删除顺丰网点18年之前的数据任务开始...");
            int totalCount = 0;
            long maxId = 15876852;
            //取最大的id
            long minId = 1;
            if (minId + 1000 >= maxId) {
                sfChildMotherRelationDAO.addBakSfChildMotherRelation(maxId);
                Integer deleteCount = sfChildMotherRelationDAO.deleteBeforeMaxId(maxId);
                totalCount += deleteCount;
            } else {
                for (long i = minId + 1000; i <= maxId; i = i + 1000) {
                    sfChildMotherRelationDAO.addBakSfChildMotherRelation(i);
                    Integer deleteCount = sfChildMotherRelationDAO.deleteBeforeMaxId(i);
                    totalCount += deleteCount;
                    if (1000 + i > maxId) {
                        sfChildMotherRelationDAO.addBakSfChildMotherRelation(maxId);
                        deleteCount = sfChildMotherRelationDAO.deleteBeforeMaxId(maxId);
                        totalCount += deleteCount;
                        break;
                    }
                    //睡眠下
                    Thread.sleep(200);
                }
            }
            logger.biz("顺丰网点删除18年之前的数据任务结束，共删除数据条数->" + totalCount);
        } catch (Exception e) {
            logger.error("顺丰网点删除18年之前的数据失败 error = {}", e.getMessage(), e);
        }
    }
}
