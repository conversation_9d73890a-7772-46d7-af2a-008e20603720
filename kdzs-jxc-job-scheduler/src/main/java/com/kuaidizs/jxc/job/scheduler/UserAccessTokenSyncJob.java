package com.kuaidizs.jxc.job.scheduler;

import com.kuaidizs.jxc.common.util.diamond.GlobalLimitConfigKey;
import com.kuaidizs.jxc.common.util.diamond.PreCheckUtils;
import com.kuaidizs.jxc.dao.UserDAO;
import com.kuaidizs.jxc.domain.user.UserAccessToken;
import com.kuaidizs.jxc.query.user.UserAccessTokenQuery;
import com.kuaidizs.jxc.service.UserService;
import com.kuaidizs.jxc.service.user.UserAccessTokenService;
import org.apache.commons.lang.time.DateUtils;
import com.raycloud.bizlogger.Logger;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 刷新用户会话
 *
 * <AUTHOR>    Date:2016/9/9  19:15
 */
@DisallowConcurrentExecution
public class UserAccessTokenSyncJob implements Job {

    public static final Logger logger = Logger.getLogger(JobContants.TOKEN_LOGGER);

    public static final int PAGE_SIZE = 2000;
    /**
     * 同步用户修改的天数
     */
    private static final int DAYS = -3;

    @Resource
    private UserAccessTokenService userAccessTokenService;
    @Resource
    UserDAO userDAO;
    @Resource
    UserService userService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        boolean refused = PreCheckUtils.checkBackTaskAllowed(GlobalLimitConfigKey.BACK_TASK_CLOSE_PERIOD);
        if (refused) {
            return;
        }
        final SimpleDateFormat sdf = new SimpleDateFormat("yyyy-dd-MM HH:mm:ss");
        Date start = DateUtils.addDays(new Date(), DAYS);
        Date notRefreshDate = DateUtils.addDays(new Date(), -2);
        logger.info("-----------开始刷新token，系统执行开始时间:" + sdf.format(new Date())
                + ", startDate:" + sdf.format(start));
        try {
            UserAccessTokenQuery userAccessTokenQuery = new UserAccessTokenQuery();
            //刷新最近三天登录用户的token
            userAccessTokenQuery.setEndLoginDateStart(start);
            userAccessTokenQuery.setEnableStatus(true);
            Integer count = userAccessTokenService.getSumUserAccessToken(userAccessTokenQuery);
            logger.info("本次需要刷新token用户数：" + count);
            if (count == null || count == 0) {
                return;
            }
            long successRefresh = 0;
            long startTime = System.currentTimeMillis();
            int pageNum = count % PAGE_SIZE == 0 ? (count / PAGE_SIZE) : (count / PAGE_SIZE + 1);
            for (int i = 0; i < pageNum; i++) {
                userAccessTokenQuery.setPage(i + 1);
                userAccessTokenQuery.setPageSize(PAGE_SIZE);
                userAccessTokenQuery.orderbyId(true);
                List<UserAccessToken> userAccessTokenList = userAccessTokenService.getUserAccessTokenList(userAccessTokenQuery);
                if (userAccessTokenList == null || userAccessTokenList.isEmpty()) {
                    continue;
                }
                long time = System.currentTimeMillis();
                for (UserAccessToken userAccessToken : userAccessTokenList) {
                    try {
                        Date refreshDate = userAccessToken.getRefreshDate();
                        if (refreshDate != null && refreshDate.after(notRefreshDate)) {
                            logger.info(userAccessToken.getTaobaoNick() + " 用户刷新时间为2天内，跳过刷新. refreshDate:" + sdf.format(refreshDate));
                            continue;
                        }
                        boolean isRefresh = userAccessTokenService.executeUserAccessToken(userAccessToken);
                        if (isRefresh) {
                            successRefresh++;
                        }
                    } catch (Exception e) {
                        logger.error(new StringBuilder("[").append(userAccessToken.getTaobaoId()).append(",")
                                .append(userAccessToken.getTaobaoNick()).append("] ")
                                .append("更新refresh出错，错误原因：").append(e.getMessage()), e);
                    }
                }
                logger.info("[userAccessToken]刷新" + userAccessTokenList.size() + "个用户，耗时:" + (System.currentTimeMillis() - time));
            }
            logger.info("[userAccessToken]刷新完[" + count + "]个用户accessToken，成功刷新[" + successRefresh + "]个用户，耗时:"
                    + (System.currentTimeMillis() - startTime));
        } catch (Exception e) {
            logger.error("刷新userAccessToken发生错误，错误原因:" + e.getMessage(), e);
        }
        logger.info("-----------结束刷新token，系统执行结束时间:" + sdf.format(new Date()));
    }


}

