package com.kuaidizs.jxc.job.scheduler;

import com.kuaidizs.jxc.common.util.DataBaseUtil;
import com.kuaidizs.jxc.common.util.DateUtil;
import com.kuaidizs.jxc.common.util.diamond.GlobalLimitConfigKey;
import com.kuaidizs.jxc.common.util.diamond.PreCheckUtils;
import com.kuaidizs.jxc.dao.trade.*;
import com.kuaidizs.jxc.job.utils.JobUtils;
import com.kuaidizs.jxc.query.trade.*;
import com.kuaidizs.jxc.service.moveTask.MoveTaskDIngTalkService;
import com.raycloud.bizlogger.Logger;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;

/**
 * 日志冷库处理定时任务.
 * Author: <EMAIL>
 * Date: 2017/2/24
 * Time: 14:57
 */
@DisallowConcurrentExecution
public class MoveCoolDataJob9 extends AbstractMoveData  implements Job {

    public static final Logger logger = Logger.getLogger(MoveCoolDataJob9.class);

    private static final int PAGE_SIZE = 5000;  //迁移量为5000

    @Resource
    private PrintBgFhdDAO printBgFhdDAO;

    @Resource
    private DzmdYzLognumberDAO dzmdYzLognumberDAO;

    @Resource
    private PrintKddLogDAO printKddLogDAO;

    @Resource
    private PrintBgKddDAO printBgKddDAO;

    @Resource
    private DzmdWdLognumberDAO dzmdWdLognumberDAO;

    @Autowired
    private MoveTaskDIngTalkService moveTaskDIngTalkService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        boolean flag = true;
        if (flag) {
            return;
        }
        try {
            boolean refused = PreCheckUtils.checkBackTaskAllowed(GlobalLimitConfigKey.BACK_TASK_CLOSE_DAY);
            if (refused) {
                logger.biz("###底单等数据迁移开关关闭###");
                return;
            }
            Calendar date = Calendar.getInstance();
            date.add(Calendar.MONTH, -3);
            date.set(Calendar.DATE, 1);//设为当前月的1号

            int fkId = 9;
            movePrintBgKddLog(fkId, date.getTime());
            movePrintKddLog(fkId, date.getTime());
            moveDzmdYzLognumber(fkId, date.getTime());
            movePrintBgFhd(fkId, date.getTime());
            moveDzmdWdLognumber(fkId, date.getTime());
            moveTradePeding(fkId, date.getTime());
            movePrintDg(fkId);
            moveAddressModifyLog(fkId, date.getTime());
            deleteCjdfTempTrade(String.valueOf(fkId));
        } catch (Exception e) {
            logger.error("###迁移冷库数据失败，失败原因:" + e.getMessage(), e);
            moveTaskDIngTalkService.addMoveErrorInfo("迁移冷库数据失败",e.toString(),null,null);
        }
    }

    /***
     * 发货单记录 print_bg_fhd
     * @param fkId 分库Id
     * @param moveDate  迁移开始日期
     */
    public void movePrintBgFhd(int fkId, Date moveDate) {
        for (int i = 0; i < DataBaseUtil.FHD_LOG_SPLIT; i++) {
            try {
                if (DateUtil.compareDateByNow()){
                    return;
                }

                PrintBgFhdQuery query = new PrintBgFhdQuery();
                query.setCreatedEnd(moveDate);

                Integer count = printBgFhdDAO.getPrintBgFhdListCount(query, String.valueOf(i), String.valueOf(fkId));
                count = JobUtils.getDealCount(count);

                //迁移三个月之前的数据
                if (count >= PAGE_SIZE) {
                    for (int pageNo = 1; pageNo <= (count + PAGE_SIZE - 1) / PAGE_SIZE; pageNo++) {
                        query.setPage(pageNo);
                        query.setPageSize(PAGE_SIZE);

                        Integer result = printBgFhdDAO.movePrintBgFhdToCoolData(query, String.valueOf(i),
                                String.valueOf(fkId));

                        logger.biz("大于5000行记录 迁移发货单记录 print_bg_fhd " + fkId + "." + i
                                + " 开始行号：" + query.getStartRow()
                                + " 结束行号：" + query.getEndRow()
                                + " count：" + count);
                    }
                } else {

                    Integer result = printBgFhdDAO.movePrintBgFhdToCoolData(query, String.valueOf(i), String.valueOf(fkId));

                    logger.biz("小于5000行记录 迁移发货单记录 print_bg_fhd " + fkId + "." + i + " count：" + count);
                }

                //删除三个月之前的数据
                if (count >= PAGE_SIZE) {
                    for (int pageNo = 1; pageNo <= (count + PAGE_SIZE - 1) / PAGE_SIZE; pageNo++) {
                        query.setPage(1);
                        query.setPageSize(PAGE_SIZE);

                        Integer result = printBgFhdDAO.deletePrintBgFhdOldData(query, String.valueOf(i),
                                String.valueOf(fkId));

                        logger.biz("大于5000行记录 删除发货单记录 print_bg_fhd " + fkId + "." + i + " count：" + count);
                    }
                } else {

                    Integer result = printBgFhdDAO.deletePrintBgFhdOldData(query, String.valueOf(i),
                            String.valueOf(fkId));

                    logger.biz("小于5000行记录 删除发货单记录 print_bg_fhd " + fkId + "." + i + " count：" + count);
                }

            } catch (Exception e) {
                logger.error("###迁移发货单记录" + fkId + "." + i + "，失败原因:" + e.getMessage(), e);
                moveTaskDIngTalkService.addMoveErrorInfo("迁移底单日志记录",e.toString(),fkId,i);
            }

        }
    }

    /***
     * 菜鸟电子面单申请记录表  dzmd_yz_lognumber
     * @param fkId  分库Id
     * @param moveDate  迁移开始日期
     */
    public void moveDzmdYzLognumber(int fkId, Date moveDate) {
        for (int i = 0; i < DataBaseUtil.YZ_LOG_SPLIT; i++) {
            try {
                if (DateUtil.compareDateByNow()){
                    return;
                }

                DzmdYzLognumberQuery query = new DzmdYzLognumberQuery();
                query.setCreatedEnd(moveDate);

                Integer count = dzmdYzLognumberDAO.getDzmdYzLognumberListCount(query, String.valueOf(i), String.valueOf(fkId));
                count = JobUtils.getDealCount(count);

                //迁移三个月之前的数据
                if (count >= PAGE_SIZE) {
                    for (int pageNo = 1; pageNo <= (count + PAGE_SIZE - 1) / PAGE_SIZE; pageNo++) {
                        query.setPage(pageNo);
                        query.setPageSize(PAGE_SIZE);

                        Integer result = dzmdYzLognumberDAO.moveDzmdYzLognumberToCoolData(query, String.valueOf(i), String.valueOf(fkId));

                        logger.biz("大于5000行记录 迁移菜鸟电子面单申请记录表 dzmd_yz_lognumber " + fkId + "." + i
                                + " 开始行号：" + query.getStartRow()
                                + " 结束行号：" + query.getEndRow()
                                + " count：" + count);
                    }
                } else {

                    Integer result = dzmdYzLognumberDAO.moveDzmdYzLognumberToCoolData(query, String.valueOf(i), String.valueOf(fkId));

                    logger.biz("小于于5000行记录 迁移菜鸟电子面单申请记录表 dzmd_yz_lognumber " + fkId + "." + i + " count：" + count);

                }

                //删除三个月之前的数据
                if (count >= PAGE_SIZE) {
                    for (int pageNo = 1; pageNo <= (count + PAGE_SIZE - 1) / PAGE_SIZE; pageNo++) {
                        query.setPage(1);
                        query.setPageSize(PAGE_SIZE);

                        Integer result = dzmdYzLognumberDAO.deleteDzmdYzLognumberOldData(query, String.valueOf(i), String.valueOf(fkId));

                        logger.biz("大于5000行记录 删除菜鸟电子面单申请记录表 dzmd_yz_lognumber " + fkId + "." + i + " count：" + count);
                    }
                } else {

                    Integer result = dzmdYzLognumberDAO.deleteDzmdYzLognumberOldData(query, String.valueOf(i), String.valueOf(fkId));

                    logger.biz("小于5000行记录 删除菜鸟电子面单申请记录表 dzmd_yz_lognumber " + fkId + "." + i + " count：" + count);
                }
                Date startTime = DateUtil.setDateByHms(2, 0, 0);
                Date endTime = DateUtil.setDateByHms(6, 0, 0);
                Date date = new Date();
                if (date.before(startTime) || date.after(endTime)) {
                    logger.biz("时间超过 迁移 结束 菜鸟电子面单申请记录表 dzmd_yz_lognumber ");
                    break;
                }
            } catch (Exception e) {
                logger.error("###迁移菜鸟电子面单申请记录" + fkId + "." + i + "，失败原因:" + e.getMessage(), e);
                moveTaskDIngTalkService.addMoveErrorInfo("迁移底单日志记录",e.toString(),fkId,i);
            }

        }
    }

    /***
     * 快递单打印日志表数据迁移 print_kdd_log
     * @param fkId  分库Id
     * @param moveDate  迁移开始日期
     */
    public void movePrintKddLog(int fkId, Date moveDate) {
        for (int i = 0; i < DataBaseUtil.KDD_LOG_SPLIT; i++) {
            try {
                if (DateUtil.compareDateByNow()){
                    return;
                }

                PrintKddLogQuery query = new PrintKddLogQuery();
                query.setCreatedEnd(moveDate);

                Integer count = printKddLogDAO.getPrintKddLogListCount(query, String.valueOf(i), String.valueOf(fkId));
                count = JobUtils.getDealCount(count);

                //迁移三个月之前的数据
                if (count >= PAGE_SIZE) {
                    for (int pageNo = 1; pageNo <= (count + PAGE_SIZE - 1) / PAGE_SIZE; pageNo++) {
                        query.setPage(pageNo);
                        query.setPageSize(PAGE_SIZE);

                        Integer result = printKddLogDAO.movePrintKddLogToCoolData(query, String.valueOf(i), String.valueOf(fkId));

                        logger.biz("大于5000行记录 迁移快递单打印日志表 print_kdd_log " + fkId + "." + i
                                + " 开始行号：" + query.getStartRow()
                                + " 结束行号：" + query.getEndRow()
                                + " count：" + count);
                    }
                } else {

                    Integer result = printKddLogDAO.movePrintKddLogToCoolData(query, String.valueOf(i), String.valueOf(fkId));

                    logger.biz("小于5000行记录 迁移快递单打印日志表 print_kdd_log " + fkId + "." + i + " count：" + count);
                }

                //删除三个月之前的数据
                if (count >= PAGE_SIZE) {
                    for (int pageNo = 1; pageNo <= (count + PAGE_SIZE - 1) / PAGE_SIZE; pageNo++) {
                        query.setPage(1);
                        query.setPageSize(PAGE_SIZE);

                        Integer result = printKddLogDAO.deletePrintKddLogOldData(query, String.valueOf(i), String.valueOf(fkId));

                        logger.biz("大于5000行记录 删除快递单打印日志表 print_kdd_log " + fkId + "." + i
                                + " count：" + count);

                    }
                } else {

                    Integer result = printKddLogDAO.deletePrintKddLogOldData(query, String.valueOf(i), String.valueOf(fkId));

                    logger.biz("小于5000行记录 删除快递单打印日志表 print_kdd_log " + fkId + "." + i
                            + " count：" + count);
                }

            } catch (Exception e) {
                logger.error("###迁移快递单打印日志记录" + fkId + "." + i + "，失败原因:" + e.getMessage(), e);
                moveTaskDIngTalkService.addMoveErrorInfo("迁移底单日志记录",e.toString(),fkId,i);
            }


        }
    }

    /***
     * 底单日志表 print_bg_kdd
     * @param fkId  分库Id
     * @param moveDate  迁移开始日期
     */

    public void movePrintBgKddLog(int fkId, Date moveDate) {
        for (int i = 0; i < DataBaseUtil.BG_LOG_SPLIT; i++) {
            try {
                if (DateUtil.compareDateByNow()){
                    return;
                }

                PrintBgKddQuery query = new PrintBgKddQuery();
                query.setCreatedEnd(moveDate);

                Integer count = printBgKddDAO.movePrintBgKddToCoolDataCount(query, String.valueOf(i), String.valueOf(fkId));
                count = JobUtils.getDealCount(count);

                //迁移三个月之前的数据
                if (count >= PAGE_SIZE) {
                    for (int pageNo = 1; pageNo <= (count + PAGE_SIZE - 1) / PAGE_SIZE; pageNo++) {
                        query.setPage(pageNo);
                        query.setPageSize(PAGE_SIZE);
                        Integer result = printBgKddDAO.movePrintBgKddToCoolData(query, String.valueOf(i), String.valueOf(fkId));

                        logger.biz("大于5000行记录 迁移底单日志表 print_bg_kdd " + fkId + "." + i
                                + " 开始行号：" + query.getStartRow()
                                + " 结束行号：" + query.getEndRow()
                                + " count：" + count);
                    }
                } else {

                    Integer result = printBgKddDAO.movePrintBgKddToCoolData(query, String.valueOf(i), String.valueOf(fkId));

                    logger.biz("小于5000行记录 迁移底单日志表 print_bg_kdd " + fkId + "." + i + " count：" + count);
                }

                //删除三个月之前的数据
                if (count >= PAGE_SIZE) {
                    for (int pageNo = 1; pageNo <= (count + PAGE_SIZE - 1) / PAGE_SIZE; pageNo++) {
                        query.setPage(1);
                        query.setPageSize(PAGE_SIZE);

                        Integer result = printBgKddDAO.deletePrintBgKddOldData(query, String.valueOf(i), String.valueOf(fkId));

                        logger.biz("大于5000行记录 删除底单日志表 print_bg_kdd " + fkId + "." + i + " count：" + count);

                    }
                } else {

                    Integer result = printBgKddDAO.deletePrintBgKddOldData(query, String.valueOf(i), String.valueOf(fkId));

                    logger.biz("小于5000行记录 删除底单日志表 print_bg_kdd " + fkId + "." + i + " count：" + count);
                }

            } catch (Exception e) {
                logger.error("###迁移底单日志记录" + fkId + "." + i + "，失败原因:" + e.getMessage(), e);
                moveTaskDIngTalkService.addMoveErrorInfo("迁移底单日志记录",e.toString(),fkId,i);
            }

        }

    }

    /***
     * 网点电子面单申请记录表 dzmd_wd_lognumber
     * @param fkId  分库Id
     * @param moveDate  迁移开始日期
     */

    public void moveDzmdWdLognumber(int fkId, Date moveDate) {
        for (int i = 0; i < DataBaseUtil.WD_LOG_SPLIT; i++) {
            try {
                if (DateUtil.compareDateByNow()){
                    return;
                }

                DzmdWdLognumberQuery query = new DzmdWdLognumberQuery();
                query.setCreatedEnd(moveDate);

                Integer count = dzmdWdLognumberDAO.getDzmdWdLognumberListCount(query, String.valueOf(i), String.valueOf(fkId));
                count = JobUtils.getDealCount(count);

                //迁移三个月之前的数据
                if (count >= PAGE_SIZE) {
                    for (int pageNo = 1; pageNo <= (count + PAGE_SIZE - 1) / PAGE_SIZE; pageNo++) {
                        query.setPage(pageNo);
                        query.setPageSize(PAGE_SIZE);
                        Integer result = dzmdWdLognumberDAO.moveDzmdWdLognumberToCoolData(query, String.valueOf(i), String.valueOf(fkId));

                        logger.biz("大于5000行记录 迁移网点电子面单申请记录表 dzmd_wd_lognumber " + fkId + "." + i
                                + " 开始行号：" + query.getStartRow()
                                + " 结束行号：" + query.getEndRow()
                                + " count：" + count);

                    }
                } else {

                    Integer result = dzmdWdLognumberDAO.moveDzmdWdLognumberToCoolData(query, String.valueOf(i), String.valueOf(fkId));

                    logger.biz("小于5000行记录 迁移网点电子面单申请记录表 dzmd_wd_lognumber " + fkId + "." + i + " count：" + count);
                }

                //删除三个月之前的数据
                if (count >= PAGE_SIZE) {
                    for (int pageNo = 1; pageNo <= (count + PAGE_SIZE - 1) / PAGE_SIZE; pageNo++) {
                        query.setPage(1);
                        query.setPageSize(PAGE_SIZE);

                        Integer result = dzmdWdLognumberDAO.deleteDzmdWdLognumberOldData(query, String.valueOf(i), String.valueOf(fkId));

                        logger.biz("小于5000行记录 删除网点电子面单申请记录 dzmd_wd_lognumber " + fkId + "." + i + " count：" + count);

                    }
                } else {

                    Integer result = dzmdWdLognumberDAO.deleteDzmdWdLognumberOldData(query, String.valueOf(i), String.valueOf(fkId));

                    logger.biz("小于5000行记录 删除网点电子面单申请记录 dzmd_wd_lognumber " + fkId + "." + i + " count：" + count);
                }

            } catch (Exception e) {
                logger.error("###迁移网点电子面单申请记录" + fkId + "." + i + "，失败原因:" + e.getMessage(), e);
                moveTaskDIngTalkService.addMoveErrorInfo("迁移底单日志记录",e.toString(),fkId,i);
            }

        }
    }


}
