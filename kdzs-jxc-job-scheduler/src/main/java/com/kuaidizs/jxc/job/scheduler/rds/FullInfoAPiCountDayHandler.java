package com.kuaidizs.jxc.job.scheduler.rds;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.kuaidizs.jxc.common.util.dingTalk.SendDingTalkClient;
import com.kuaidizs.spymemcached.extend.listener.RayMemcacheClientUtil;
import com.raycloud.bizlogger.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Component
@JobHandler("fullInfoAPiCountDayHandler")
public class FullInfoAPiCountDayHandler extends IJobHandler {


    @Autowired
    private RayMemcacheClientUtil rayMemcacheClientUtil;

    private static final Logger logger = Logger.getLogger(FullInfoAPiCountDayHandler.class);


    @Override
    public ReturnT<String> execute(String today) throws Exception {

        //10点推送一下前一天数据
        Integer freeUserTotal = 0;
        Integer gt30DayTotal = 0;
        Integer otherValueTotal = 0;
        Integer gt60DayTotal = 0;
        Integer gt90DayTotal = 0;
        Integer tradeNullTotal = 0;
        String lastDay = DateUtil.formatDate(DateUtil.yesterday());
        if (StringUtils.isNotBlank(today)) {
            lastDay = DateUtil.formatDate(new Date());
        }
        for (int i = 0; i < 24; i++) {
            Object freeUserValue = rayMemcacheClientUtil.get("12158997_DETAILCOUNT_" + lastDay + "_" + i + "_FREEUSER");
            Object gt30DayValue = rayMemcacheClientUtil.get("12158997_DETAILCOUNT_" + lastDay + "_" + i + "_GT30Day");
            Object otherValue = rayMemcacheClientUtil.get("12158997_DETAILCOUNT_" + lastDay + "_" + i + "_OTHER");
            Object gt60DayValue = rayMemcacheClientUtil.get("12158997_DETAILCOUNT_" + lastDay + "_" + i + "_GT60Day");
            Object gt90DayValue = rayMemcacheClientUtil.get("12158997_DETAILCOUNT_" + lastDay + "_" + i + "_GT90Day");
            Object tradeNullValue = rayMemcacheClientUtil.get("12158997_DETAILCOUNT_" + lastDay + "_" + i + "_TRADENULL");

            if (Objects.nonNull(freeUserValue)) {
                freeUserTotal = freeUserTotal + Convert.convert(Integer.class, freeUserValue, 0);
            }
            if (Objects.nonNull(gt30DayValue)) {
                gt30DayTotal = gt30DayTotal + Convert.convert(Integer.class, gt30DayValue, 0);
            }
            if (Objects.nonNull(otherValue)) {
                otherValueTotal = otherValueTotal + Convert.convert(Integer.class, otherValue, 0);
            }
            if (Objects.nonNull(gt60DayValue)) {
                gt60DayTotal = gt60DayTotal + Convert.convert(Integer.class, gt60DayValue, 0);
            }
            if (Objects.nonNull(gt90DayValue)) {
                gt90DayTotal = gt90DayTotal + Convert.convert(Integer.class, gt90DayValue, 0);
            }
            if (Objects.nonNull(tradeNullValue)) {
                tradeNullTotal = tradeNullTotal + Convert.convert(Integer.class, tradeNullValue, 0);
            }
        }
        StringBuilder sbTotal = new StringBuilder(DateUtil.yesterday().toDateStr());

        if (StringUtils.isBlank(today)) {
            sbTotal.append("【前一天总量统计】-详情走api：");
        } else {
            sbTotal.append("【当天总量统计】-详情走api：");
        }
        int total = freeUserTotal + gt30DayTotal + otherValueTotal + gt60DayTotal + gt90DayTotal + tradeNullTotal;
        sbTotal.append("\n")
                .append("免费用户调用：").append(freeUserTotal).append(",占比：").append(freeUserTotal * 100 / total).append("%").append("\n")
                .append("大于30天小于60天订单：").append(gt30DayTotal).append(",占比：").append(gt30DayTotal * 100 / total).append("%").append("\n")
                .append("大于60天小于90天订单：").append(gt60DayTotal).append(",占比：").append(gt60DayTotal * 100 / total).append("%").append("\n")
                .append("大于90天订单：").append(gt90DayTotal).append(",占比：").append(gt90DayTotal * 100 / total).append("%").append("\n")
                .append("api也不存在订单：").append(tradeNullTotal).append(",占比：").append(tradeNullTotal * 100 / total).append("%").append("\n")
                .append("未知情况（状态时间不一致）：").append(otherValueTotal).append(",占比：").append(otherValueTotal * 100 / total).append("%").append("\n")
                .append("总计：").append(total);
        logger.info(sbTotal.toString());
        SendDingTalkClient.sendDingTalkClient("https://oapi.dingtalk.com/robot/send?access_token=15141d2e792a3f8b43ffee14f739eba800176da677b19d5d5b13598bb4b268da", sbTotal.toString());

        return ReturnT.SUCCESS;
    }
}
