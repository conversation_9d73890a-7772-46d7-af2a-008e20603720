package com.kuaidizs.jxc.job.scheduler.importdelivery;

import com.kuaidizs.jxc.service.trade.ImportDeliveryService;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 每天凌晨2点清除两个小时之前的导入发货记录
 *
 * <AUTHOR>
 * @since 2018-12-06
 */
@Component
public class OldImportDeliveryCleanScheduler0 {

    @Resource
    private ImportDeliveryService importDeliveryService;


    @Scheduled(cron = "0 0 2 1 * ?")
    public void cleanOldData() {
        importDeliveryService.cleanOldData("0");
    }


}
