package com.kuaidizs.jxc.job.scheduler;

import com.kuaidizs.jxc.common.util.diamond.GlobalLimitConfigKey;
import com.kuaidizs.jxc.common.util.diamond.PreCheckUtils;
import com.kuaidizs.jxc.dao.trade.PrintTempdatafplDAO;
import com.kuaidizs.jxc.dao.trade.PrintTempdataplDAO;
import com.raycloud.bizlogger.Logger;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * Created by meiweifeng on 16/9/11.
 */
@DisallowConcurrentExecution
public class TradeDeleteJob0 implements Job{

    private static  final Logger logger = Logger.getLogger(TradeDeleteJob0.class);

    @Resource
    PrintTempdatafplDAO printTempdatafplDAO;
    @Resource
    PrintTempdataplDAO printTempdataplDAO;

    //删除用户查询出来的订单，只保留当天的，前一天的全部删除
    @Override
    public void execute(JobExecutionContext jobExecutionContext) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try{
            boolean refused = PreCheckUtils.checkBackTaskAllowed(GlobalLimitConfigKey.BACK_TRADE_DELETE_JOB);
            if (refused) {
                logger.biz("###订单临时表删除已关闭###");
                return;
            }
            //每天凌晨删除上一天数据
            String outDate = sdf.format(new Date());

            int fkId = 0;
            //删除批量表
            for(int i=0; i<300; i++){
                try{
                    Long count = printTempdataplDAO.getOutDateTradesCount(outDate, i+"", fkId+"");

                    if(count >= 10000){
                        for(int k=0; k<count; k=k+10000){
                            printTempdataplDAO.deleteOutDateTrades(outDate, i+"", fkId+"");
                        }
                    }else{
                        printTempdataplDAO.deleteOutDateTrades(outDate, i+"", fkId+"");
                    }

                }catch (Exception e){
                    logger.error("删除批量订单表:"+i+" 失败:"+e.getMessage(), e);
                }
            }

            //删除非批量表
            for(int i=0; i<100; i++){
                try{
                    Long count = printTempdatafplDAO.getOutDateTradesCount(outDate, i+"", fkId+"");

                    if(count >= 10000){
                        for(int k=0; k<count; k=k+10000){
                            printTempdatafplDAO.deleteOutDateTrades(outDate, i+"", fkId+"");
                        }
                    }else{
                        printTempdatafplDAO.deleteOutDateTrades(outDate, i+"", fkId+"");
                    }

                }catch (Exception e){
                    logger.error("删除批量订单表:"+i+" 失败:"+e.getMessage(), e);
                }
            }
        }catch (Exception e){
            //整个删除订单任务失败,理论上需要告警了
            logger.error("删除订单任务失败:"+e.getMessage(), e);
        }


    }
}
