package com.kuaidizs.jxc.job.scheduler.rds;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import com.kuaidizs.jxc.common.util.dingTalk.SendDingTalkClient;
import com.kuaidizs.spymemcached.extend.listener.RayMemcacheClientUtil;
import com.raycloud.bizlogger.Logger;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

@Component
@JobHandler("fullInfoAPiCountHandler")
public class FullInfoAPiCountHandler extends IJobHandler {


    @Autowired
    private RayMemcacheClientUtil rayMemcacheClientUtil;

    private static final Logger logger = Logger.getLogger(FullInfoAPiCountHandler.class);


    @Override
    public ReturnT<String> execute(String s) throws Exception {

        String today = DateUtil.formatDate(new Date());

        String hour = DateUtil.hour(DateUtil.offsetHour(new Date(), -1).toJdkDate(), true) + "";
        if (Objects.equals(hour, "23")) {
            today = DateUtil.formatDate(DateUtil.yesterday());
        }
        Object freeUser = rayMemcacheClientUtil.get("12158997_DETAILCOUNT_" + today + "_" + hour + "_FREEUSER");
        Object gt30Day = rayMemcacheClientUtil.get("12158997_DETAILCOUNT_" + today + "_" + hour + "_GT30Day");
        Object gt60Day = rayMemcacheClientUtil.get("12158997_DETAILCOUNT_" + today + "_" + hour + "_GT60Day");
        Object tradeNull = rayMemcacheClientUtil.get("12158997_DETAILCOUNT_" + today + "_" + hour + "_TRADENULL");
        Object other = rayMemcacheClientUtil.get("12158997_DETAILCOUNT_" + today + "_" + hour + "_OTHER");


        Integer freeUserValue = Convert.convert(Integer.class, freeUser, 0);
        Integer gt30DayValue = Convert.convert(Integer.class, gt30Day, 0);
        Integer gt60DayValue = Convert.convert(Integer.class, gt60Day, 0);
        Integer tradeNullValue = Convert.convert(Integer.class, tradeNull, 0);
        Integer otherValue = Convert.convert(Integer.class, other, 0);


        StringBuilder sb = new StringBuilder(DateUtil.now());
        sb.append("【按小时统计】-详情走api：").append("\n")
                .append("免费用户调用：").append(freeUserValue).append("\n")
                .append("仅大于30天订单：").append(gt30DayValue).append("\n")
                .append("大于60天订单：").append(gt60DayValue).append("\n")
                .append("api也不存在订单：").append( tradeNullValue).append("\n")
                .append("未知情况：").append(otherValue).append("\n")
                .append("总计：").append(freeUserValue + gt30DayValue + gt60DayValue + tradeNullValue + otherValue);

        logger.info(sb.toString());
        SendDingTalkClient.sendDingTalkClient("https://oapi.dingtalk.com/robot/send?access_token=15141d2e792a3f8b43ffee14f739eba800176da677b19d5d5b13598bb4b268da", sb.toString());


        return ReturnT.SUCCESS;
    }
}
