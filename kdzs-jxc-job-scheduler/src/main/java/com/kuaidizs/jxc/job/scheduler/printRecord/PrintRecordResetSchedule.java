//package com.kuaidizs.jxc.job.scheduler.printRecord;
//
//import com.kuaidizs.jxc.common.util.LogHelper;
//import com.kuaidizs.jxc.common.util.SendDingdingUtils;
//import com.kuaidizs.jxc.common.util.dingTalk.SendDingTalkClient;
//import com.kuaidizs.jxc.dao.UserDAO;
//import com.kuaidizs.jxc.domain.User;
//import com.kuaidizs.jxc.domain.trade.PrintVersionRecord;
//import com.kuaidizs.jxc.query.UserQuery;
//import com.kuaidizs.jxc.service.trade.PrintVersionRecordService;
//import com.raycloud.bizlogger.Logger;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.util.ArrayList;
//import java.util.List;
//
///**
// * 每月1号执行一次.定时清空用户的打印数
// */
//@Component
//public class PrintRecordResetSchedule {
//
//    @Resource
//    private PrintVersionRecordService printVersionRecordService;
//    @Resource
//    private UserDAO userDAO;
//    private static final Logger logger = Logger.getLogger(PrintRecordResetSchedule.class);
//
//    //每月1号执行一次
//    @Scheduled(cron = "0 0 0 1 * ?")
//    public void schedule() {
//        logger.biz("开始执行用户重置打印记录数据>>>>>");
//        long start = System.currentTimeMillis();
//        int total = 0;
//        //获取用户数据
//        UserQuery userQuery = new UserQuery();
//        userQuery.setEnableStatus(true);
//        userQuery.setStartId(0L);
//        userQuery.setEndId(0L);
//        List<User> userList = new ArrayList<>();
//        try {
//            for (long lastId = userDAO.getLastId(); userQuery.getEndId() < lastId;) {
//                userQuery.setStartId(userQuery.getEndId() + 1L);
//                userQuery.setEndId(userQuery.getEndId() + 500L);
//                userList = userDAO.getUserList(userQuery);
//                resetPrintVersion(userList);
//                logger.biz("用户重置打印记录数据，startId:{}，endId:{}，size:{}", userQuery.getStartId(), userQuery.getEndId(), total += userList.size());
//            }
//        } catch (Exception e) {
//            logger.error("用户重置打印记录数据异常，startId:{}，endId:{}，size:{}", userQuery.getStartId(), userQuery.getEndId(), total += userList.size(), e);
//            String msg = String.format("用户重置打印记录数据异常，startId:%s，endId:%s，size:%s", userQuery.getStartId(), userQuery.getEndId(), total);
//            SendDingdingUtils.sendDingDing(msg, SendDingTalkClient.MSG_DING_URL);
//        }
//        logger.biz("用户重置打印记录数据结束，总共取消用户数为：" + total + "，耗时：" + (System.currentTimeMillis() - start) / 1000 + " s");
//    }
//
//    private void resetPrintVersion(List<User> users) {
//
//        for (User user : users) {
//            try {
//                logger.biz(LogHelper.buildLogHead(user).append("开始重置打印记录数据").toString());
//                PrintVersionRecord printVersionRecord = printVersionRecordService.getPrintVersionRecordByTaobaoId(user);
//                if (printVersionRecord == null || printVersionRecord.getStandardPrintNums() == null
//                        || printVersionRecord.getStandardPrintNums() == 0) {
//                    logger.biz(LogHelper.buildLogHead(user).append("用户数据为空不做更新").toString());
//                    continue;
//                }
//                PrintVersionRecord updRecord = new PrintVersionRecord();
//                updRecord.setTaobaoId(printVersionRecord.getTaobaoId());
//                updRecord.setStandardPrintNums(0L);
//                printVersionRecordService.resetStandardPrintNums(user, updRecord);
//                logger.biz(LogHelper.buildLogHead(user).append("重置打印记录数据成功").toString());
//            } catch (Exception e) {
//                logger.biz(LogHelper.buildLogHead(user).append("用户数据异常 , msg:").append(e.getMessage()).toString());
//            }
//        }
//    }
//
//
//}
