package com.kuaidizs.jxc.job.scheduler.waybillUseLog;

import com.kuaidizs.jxc.common.util.DateUtil;
import com.kuaidizs.jxc.common.util.diamond.GlobalLimitConfigKey;
import com.kuaidizs.jxc.common.util.diamond.PreCheckUtils;
import com.kuaidizs.jxc.dao.share.WaybillUseLogDAO;
import com.kuaidizs.jxc.domain.share.WaybillUseLogQuery;
import com.raycloud.bizlogger.Logger;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * @auther xudaomeng
 * @since 2020-05-27 15:14
 */
@Component
public class OldWaybillUseLogCleanSchedule {

    private final Logger logger = Logger.getLogger(OldWaybillUseLogCleanSchedule.class);

    private static final int PAGE_SIZE = 5000;
    private static final int FK_SIZE = 24;
    private static final int TABLE_SIZE = 20;//共20张表

    private static final String TIME_START = "01:00:00";
    private static final String TIME_END = "06:00:00";

    @Resource
    private WaybillUseLogDAO waybillUseLogDAO;

//    @Scheduled(cron = "0 0 1 ? * THU")
    public void cleanOldData() {
        boolean flag = true;
        if (flag) {
            return;
        }
        boolean refused = PreCheckUtils.checkBackTaskAllowed(GlobalLimitConfigKey.BACK_TASK_CLOSE_DAY);
        if (refused) {
            return;
        }

        Date date = new Date();
//        if (!DateUtil.isInDate(date, TIME_START, TIME_END)) {//6点停止执行
//            return;
//        }

        Date endDate = DateUtils.addMonths(new Date(), -4);//删除4个月前数据
        for (int i = 0; i < FK_SIZE; i++) {
            for (int tb = 0; tb < TABLE_SIZE; tb++) {

                if (!DateUtil.isInDate(date, TIME_START, TIME_END)) {//6点停止执行
                    return;
                }

                try {
                    WaybillUseLogQuery waybillUseLogQuery = new WaybillUseLogQuery();
                    waybillUseLogQuery.setEndTime(endDate);
                    waybillUseLogQuery.setFkId(String.valueOf(i));
                    waybillUseLogQuery.setTableName(String.valueOf(tb));
                    Integer count = waybillUseLogDAO.getShareUseRecordCountSchedule(waybillUseLogQuery);
                    if (count > PAGE_SIZE) {
                        for (int pageNo = 1; pageNo <= (count + PAGE_SIZE - 1) / PAGE_SIZE; pageNo++) {
                            try {
                                waybillUseLogQuery.setPage(pageNo);
                                waybillUseLogQuery.setPageSize(PAGE_SIZE);
                                Integer result = waybillUseLogDAO.moveShareUseRecordToCoolData(waybillUseLogQuery);
                                logger.biz("大于5000行记录 迁移单号分享记录 " + i + "." + tb
                                        + " 开始行号：" + waybillUseLogQuery.getStartRow()
                                        + " 结束行号：" + waybillUseLogQuery.getEndRow()
                                        + " count：" + count
                                        + " result:" + result);
                            } catch (Exception e) {
                                logger.error("###迁移单号分享记录 插入数据库失败  " + i + "." + tb + "，失败原因:" + e.getMessage(), e);
                            }
                        }
                    } else {
                        try {
                            Integer result = waybillUseLogDAO.moveShareUseRecordToCoolData(waybillUseLogQuery);
                            logger.biz("小于5000行记录 迁移单号分享记录   " + i + "." + tb + " count：" + count
                                    + " result :" + result);
                        } catch (Exception e) {
                            logger.error("###迁移单号分享记录 插入数据库失败 " + i + "." + tb + "，失败原因:" + e.getMessage(), e);
                        }
                    }


                    //删除4个月前的数据
                    waybillUseLogQuery.setPage(1);
                    waybillUseLogQuery.setPageSize(PAGE_SIZE);
                    if (count >= PAGE_SIZE) {
                        for (int pageNo = 1; pageNo <= (count + PAGE_SIZE - 1) / PAGE_SIZE; pageNo++) {
                            Integer result = waybillUseLogDAO.deleteShareUseRecord(waybillUseLogQuery);
                            logger.biz("大于5000行记录 删除单号分享记录" + i + "." + tb + " count：" + count
                                    + " result:" + result);
                        }
                    } else {
                        Integer result = waybillUseLogDAO.deleteShareUseRecord(waybillUseLogQuery);
                        logger.biz("小于5000行记录 删除单号分享记录" + i + "." + tb + " count：" + count
                                + " result:" + result);
                    }
                } catch (Exception e) {
                    logger.error("删除4个月前单号分享记录失败" + i + "." + tb + "，失败原因：" + e.getMessage(), e);
                }
            }
        }
    }
}
