package com.kuaidizs.jxc.job.scheduler;

import com.kuaidizs.jxc.dao.download.DownTaskDAO;
import com.kuaidizs.jxc.query.download.DownTaskQuery;
import com.raycloud.bizlogger.Logger;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;

/**
 * 下载中心数据迁移任务
 *
 * @author: qingfeng.cxb
 * @create: 2018-09-11 14:00
 */
//@Component
public class MoveDownTaskDataJob {

    public static final Logger logger = Logger.getLogger(MoveDownTaskDataJob.class);
    //单次迁移量为5000
    private static final int PAGE_SIZE = 5000;
    @Resource
    DownTaskDAO downTaskDAO;

//    @Scheduled(cron = "0 0 2 ? * THU")
    public void execute() {
        try {
            Calendar date = Calendar.getInstance();
            //设为三月前
            date.add(Calendar.MONTH, -3);
            //设为当前月的1号
            date.set(Calendar.DATE, 1);

            moveDownTask(date.getTime());
        } catch (Exception e) {
            logger.error("###迁移冷库数据失败，失败原因:" + e.getMessage(), e);
        }
    }

    /**
     * 迁移下载中心数据
     */
    public void moveDownTask(Date moveData) {
        DownTaskQuery downTaskQuery = new DownTaskQuery();
        downTaskQuery.setCreatedEnd(moveData);
        Integer count = downTaskDAO.getDownTaskListCount(downTaskQuery);
        try {
            //迁移任务
            if (count >= PAGE_SIZE) {
                for (int pageNo = 1; pageNo <= (count + PAGE_SIZE - 1) / PAGE_SIZE; pageNo++) {
                    downTaskQuery.setPageSize(PAGE_SIZE);
                    downTaskQuery.setPage(pageNo);

                    Integer result = downTaskDAO.moveDownTaskData(downTaskQuery);
                    logger.biz("大于5000行记录 迁移下载中心任务记录 "
                            + " 开始行号：" + downTaskQuery.getStartRow()
                            + " 结束行号：" + downTaskQuery.getEndRow()
                            + " count：" + count);
                }
            } else {
                Integer result = downTaskDAO.moveDownTaskData(downTaskQuery);
                logger.biz("小于5000行记录 迁移下载中心任务记录 count：" + count);
            }
        } catch (Exception e) {
            logger.error("###迁移下载中心任务记录，失败原因:" + e.getMessage(), e);
        }
        try {
            if (count >= PAGE_SIZE) {
                for (int pageNo = 1; pageNo <= (count + PAGE_SIZE - 1) / PAGE_SIZE; pageNo++) {
                    downTaskQuery.setPageSize(PAGE_SIZE);
                    downTaskQuery.setPage(pageNo);
                    Integer result = downTaskDAO.deleteDownTaskData(downTaskQuery);
                    logger.biz("大于5000行记录 删除下载中心任务记录 "
                            + " 开始行号：" + downTaskQuery.getStartRow()
                            + " 结束行号：" + downTaskQuery.getEndRow()
                            + " count：" + count);
                }
            } else {
                Integer result = downTaskDAO.deleteDownTaskData(downTaskQuery);
                logger.biz("小于5000行记录 删除下载中心任务记录 count：" + count);
            }
        } catch (Exception e) {
            logger.error("###删除下载中心任务记录，失败原因:" + e.getMessage(), e);
        }
    }

}
