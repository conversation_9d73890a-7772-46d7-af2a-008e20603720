package com.kuaidizs.jxc.job.scheduler;

import com.kuaidizs.jxc.common.util.diamond.GlobalLimitConfigKey;
import com.kuaidizs.jxc.common.util.diamond.PreCheckUtils;
import com.kuaidizs.jxc.query.trade.YfhOrderlistQuery;
import com.kuaidizs.jxc.service.trade.YfhOrderlistService;
import com.raycloud.bizlogger.Logger;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;

/**
 * 预发货数据迁移备份job.
 * User: <EMAIL>
 * Date: 2017/5/23
 * Time: 上午11:04
 */
@DisallowConcurrentExecution
public class MoveYfhDataJob implements Job {

    public static final Logger logger = Logger.getLogger(MoveYfhDataJob.class);

    private static final int PAGE_SIZE = 5000;  //迁移量为5000

    @Resource
    private YfhOrderlistService yfhOrderlistService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        boolean flag = true;
        if (flag) {
            return;
        }
        try {
            boolean refused = PreCheckUtils.checkBackTaskAllowed(GlobalLimitConfigKey.BACK_TASK_CLOSE_DAY);
            if (refused) {
                logger.biz("###执行预发货数据迁移备份任务已关闭###");
                return;
            }
            logger.biz("###执行预发货数据迁移备份任务###");
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MONTH, -3);
            calendar.set(Calendar.DATE, 1);//设为当前月的1号
            int fkId = 0;

            yfhOrderlistService.setMovingData(true);    //正在执行迁移任务，暂停自动发货任务
            YfhOrderlistQuery yfhOrderlistQuery = new YfhOrderlistQuery();
            yfhOrderlistQuery.setModifiedEnd(calendar.getTime());
            yfhOrderlistQuery.setFkId(String.valueOf(fkId));
            Integer totalCount = yfhOrderlistService.getMoveYfhOrderCount(yfhOrderlistQuery);
            moveYfhData(calendar.getTime(), fkId, totalCount);
            deleteMovedYfhData(calendar.getTime(), fkId, totalCount);
            logger.biz("预发货数据迁移备份成功量：" + totalCount);
        } catch (Exception e) {
            logger.error("###预发货数据迁移备份失败: " + e.getMessage(), e);
        } finally {
            yfhOrderlistService.setMovingData(false);//迁移任务执行结束，取消对自动发货任务的限制
        }
    }

    /***
     * 迁移部分预发货数据
     * @param endDate 查询条件
     * @param totalCount 迁移备份总行数
     */
    private void moveYfhData(Date endDate, int fkId, int totalCount) {
        YfhOrderlistQuery yfhOrderlistQuery = new YfhOrderlistQuery();
        yfhOrderlistQuery.setModifiedEnd(endDate);
        yfhOrderlistQuery.setFkId(String.valueOf(fkId));
        //迁移三个月之前的数据
        if (totalCount >= PAGE_SIZE) {
            for (int pageNo = 1; pageNo <= (totalCount + PAGE_SIZE - 1) / PAGE_SIZE; pageNo++) {
                yfhOrderlistQuery.setPage(pageNo);
                yfhOrderlistQuery.setPageSize(PAGE_SIZE);
                Integer result = yfhOrderlistService.moveYfhCoolData(yfhOrderlistQuery);
                logger.biz("大于5000行记录 迁移预发货记录 yhf_orderlist "
                        + " 开始行号：" + yfhOrderlistQuery.getStartRow()
                        + " 结束行号：" + yfhOrderlistQuery.getEndRow()
                        + " result：" + result);
            }
        } else {
            int result = yfhOrderlistService.moveYfhCoolData(yfhOrderlistQuery);
            logger.biz("小于5000行记录 迁移预发货记录 yhf_orderlist " + " result：" + result);
        }
    }

    /***
     * 删除三个月之前的数据
     * @param endDate 查询条件
     * @param totalCount 删除总行数
     */
    private void deleteMovedYfhData(Date endDate, int fkId, int totalCount) {
        YfhOrderlistQuery yfhOrderlistQuery = new YfhOrderlistQuery();
        yfhOrderlistQuery.setModifiedEnd(endDate);
        yfhOrderlistQuery.setFkId(String.valueOf(fkId));
        if (totalCount >= PAGE_SIZE) {
            for (int pageNo = 1; pageNo <= (totalCount + PAGE_SIZE - 1) / PAGE_SIZE; pageNo++) {
                yfhOrderlistQuery.setPage(pageNo);
                yfhOrderlistQuery.setPageSize(PAGE_SIZE);
                Integer result = yfhOrderlistService.delMovedYfhCoolData(yfhOrderlistQuery);
                logger.biz("大于5000行记录 删除预发货记录 yhf_orderlist " + " result：" + result);
            }
        } else {
            Integer result = yfhOrderlistService.delMovedYfhCoolData(yfhOrderlistQuery);
            logger.biz("小于5000行记录 删除预发货记录 yhf_orderlist " + " result：" + result);
        }
    }
}
