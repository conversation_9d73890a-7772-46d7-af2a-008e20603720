package com.kuaidizs.jxc.job.scheduler;

import com.kuaidizs.jxc.common.util.SendDingdingUtils;
import com.raycloud.api.msgjob.domain.MsgCount;
import com.raycloud.api.msgjob.service.MsgCountRequest;
import com.raycloud.bizlogger.Logger;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @create 2021-04-27 4:59 下午
 */
@Component
public class MsgCheackJob {

    public static final Logger logger = Logger.getLogger(MsgCheackJob.class);
    private static final int REMAIN_WARN_COUNT = 30000;

    @Resource
    MsgCountRequest msgCountRequest;

    @Scheduled(cron = "0 0 12 * * ?")
    public void execute() throws Exception {
        MsgCount tbMsgCount = SendDingdingUtils.getTbMsgCount(msgCountRequest);
        MsgCount zzMsgCount = SendDingdingUtils.getZzMsgCount(msgCountRequest);
        int tbRemain = tbMsgCount.getMsgCount();
        int zzRemain = zzMsgCount.getMsgCount();
        if (tbRemain <= REMAIN_WARN_COUNT || zzRemain <= REMAIN_WARN_COUNT) {
            String msg = "剩余短信数量不足" + REMAIN_WARN_COUNT + "告警!" + "\n" + "淘宝现剩余短信数量：" + tbRemain + "\n" + "自助版现剩余短信数量：" + zzRemain + "\n"+ "需要充值啦！！！";
            SendDingdingUtils.sendDingDing(msg, "https://oapi.dingtalk.com/robot/send?access_token=e910b104ffd0a51c40fd76e4fa2ddb9e621b8bb14d24d2b009dcfaa73424706d");
        }

    }

}
