package com.kuaidizs.jxc.job.scheduler;

import com.kuaidizs.jxc.common.util.DateUtil;
import com.kuaidizs.jxc.service.moveTask.MoveTaskDIngTalkService;
import com.raycloud.bizlogger.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 迁移信息钉钉推送定时任务
 */
@Component
public class MoveInfoDIngPushSchedule {

    private final Logger logger = Logger.getLogger(MoveInfoDIngPushSchedule.class);

    @Autowired
    private MoveTaskDIngTalkService moveTaskDIngTalkService;

    /**
     * 每天10点定时执行
     */
    @Scheduled(cron = "0 0 10 * * ?")
    public void schedule() {
        String today = null;
        try {
            today = DateUtil.convertDateToStr(new Date(), "yyyy-MM-dd");
            List<String> dayTimeList = new ArrayList<>();
            dayTimeList.add(today);
            moveTaskDIngTalkService.sendMoveErrorMsgToDIngTalk(dayTimeList);
            logger.biz("已推送迁移错误信息到钉钉.dayTime:{}", today);
        } catch (Exception e) {
            logger.error("推送迁移错误信息错误.dayTime:{}", today, e);
        }
    }
}