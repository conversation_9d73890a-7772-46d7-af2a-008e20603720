package com.kuaidizs.jxc.job.scheduler.sf;

import com.kuaidizs.jxc.common.util.DataBaseUtil;
import com.kuaidizs.jxc.dao.UserDAO;
import com.kuaidizs.jxc.dao.print.SfChildMotherRelationDAO;
import com.kuaidizs.jxc.dao.print.SfQianYiUserDAO;
import com.kuaidizs.jxc.domain.User;
import com.kuaidizs.jxc.domain.print.SfChildMotherRelation;
import com.kuaidizs.jxc.domain.print.SfQianYiUser;
import com.kuaidizs.jxc.query.UserQuery;
import com.raycloud.bizlogger.Logger;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.SQLException;
import java.util.Date;
import java.util.List;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @Intro:
 * @Author: WangJiongDa(yunkai)
 * @Date: 2018/11/28
 * @Time: 下午5:35
 */
//@Component
public class SFQiYiSchedule {

    private static Logger logger = Logger.getLogger(SFQiYiSchedule.class);

    private static final Integer pageSize = 1000;

    /**
     * 起始提前迁移的id值，到时候需要根据库中数据及时更改。
     */
    private static final Integer id = 1;

    private static ThreadPoolExecutor dealExecutor = new ThreadPoolExecutor(4, 4, 50, TimeUnit.MILLISECONDS, new LinkedBlockingDeque<Runnable>(), new ThreadPoolExecutor.DiscardOldestPolicy());

    @Resource
    private SfChildMotherRelationDAO sfChildMotherRelationDAO;

    @Resource
    private UserDAO userDAO;

    @Resource
    private SfQianYiUserDAO sfQianYiUserDAO;

    //@Scheduled(cron="0/10 * *  * * ? ")
    public void schedule() {
        try {
            logger.biz("顺丰网点迁移老程序分库分表任务开始...");
            UserQuery userQuery = new UserQuery();
            List<User> users = userDAO.getUserList(userQuery, false);
            logger.biz("users size = {}", users.size());
            long startTime = System.currentTimeMillis();
            if (CollectionUtils.isNotEmpty(users)) {
                for (final User user : users) {
                    dealExecutor.execute(new Runnable() {
                        @Override
                        public void run() {
                            try {
                                int startRow = 0;
                                String fkId = DataBaseUtil.getDbConf(user.getDbConfig(), DataBaseUtil.DB);
                                String tableName = DataBaseUtil.initValue(user.getTaobaoId(), DataBaseUtil.SF_CHILD_MOTHER_RELATION_SPLIT) + "";
                                while (true) {
                                    List<SfChildMotherRelation> sfChildMotherRelations = sfChildMotherRelationDAO.queryByPage(1, id, user.getTaobaoId(), startRow, pageSize);
                                    if (CollectionUtils.isNotEmpty(sfChildMotherRelations)) {
                                        for (SfChildMotherRelation sfChildMotherRelation : sfChildMotherRelations) {
                                            try {
                                                sfChildMotherRelationDAO.addSfChildMotherRelation(sfChildMotherRelation, tableName, fkId);
                                            } catch (SQLException e) {
                                                logger.error("失败的数据id : {}", sfChildMotherRelation.getId());
                                            }
                                        }
                                    } else {
                                        break;
                                    }
                                    if (CollectionUtils.isNotEmpty(sfChildMotherRelations) && sfChildMotherRelations.size() < pageSize) {
                                        break;
                                    }
                                    startRow = startRow + sfChildMotherRelations.size();
                                }
                            } catch (Exception e) {
                                //出错时，保留用户数据
                                logger.biz("失败的user的taobaoId : {}", user.getTaobaoId());
                                SfQianYiUser sfQianYiUser = new SfQianYiUser();
                                sfQianYiUser.setCreated(new Date());
                                sfQianYiUser.setTaobaoId(user.getTaobaoId());
                                sfQianYiUserDAO.addSfQianYiUser(sfQianYiUser);
                            }
                        }
                    });
                }
            }
            long endTime = System.currentTimeMillis();
            logger.biz("执行时间 = {}", endTime - startTime);
        } catch (Exception e) {
            logger.error("顺丰网点迁移老程序分库分表失败 error = {}", e.getMessage(), e);
        }
    }
}
