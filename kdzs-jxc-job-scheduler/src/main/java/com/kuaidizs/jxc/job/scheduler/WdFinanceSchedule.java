package com.kuaidizs.jxc.job.scheduler;

import com.kuaidizs.jxc.common.db.Result;
import com.kuaidizs.jxc.common.util.LogHelper;
import com.kuaidizs.jxc.common.util.diamond.GlobalLimitConfigKey;
import com.kuaidizs.jxc.common.util.diamond.PreCheckUtils;
import com.kuaidizs.jxc.domain.User;
import com.kuaidizs.jxc.domain.trade.DzmdYzLognumber;
import com.kuaidizs.jxc.domain.trade.YdInfo;
import com.kuaidizs.jxc.domain.user.UserGroupConnection;
import com.kuaidizs.jxc.msg.WdFinanceMsgDao;
import com.kuaidizs.jxc.query.trade.DzmdYzLognumberQuery;
import com.kuaidizs.jxc.service.UserService;
import com.kuaidizs.jxc.service.trade.DzmdYzLognumberService;
import com.kuaidizs.jxc.service.user.UserGroupConnectionService;
import com.kuaidizs.jxc.taobaoapi.apibean.KdInfoVo;
import com.kuaidizs.jxc.taobaoapi.apibean.TradeInfoVo;
import com.raycloud.bizlogger.Logger;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 补偿程序，每天重跑当天的数据推送到网点
 */
@Component
public class WdFinanceSchedule {

    @Resource
    private UserGroupConnectionService userGroupConnectionService;
    @Resource
    private UserService userService;
    @Resource
    private DzmdYzLognumberService dzmdYzLognumberService;
    @Resource
    private WdFinanceMsgDao wdFinanceMsgDao;

    private static final Long groupId = 334724657L;

    private final Logger logger = Logger.getLogger(WdFinanceSchedule.class);

    @Scheduled(cron = "0 0 0 * * ?")
    public void execute() {
        try {
            boolean refused = PreCheckUtils.checkBackTaskAllowed(GlobalLimitConfigKey.BACK_TASK_CLOSE_PERIOD);
            if (refused) {
                return;
            }
            logger.biz("开始定时推送网点数据完成》》》》》");
            Date end = new Date();
            Date start = DateUtils.addDays(end, -1);
            List<UserGroupConnection> groups = userGroupConnectionService.getUserGroupConnectionByGroupId(groupId);
            for (UserGroupConnection userGroupConnection : groups) {
                User user = userService.getBaseUserByTaobaoId(userGroupConnection.getTaobaoId());
                pushMsgByUser(user, end, start);
            }
            logger.biz("定时推送网点数据完成》》》》》");
        } catch (Exception e) {
            logger.error("定时推送网点数据异常, msg: " + e.getMessage(), e);
        }
    }

    private void pushMsgByUser(User user, Date end, Date start) {
        DzmdYzLognumberQuery dzmdYzLognumberQuery = new DzmdYzLognumberQuery();
        dzmdYzLognumberQuery.setCreatedStart(start);
        dzmdYzLognumberQuery.setCreatedEnd(end);
        int page = 1;
        dzmdYzLognumberQuery.setPage(page);
        dzmdYzLognumberQuery.setPageSize(200);
        dzmdYzLognumberQuery.setKdCode("YTO");
        dzmdYzLognumberQuery.setTaobaoId(user.getTaobaoId());
        dzmdYzLognumberQuery.setUseTaobaoId(334724657L);

        Result<DzmdYzLognumber> result = dzmdYzLognumberService.getDzmdYzLognumberListWithPageFields(user, dzmdYzLognumberQuery);
        while (result != null && CollectionUtils.isNotEmpty(result.getList())) {
            logger.biz(LogHelper.buildLogHead(user).append("重新推送共享单号数据 size:").append(result.getList().size()).toString());
            for (DzmdYzLognumber dzmdYzLognumber : result.getList()) {
                if (!StringUtils.equals("YTO", dzmdYzLognumber.getKdCode())) {
                    continue;
                }
                push(dzmdYzLognumber, user);
            }
            dzmdYzLognumberQuery.setPage(dzmdYzLognumberQuery.getPage() + 1);
            result = dzmdYzLognumberService.getDzmdYzLognumberListWithPageFields(user, dzmdYzLognumberQuery);
            if (CollectionUtils.isEmpty(result.getList())) {
                break;
            }
        }
        logger.biz(LogHelper.buildLogHead(user).append("重新推送共享单号数据  结束").toString());
    }

    private void push(DzmdYzLognumber dzmdYzLognumber, User user) {

        TradeInfoVo tradeInfoVo = new TradeInfoVo();
        KdInfoVo kdInfoVo = new KdInfoVo();
        YdInfo ydInfo = new YdInfo();

        kdInfoVo.setBranchCode("571020");
        kdInfoVo.setBranchName("浙江省杭州市钱江新城");
        kdInfoVo.setKdCode("YTO");
        kdInfoVo.setKdName("圆通");
        kdInfoVo.setUseTaoBaoId(dzmdYzLognumber.getUseTaobaoId() + "");
        kdInfoVo.setUseTaobaoNick("fgq20091108");

        tradeInfoVo.setYdNo(dzmdYzLognumber.getYdNo());
        tradeInfoVo.setTids(dzmdYzLognumber.getTids());
        tradeInfoVo.setReceiverName(dzmdYzLognumber.getReceiverName());
        tradeInfoVo.setSenderAddressDetail("xxxxx");
        tradeInfoVo.setTaobaoId(user.getTaobaoId() + "");
        ydInfo.setYdId(dzmdYzLognumber.getYdNo());

        wdFinanceMsgDao.pushApplyMsg(tradeInfoVo, kdInfoVo, ydInfo);
        logger.biz(LogHelper.buildLogHead(user).append("推送网点数据,ydNo:").append(dzmdYzLognumber.getYdNo()).toString());
    }
}
