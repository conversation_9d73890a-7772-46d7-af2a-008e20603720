package com.kuaidizs.jxc.job.scheduler.voiceRecall;

import com.kuaidizs.jxc.common.db.Result;
import com.kuaidizs.jxc.common.nacos.NacosConfigKey;
import com.kuaidizs.jxc.common.util.DateUtil;
import com.kuaidizs.jxc.common.util.diamond.GlobalLimitConfigKey;
import com.kuaidizs.jxc.common.util.diamond.PreCheckUtils;
import com.kuaidizs.jxc.domain.trade.PrintVersionRecord;
import com.kuaidizs.jxc.query.trade.PrintVersionRecordQuery;
import com.kuaidizs.jxc.service.trade.PrintVersionRecordService;
import com.kuaidizs.jxc.service.voiceRecall.VoiceRecallService;
import com.raycloud.bizlogger.Logger;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 按天发送语音召回功能
 */
@Component
public class SendVoiceRecallDay {

    private final Logger logger = Logger.getLogger(SendVoiceRecallDay.class);


    private static final SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

    @Resource
    private PrintVersionRecordService printVersionRecordService;

    @Resource
    private VoiceRecallService voiceRecallService;

    //每天的11点发送
    //@Scheduled(cron = "0 55 19 * * ?")
    //@Scheduled(cron = "0 0 11 * * ?")
    public void sendVoiceDay() {
/*
        boolean limit = PreCheckUtils.checkBackTaskAllowed(GlobalLimitConfigKey.BACK_TASK_CLOSE_DAY);
        if (limit) {
            return;
        }

        List<String> topVipList = new ArrayList<>();
        topVipList.add("1");
        topVipList.add("3");
        topVipList.add("5");

        Date date = new Date();
        String version = simpleDateFormat.format(date);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        int dayToMonth = calendar.get(Calendar.DAY_OF_MONTH);
        //计算查询的开始时间,拿到前10天到期到后5天到期的数据
       *//* Date topExpiredStart = DateUtil.getDayStart(date, -10);
        if(dayToMonth == 1) {
            topExpiredStart = DateUtil.getDayStart(date, -32);
        }*//*
        //尝试每天都跑
        Date topExpiredStart = DateUtil.getDayStart(date, -32);

        Date topExpiredEnd = DateUtil.getDayStart(date, 5);
        Integer pageSize = 1000;
        //查看当前到期用户
        for (int i = 0; i < 24; i++) {
            Integer pageNo = 1;
            PrintVersionRecordQuery printVersionRecordQuery = new PrintVersionRecordQuery();
            printVersionRecordQuery.setTopVipList(topVipList);
            printVersionRecordQuery.setTopExpiredStart(topExpiredStart);
            printVersionRecordQuery.setTopExpiredEnd(topExpiredEnd);
            printVersionRecordQuery.setPage(pageNo);
            printVersionRecordQuery.setPageSize(pageSize);
            printVersionRecordQuery.setFkId(String.valueOf(i));
            Result<PrintVersionRecord> rs = printVersionRecordService.getPrintVersionRecordListWithPage(printVersionRecordQuery, true);
            int count = rs.getCount();
            if (count > pageSize) {
                while (pageNo * pageSize <= count) {
                    pageNo++;
                    printVersionRecordQuery.setPage(pageNo);
                    Result<PrintVersionRecord> pageRs = printVersionRecordService.getPrintVersionRecordListWithPage(printVersionRecordQuery, false);
                    List<PrintVersionRecord> printVersionRecordList = pageRs.getList();
                    if (CollectionUtils.isNotEmpty(printVersionRecordList)) {
                        for (PrintVersionRecord printVersionRecord : printVersionRecordList) {
                            //用户不为空，且不在黑名单中，则进行语音召回
                            if (printVersionRecord.getTaobaoId() != null &&
                                    (CollectionUtils.isEmpty(NacosConfigKey.voiceRecallBlackList) || !NacosConfigKey.voiceRecallBlackList.contains(printVersionRecord.getTaobaoId().toString()))) {
                                sendVoice(printVersionRecord, version, date);
                                sendMsg(printVersionRecord, version, date);
                            }
                        }
                    }
                }
            }
            List<PrintVersionRecord> printVersionRecords = rs.getList();
            if (CollectionUtils.isNotEmpty(printVersionRecords)) {
                for (PrintVersionRecord printVersionRecord : printVersionRecords) {
                    sendVoice(printVersionRecord, version, date);
                    sendMsg(printVersionRecord, version, date);
                }
            }
        }*/
    }

    private void sendVoice(PrintVersionRecord printVersionRecord, String version, Date date) {
        try {
            // 限制发送语音时间11:00 - 12:00
            Date startTime = DateUtil.setDateByHms(11, 0, 0);
            Date endTime = DateUtil.setDateByHms(12, 0, 0);
            if (date.before(startTime) || date.after(endTime)) {
                logger.warn("语音召回任务未在指定时间内调度，请注意");
                return;
            }

            int type = 0;

            String vip = printVersionRecord.getTopVip();
            //用户过期时间
            Date topExpired = printVersionRecord.getTopExpired();
            if (topExpired == null || vip == null) {
                return;
            }

            long day = DateUtil.getDayBetweens(topExpired, date);
            if (day == -3 && ("1".equals(vip) || "3".equals(vip))) {
                type = 1;
            } else if (day == -3 && "5".equals(vip)) {
                type = 2;
            } else if (day == 15) {
                type = 3;
            } else if (day == 30) {
                type = 4;
            }
            voiceRecallService.sendVoiceUser(printVersionRecord.getTaobaoId(), version, type);
        } catch (Exception e) {
            logger.error(printVersionRecord.getTaobaoId() + " 发送语音失败：" + e.getMessage());
        }
    }

    private void sendMsg(PrintVersionRecord printVersionRecord, String version, Date date) {
        try {

            // 限制发送语音时间11:00 - 12:00
            Date startTime = DateUtil.setDateByHms(11, 0, 0);
            Date endTime = DateUtil.setDateByHms(12, 0, 0);
            if (date.before(startTime) || date.after(endTime)) {
                logger.warn("短信召回任务未在指定时间内调度，请注意");
                return;
            }

            int userType = 0;

            String vip = printVersionRecord.getTopVip();
            //用户过期时间
            Date topExpired = printVersionRecord.getTopExpired();
            if (topExpired == null || vip == null) {
                return;
            }
            long day = DateUtil.getDayBetweens(topExpired, date);
            //非专业版用户
            if ("1".equals(vip) || "3".equals(vip)) {
                if (day == -5) {
                    userType = 1; //5天后即将过期的非专业版用户
                } else if (day == -1) {
                    userType = 2; //1天后即将过期的非专业版用户
                } else if (day == 3) {
                    userType = 3; //过期3天后即将过期的非专业版用户
                } else if (day == 7) {
                    userType = 4; //过期7天后即将过期的非专业版用户
                } else if (day == 15) {
                    userType = 5; //过期15天后即将过期的非专业版用户
                } else if (day == 30) {
                    userType = 6; //过期30天后即将过期的非专业版用户
                }
            } //专业版用户
            else if ("5".equals(vip)) {
                if (day == -5) {
                    userType = 7; //5天后即将过期的专业版用户
                } else if (day == -1) {
                    userType = 8; //1天后即将过期的专业版用户
                } else if (day == 3) {
                    userType = 9; //过期3天后即将过期的专业版用户
                } else if (day == 7) {
                    userType = 10; //过期7天后即将过期的专业版用户
                } else if (day == 15) {
                    userType = 11; //过期15天后即将过期的专业版用户
                } else if (day == 30) {
                    userType = 12; //过期30天后即将过期的专业版用户
                }
            }
            voiceRecallService.sendMsgUser(printVersionRecord.getTaobaoId(), version, userType);
        } catch (Exception e) {
            logger.error(printVersionRecord.getTaobaoId() + " 发送语短信失败：" + e.getMessage());
        }
    }
}
