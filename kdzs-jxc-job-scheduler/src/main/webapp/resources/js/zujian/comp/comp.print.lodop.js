﻿/// <reference path="comp.print.base.js" />
/// <reference path="comp.Print.js" />
/// <reference path="comp.print.resources.js" />
(function (window) {
    var lodopObj = nameSpace("comp.print.lodop");
    var hiddenLodop = null;

    //初始化方法
    lodopObj.init = function (callback) {
        var id = "sysHidden_" + (+new Date);
        var fn = new comp.Print.FN();
        hiddenLodop = fn.createLodopDom(null, id, 0, 0);
        lodopObj.dom = hiddenLodop;
        lodopObj.isInit = true;
       // console.info("comp.print.lodop.init");
        callback();
    };

    //获取隐藏的Lodop
    lodopObj.getHiddenLodop = function () {
        return lodopObj.dom;
    };

    //获取最新ID
    lodopObj.getNewLodopKey = function (lodop) {
        var key = getRandomNum();
        with (lodop.GET_VALUE("ItemExist", key)) {
            key = getRandomNum();
        }
        return key;
    };

    //指定lodop背景图
    lodopObj.setLodopBgImg = function (lodop, imgSrc) {
        lodop.ADD_PRINT_SETUP_BKIMG("<img border='0' src='" + imgSrc + "'>"); //指定背景图
    };

    //获取所有打印机
    lodopObj.getPrinters = function () {
        var arr = [];
        var PrinterCount = hiddenLodop.GET_PRINTER_COUNT();
        for (var i = 0; i < PrinterCount; i++) {
            var printname = hiddenLodop.GET_PRINTER_NAME(i);
            if (printname != "Fax" && printname != "Microsoft Office Document Image Writer") {
                arr.push(printname);
            }
        }
        return arr;
    };


    //获取默认打印机
    lodopObj.getDefaultPrinter = function () {
        var defaultP = hiddenLodop.GET_PRINTER_NAME(-1);
        console.log('组件获取电脑设置的默认打印机为:'+ defaultP);
        return defaultP;
    };

    //验证菜鸟控件是否安装或需要升级
    lodopObj.checkLodop = function () {
        var installObj = comp.print.resources.install;
        try {
            if ((hiddenLodop == null) || (typeof (hiddenLodop.VERSION) == "undefined")) {
                if (brower.isFF) {
                    return installObj.strHtmFireFox;
                } else if (brower.is64IE) {
                    return installObj.strHtm64_Install;
                } else if (brower.isIE) {
                    return installObj.strHtmInstall;
                } else {
                    return installObj.strHtmInstall;
                }
            }
            else if (hiddenLodop.VERSION < "1.0.2.2") {
                if (brower.is64IE) {
                    return installObj.strHtm64_Update;
                } else if (brower.isIE) {
                    return installObj.strHtmUpdate;
                } else {
                    return installObj.strHtmUpdate;
                };
            }
            return "";
        } catch (err) {
            if (brower.is64IE) {
                return "err" + installObj.strHtm64_Install;
            }
            else {
                return "err" + installObj.strHtmInstall;
            }
        }
    };

    //获取客户端信息
    lodopObj.getLodopInfo = function (info) {
        return hiddenLodop.GET_SYSTEM_INFO(info);
    };

    //保存Html到Excel
    lodopObj.saveAsExcel = function (name, width, height, html) {
        hiddenLodop.PRINT_INIT("");
        hiddenLodop.ADD_PRINT_TABLE(0, 0, width, height, html);
        hiddenLodop.SET_SAVE_MODE("Orientation", 1); //Excel文件的页面设置：横向打印   1-纵向,2-横向;
        hiddenLodop.SET_SAVE_MODE("PaperSize", 9); //Excel文件的页面设置：纸张大小   9-对应A4
        hiddenLodop.SET_SAVE_MODE("Zoom", 100); //Excel文件的页面设置：缩放比例
        hiddenLodop.SET_SAVE_MODE("CenterHorizontally", true); //Excel文件的页面设置：页面水平居中
        hiddenLodop.SET_SAVE_MODE("CenterVertically", true); //Excel文件的页面设置：页面垂直居中
        hiddenLodop.SAVE_TO_FILE(name);
    };

    /**x
           * 打印Html
           * html:html内容
           * w:宽度
           * h:高度
           * intorient:1---纵(正)向打印，固定纸张； 2---横向打印，固定纸张；  3---纵(正)向打印，宽度固定，高度按打印内容的高度自适应；0(或其它)----打印方向由操作者自行选择或按打印机缺省设置；
           * pageSize: var pageSize = { intPageWidth: 0, intPageHeight: 0, strPageName: "A4" } (默认值可以不传)；
           * hasView:打印前是否预览 true:预览 (不传直接打印)；
           * defPrinter:默认打印机
           * modeid:打印类型
           * update: ck  2016-3-01
           */
    lodopObj.printHTML = function (lodopDom, html, width, height, intOrient, pageSize, hasView, modeid, defPrinter) {
        lodopDom.PRINT_INIT("打印-快递助手");
        if (modeid == 'jhd') {
            lodopDom.SET_PRINTER_INDEX(defPrinter);
        }
        if (modeid == 'bhd') {
            lodopDom.SET_PRINTER_INDEX(defPrinter);
        }
        if (pageSize) {
            lodopDom.SET_PRINT_PAGESIZE(intOrient, pageSize.intPageWidth, pageSize.intPageHeight, pageSize.strPageName);
        } else {
            lodopDom.SET_PRINT_PAGESIZE(intOrient, 0, 0, "A4");
        }
        lodopDom.ADD_PRINT_HTM(0, 0, width, height, html);
        if (hasView) {
            lodopDom.PREVIEW();
        } else {
            lodopDom.PRINT();
        }
    };

    /*打印模板
        * lodop : lodop 控件
        * printdatas:要打印的数据
        * templateSet:模板内容
        * ptype:快递单还是发货单 kdd/fhd
        * hasView:是否打印预览
        * printBoxIsShow:是否显示系统打印机选择框
        * selectedPrinter:已选择的打印机
         * printSynFunc:打印异步执行方法，进度条等等
          * clearFunc:选择打印机取消事件
           * printOkFunc:打印成功后调用
        */
    lodopObj.printTemplate = function (lodop, printdatas, templateSet, ptype, hasView, printBoxIsShow, selectedPrinter, printSynFunc, clearFunc, printOkFunc) {
        var dataObj = comp.Print.Data;
        var that = new comp.Print.FN();
        dataObj.isFirstPrint = true;
        //dataObj.templateSet = that.getKddSet(mkddid);
        dataObj.templateSet = templateSet;

        if (ptype == "fhd" && templateSet.ModeList.KddType == 3) {
            dataObj.isLinkedItem = false;
        }
        else {
            dataObj.isLinkedItem = false;
        }
        if (ptype == "kdd") {
            this.printKdd(lodop, printdatas, hasView, 0, printBoxIsShow, printSynFunc, clearFunc, printOkFunc,selectedPrinter);
        }
        else if (ptype == "fhd") {
            this.printFhd(lodop, printdatas, hasView, 0, printBoxIsShow, printSynFunc, clearFunc, printOkFunc,selectedPrinter);
        }

    };

    //打印的全局设置
    lodopObj.setGlobal_lodop = function (type, lodop, isDef) {
        var dataObj = comp.Print.Data;
        var that = new comp.Print.FN();
        //重置纸张
        dataObj.currentPage = 1;
        //读取默认纸张
        if (dataObj.templateSet.ModeTempPrintcfg != null) {
            dataObj.pageHMmLodop = dataObj.templateSet.ModeTempPrintcfg.Height;
            dataObj.pageWMmLodop = dataObj.templateSet.ModeTempPrintcfg.Width;
        }
        else {
            if (dataObj.templateSet != null) {
                if (dataObj.templateSet.ModeList.kddtype > 1) {
                    dataObj.pageWMmLodop = 100;
                }
                else
                    dataObj.pageWMmLodop = dataObj.templateSet.ModeList.WidthPaper / 10;
                dataObj.pageHMmLodop = dataObj.templateSet.ModeList.HeightPaper / 10;
            }
            else {
                dataObj.pageWMmLodop = lodop.GET_VALUE("PRINTSETUP_PAGE_WIDTH", "selected") / 10;
                dataObj.pageHMmLodop = lodop.GET_VALUE("PRINTSETUP_PAGE_HEIGHT", "selected") / 10;
            }
        }
        //_lodop.PRINT_INIT("打印-北京格玩科技有限公司");
        //印初始化、设定纸张整体偏移量、设定可视编辑区域大小
        if (!isDef) {
            if (dataObj.templateSet.ModeTempPrintcfg.Direction == 3 && dataObj.isSinglePrint) {
                lodop.PRINT_INIT("快递助手-" + (type === "kdd" ? "快递单" : "发货单") + "打印任务");
                lodop.SET_PRINT_STYLEA('PRINT_INIT', 'Top', dataObj.templateSet.ModeTempPrintcfg.Updown + (type == "fhd" ? Math.ceil(dataObj.marginTop / dataObj.mmToPxUnit) : 0) + "mm");
                lodop.SET_PRINT_STYLEA('PRINT_INIT', 'Left', dataObj.templateSet.ModeTempPrintcfg.Leftright + (type == "fhd" ? Math.ceil(dataObj.marginLeft / dataObj.mmToPxUnit) : 0) + "mm");
            }
            else {
                lodop.PRINT_INITA(dataObj.templateSet.ModeTempPrintcfg.Updown + (type == "fhd" ? Math.ceil(dataObj.marginTop / dataObj.mmToPxUnit) : 0) + "mm", dataObj.templateSet.ModeTempPrintcfg.Leftright + (type == "fhd" ? Math.ceil(dataObj.marginLeft / dataObj.mmToPxUnit) : 0) + "mm", (dataObj.pageWMmLodop + 4) + "mm", dataObj.pageHMmLodop + "mm", "快递助手-" + (type === "kdd" ? "快递单" : "发货单") + "打印任务");
            }

            lodop.SET_PRINT_PAGESIZE(dataObj.templateSet.ModeTempPrintcfg.Direction, dataObj.pageWMmLodop + "mm", dataObj.templateSet.ModeTempPrintcfg.Direction == 3 ? 20 : dataObj.pageHMmLodop + "mm", "");
        } else {
            lodop.PRINT_INITA(0, 0, (dataObj.pageWMmLodop + 5) + "mm", (dataObj.templateSet.ModeTempPrintcfg.Direction == 3 ? 180 : dataObj.pageHMmLodop) + "mm", "");
            lodop.SET_PRINT_PAGESIZE(dataObj.templateSet.ModeTempPrintcfg.Direction != 3 ? 0 : dataObj.templateSet.ModeTempPrintcfg.Direction, dataObj.pageWMmLodop + "mm", dataObj.templateSet.ModeTempPrintcfg.Direction == 3 ? 20 : dataObj.pageHMmLodop + "mm", "");
        }
        lodop.SET_SHOW_MODE("LANGUAGE", 0);
        //设置是否进行对后台服务的打印状态进行捕获
        lodop.SET_PRINT_MODE("CATCH_PRINT_STATUS", false);
        lodop.SET_PRINT_MODE("POS_BASEON_PAPER", true); //设置输出位置以纸张边缘为基点
        lodop.SET_PRINT_MODE("NOCLEAR_AFTER_PRINT", true); //打印或预览后会清空所有内容
        lodop.SET_PRINT_MODE("AUTO_CLOSE_PREWINDOW", true); //设置打印完毕是否自动关闭预览窗口
        lodop.SET_SHOW_MODE("BKIMG_PRINT", 0); //设置打印时是否包含背景图

        lodop.SET_LICENSES("北京格玩科技有限公司", "653587569718688748719056235623", "kuaidizs.com", "2F71232D415F674063F2FD9EFD7C6BBE75");
        //菜鸟官方模板不走全局设置
        if (type == "kdd" && !(dataObj.templateSet.ModeList.KddType == 3 && dataObj.templateSet.ModeList.StyleId == 2)) {
            dataObj.tempDefaultFontSize =that.getFontSize_lodop(dataObj.globalSettingKdd.ModeSet.Fontsize);
            dataObj.tempDefaultFontName= dataObj.globalSettingKdd.ModeSet.Fontname;
            lodop.SET_PRINT_MODE("PRINT_PAGE_PERCENT", "Width:" + dataObj.globalSettingKdd.ModeSet.SliderW + "%;Height:" + dataObj.globalSettingKdd.ModeSet.SliderH + "%"); //指定整页缩放打印的比例
            lodop.SET_PRINT_STYLE("FontName", dataObj.tempDefaultFontName); //设定纯文本打印项的字体名称
            lodop.SET_PRINT_STYLE("FontSize", dataObj.tempDefaultFontSize); //设定纯文本打印项的字体大小
            lodop.SET_PRINT_STYLE("Bold", ((dataObj.globalSettingKdd.ModeSet.Isb == true || dataObj.globalSettingKdd.ModeSet.Isb == "1" || dataObj.globalSettingKdd.ModeSet.Isb == 1) ? 1 : 0)); //设定纯文本打印项是否粗体
        }
        else if (type == "fhd") {
            dataObj.tempDefaultFontSize = that.getFontSize_lodop(dataObj.globalSettingFhd.ModeSet.Fontsize);
            dataObj.tempDefaultFontName = dataObj.globalSettingFhd.ModeSet.Fontname;
            lodop.SET_PRINT_MODE("PRINT_PAGE_PERCENT", "Width:" + dataObj.globalSettingFhd.ModeSet.SliderW + "%;Height:" + dataObj.globalSettingFhd.ModeSet.SliderH + "%"); //指定整页缩放打印的比例
            lodop.SET_PRINT_STYLE("FontName", dataObj.globalSettingFhd.ModeSet.Fontname); //设定纯文本打印项的字体名称
            lodop.SET_PRINT_STYLE("FontSize", dataObj.tempDefaultFontSize); //设定纯文本打印项的字体大小
            lodop.SET_PRINT_STYLE("Bold", ((dataObj.globalSettingFhd.ModeSet.Isb == true || dataObj.globalSettingFhd.ModeSet.Isb == "1" || dataObj.globalSettingFhd.ModeSet.Isb == 1) ? 1 : 0)); //设定纯文本打印项是否粗体
            lodop.SET_PRINT_STYLEA(0, "LineSpacing", that.getLineSpacing_lodop(dataObj.tempDefaultFontSize, -1)); //纯文本的行间距
        }


    };

    //打印快递单
    lodopObj.printKdd = function (lodop, printdatas, hasView, valtem, printBoxIsShow, printSynFunc, clearFunc, printOkFunc,selectedPrinter) {
        var that = this;
        var dataObj = comp.Print.Data;
        if (dataObj.templateSet.ModeList.Exid == 89) {
            var values = [];
            for (var i = 0; i < printdatas.length; i++) {
                printdatas[i].sfjc = _p30_getSfjcVal(printdatas[i].s_p);
                values.push(printdatas[i]);
            }
            var parameter = {};
            parameter["tname"] = "mailtmp_s2";
            parameter["docname"] = "mailpdfm1";
            parameter["t"] = new Date().getTime();
            parameter["value"] = values.join("@");
            $.ajax({
                type: "Post",
                dataType: "json", //返回json格式的数据
                url: "http://localhost:9090/ydecx/service/mailpx/printDirect.pdf",
                data: parameter,
                success: function (data) {

                }
            });
            if (typeof printOkFunc == 'function') printOkFunc(); //回调
        }
        else {
            dataObj.printConfig = printdatas[0].printconfig;
            if ( !dataObj.templateSet.ModeTempPrintcfg && !dataObj.templateSet.ModeTempPrintcfg.DefaultPrinter && dataObj.templateSet.ModeTempPrintcfg.DefaultPrinter.indexOf("BTP-") > -1) {
                //alert("新北洋打印机发送任务时页面会卡住，如果出现卡住的情况请稍等。建议新北洋打印机一次性打印的订单数量不要太多。");
                dataObj.kddGroupCount = printdatas.length;
            }
            var plPrints = that.getPrintModelsPL(printdatas, dataObj.kddGroupCount);
            if (valtem < plPrints.length) {
                that.setGlobal_lodop("kdd", lodop);
                if (typeof printSynFunc == 'function') printSynFunc(valtem * dataObj.kddGroupCount); //回调

                for (var datai = 0; datai < plPrints[valtem].length; datai++) {
                    if (datai > 0) {
                        lodop.NewPage();
                    }
                    //开始绘制
                    this.loadPrintItem(lodop, plPrints[valtem][datai], dataObj.templateSet, "kdd", false, false, 0,hasView);
                }
                //打印
                //lodop, hasView, printBoxIsShow, selectedPrinter, clearFunc, printOkFunc
                if(!selectedPrinter){
                    selectedPrinter = dataObj.templateSet.ModeTempPrintcfg != null ? dataObj.templateSet.ModeTempPrintcfg.DefaultPrinter : that.getDefaultPrinter()
                } 
                that.printData(lodop, hasView, printBoxIsShow, selectedPrinter , clearFunc);
                valtem++;
            }

            if (valtem < plPrints.length) {
                setTimeout(function (lodop, printdatas, hasView, valtem, printBoxIsShow, printSynFunc, clearFunc, printOkFunc) {
                    return function () {
                        that.printKdd(lodop,
                            printdatas,
                            hasView,
                            valtem,
                            printBoxIsShow,
                            printSynFunc,
                            clearFunc,
                            printOkFunc,
                            selectedPrinter);
                    }
                }(lodop, printdatas, hasView, valtem, printBoxIsShow, printSynFunc, clearFunc, printOkFunc), 100);
            } else {
                if (typeof printOkFunc == 'function') printOkFunc(); //回调
            }
        }

    };

    //打印发货单
    lodopObj.printFhd = function (lodop, printdatas, hasView, valtem, printBoxIsShow, printSynFunc, clearFunc, printOkFunc,selectedPrinter) {
        var that = this;
        var dataObj = comp.Print.Data;
        //表格列数量，每一行的高度，四边框线条，标题行线条，合计行线条，产品行线条产品列线条
        //1:黑 2：深灰 3：浅灰 4：无
        var pw = parseInt(dataObj.pageWMmLodop * dataObj.mmToPxUnit);
        var ph = parseInt(dataObj.pageHMmLodop * dataObj.mmToPxUnit) - (0.01 * parseInt(dataObj.pageHMmLodop * dataObj.mmToPxUnit));
        var printDataItem = null;
        var inputArr = [];
        var printItemArr = []; //当前当前打印项的坐标数组
        var contentH = 0; //每一页的内容高度 当在一张纸上打印多个发货单的时候可以用来确定下一个发货单的top值
        var minusH = 0; //当某一项不显示的时候需要把高度减掉
        var tableJson = {};
        if (valtem === 0) {
            dataObj.overH = 0;
            dataObj.pTop = 0;
            dataObj.currentPage = 1;
            dataObj.linkedIndex = 0;
            that.setGlobal_lodop("fhd", lodop);
        }
        if (dataObj.globalSettingFhd.ModeSet.A4mode === 0) {
            if ( !dataObj.templateSet.ModeTempPrintcfg && !dataObj.templateSet.ModeTempPrintcfg.DefaultPrinter && dataObj.templateSet.ModeTempPrintcfg.DefaultPrinter.indexOf("BTP-") > -1) {
                //alert("新北洋打印机发送任务时页面会卡住，如果出现卡住的情况请稍等。建议新北洋打印机一次性打印的订单数量不要太多。");
                dataObj.fhdGroupCount = printdatas.length;
            }
            //连续纸 自定义高度需单任务发送任务
            var plPrints = that.getPrintModelsPL(printdatas, dataObj.templateSet.ModeTempPrintcfg.Direction == 3?1:dataObj.fhdGroupCount);
            if (valtem < plPrints.length) {
                if (valtem > 0) {
                    that.setGlobal_lodop("fhd", lodop);
                }
                if (typeof printSynFunc == "function") printSynFunc(valtem * (dataObj.templateSet.ModeTempPrintcfg.Direction == 3 ? 1 : dataObj.fhdGroupCount)); //回调

                for (var datai = 0; datai < plPrints[valtem].length; datai++) {
                    if (datai > 0) {
                        lodop.NewPageA();
                        dataObj.linkedIndex = 0;
                    }
                    //开始绘制
                    this.loadPrintItem(lodop, plPrints[valtem][datai], dataObj.templateSet, "fhd", false, false, 0);
                    that.printTasksDraw(0, lodop);
                }
                //打印
                if(!selectedPrinter){
                    selectedPrinter = dataObj.templateSet.ModeTempPrintcfg != null ? dataObj.templateSet.ModeTempPrintcfg.DefaultPrinter : that.getDefaultPrinter();
                }
                that.printData(lodop, hasView, printBoxIsShow,selectedPrinter , clearFunc);
                valtem++;
            }
            if (valtem < plPrints.length) {
                setTimeout(function (lodop, printdatas, hasView, valtem, printBoxIsShow, printSynFunc, clearFunc, printOkFunc) {
                    return function () {
                        that.printFhd(lodop,
                            printdatas,
                            hasView,
                            valtem,
                            printBoxIsShow,
                            printSynFunc,
                            clearFunc,
                            printOkFunc,
                            selectedPrinter);
                    }
                }(lodop, printdatas, hasView, valtem, printBoxIsShow, printSynFunc, clearFunc, printOkFunc), 100);
            } else {
                that.printOver();
                if (typeof printOkFunc == 'function') printOkFunc(); //回调
            }
        }
        else {
            //拼接
            if (valtem < printdatas.length) {
                if (typeof printSynFunc == "function") printSynFunc(valtem); //回调

                var totalPages = Math.ceil((dataObj.A4Height * dataObj.BatchPages) / ph);

                printDataItem = printdatas[valtem];
                //inputArr = that.sortInputArr_lodop(dataObj.templateSet.inputTextArray); //模板文本框数组
                //inputArr = dataObj.templateSet.ModeInputs; //模板文本框数组
                printItemArr = []; //当前当前打印项的坐标数组
                contentH = 0; //每一页的内容高度 当在一张纸上打印多个发货单的时候可以用来确定下一个发货单的top值
                minusH = 0; //当某一项不显示的时候需要把高度减掉

                contentH += this.loadPrintItem(lodop, printDataItem, dataObj.templateSet, "fhd", false, false, 0);

                contentH += 20;
                //var tempPTop = dataObj.pTop;
                var lineFlag = true;
                var IsCalHeight = false;
                var contentMoveH = 0; //打印当前发货单总体需要下移的高度
                if (dataObj.pTop !== 0) {
                    dataObj.overH = ph - dataObj.pTop % ph - (dataObj.templateSet.ModeTempPrintcfg.Updown + dataObj.marginTop); //当前页剩余的高度
                    IsCalHeight = true;
                }
                //第一页统计会出问题 暂时注释
                //else {
                //    dataObj.overH = ph - contentH - (dataObj.templateSet.ModeTempPrintcfg.Updown + dataObj.marginTop); //当前页剩余的高度
                //    if (dataObj.overH<=0) {
                //        IsCalHeight = true;
                //    }
                //}
                if (IsCalHeight) {
                    if (contentH >= dataObj.overH) {
                        if (contentH - dataObj.overH > 20) {
                            dataObj.pTop = 0;
                            dataObj.linkedIndex = 0;
                            lodop.NewPageA();
                            dataObj.currentPage++;
                        } else {
                            contentH = contentH - (contentH - dataObj.overH) - 1;
                            contentMoveH = 1; //不让ptop加10
                            lineFlag = false;
                        }

                    } else {
                        contentMoveH = 0;
                    }
                    dataObj.pTop += contentMoveH;
                }

                if (lineFlag) {
                    // A4纸拼接的时候是否打印分隔线0|1
                    if (dataObj.globalSettingFhd.ModeSet.A4line == 1) {
                        var task = {
                            itemKey: "123",//Key
                            dataType: "line",
                            itemY: contentH,
                            itemX: (0 - dataObj.marginLeft),
                            itemW: (pw - dataObj.marginLeft),
                            itemH: 1,
                            lineStyle: 2
                        };
                        dataObj.printTaskArr_lodop.push(task);
                    }
                }
                //按纸张传任务打印
                if (dataObj.currentPage > totalPages || valtem == printdatas.length - 1) {
                    if (dataObj.currentPage > totalPages) {
                        valtem--;
                        dataObj.printTaskArr_lodop = [];
                    } else {
                        that.printTasksDraw((dataObj.pTop + contentMoveH), lodop);
                    }
                    that.printData(lodop, hasView, printBoxIsShow, dataObj.templateSet.ModeTempPrintcfg != null ? dataObj.templateSet.ModeTempPrintcfg.DefaultPrinter : "", clearFunc);
                    dataObj.overH = 0;
                    dataObj.pTop = 0;
                    dataObj.currentPage = 1;
                    dataObj.linkedIndex = 0;
                    that.setGlobal_lodop("fhd", lodop);
                } else {
                    //将打印任务发送给打印机
                    that.printTasksDraw((dataObj.pTop + contentMoveH), lodop);
                    dataObj.pTop += contentH;
                    //打印-下一页需要下移的偏移
                    dataObj.pTop += contentMoveH > 0 ? 0 : 10;
                }
            }
            valtem++;
            if (valtem < printdatas.length) {
                setTimeout(function (lodop, printdatas, hasView, valtem, printBoxIsShow, printSynFunc, clearFunc, printOkFunc) {
                    return function () {
                        that.printFhd(lodop,
                            printdatas,
                            hasView,
                            valtem,
                            printBoxIsShow,
                            printSynFunc,
                            clearFunc,
                            printOkFunc);
                    }
                }(lodop, printdatas, hasView, valtem, printBoxIsShow, printSynFunc, clearFunc, printOkFunc), 100);
            } else {
                that.printOver();
                if (typeof printOkFunc == 'function') printOkFunc(); //回调
            }
        }


    };

    //结束打印
    lodopObj.printOver = function () {
        var dataObj = comp.Print.Data;
        dataObj.isLinkedItem = false;
        dataObj.linkedIndex = 0;
        dataObj.isFirstLinkedItem = false;
        dataObj.linkedSize = 0;
    }
    /*绘制打印项
  topY：初始高度
  lodop：打印控件对象
  */
    lodopObj.printTasksDraw = function (topY, lodop) {
        var dataObj = comp.Print.Data;
        var that = new comp.Print.FN();
        $.each(dataObj.printTaskArr_lodop, function (j, item) {
            that.drawItem(lodop, item, topY,"print");//发送打印任务
            if (item.dataType=="table") {
                dataObj.linkedIndex = lodop.Get_VALUE("ItemCount", 0);
                dataObj.isFirstLinkedItem = true;
                dataObj.linkedSize = 0;
            }
            else if (item.isLastIndex) {
                dataObj.linkedIndex = lodop.Get_VALUE("ItemCount",0);
                dataObj.isFirstLinkedItem = true;
                dataObj.linkedSize = 0;
            }
        });
        dataObj.printTaskArr_lodop = [];
    };

    /*绘制打印项
  lodop：打印控件对象
  printDataItem：打印数据
  templateSet：模板设置
  templateType：面单类型 kdd||fhd
  isShowTempImg：是否显示底图
  showType: setup 主页面，design 编辑页面
  */
    lodopObj.loadPrintItem = function (lodop, printDataItem, templateSet, templateType, isShowTempImg, showType, starTop,hasView) {
        var dataObj = comp.Print.Data;
        var cp_code = templateSet.ModeList.Excode;
        var that = new comp.Print.FN();
        var isCompatible = templateType != "kdd";
        var modeH = 0;//模板高度
        var isCainiao=(templateType === "kdd" && templateSet.ModeList.KddType == 3 && templateSet.ModeList.StyleId == 2);
        if (isShowTempImg) {//预览编辑
            this.printOver();
            dataObj.templateSet = templateSet;
            dataObj.printConfig = printDataItem.printconfig;
            that.setGlobal_lodop(templateType, lodop, (dataObj.isSinglePrint &&　showType == "setup")?false:true);
        }
        //处理省份简称
        printDataItem.sfjc = _p30_getSfjcVal(printDataItem.s_p);
        if (isCainiao && dataObj.modeType != 1) {//菜鸟模板
            lodop.SET_PRINT_MODE("CAINIAOPRINT_MODE", "CP_CODE=" + dataObj.templateSet.ModeList.Excode + "&CONFIG=" + dataObj.printConfig);//TAOBAO_SS_IDSDATA
            lodop.ADD_PRINT_DATA("ProgramData", dataObj.templateSet.ModeCustomerTemplate.Configcode);//加载自定义区域
            var printdata = printDataItem;
            lodop.SET_PRINT_CONTENT("ali_waybill_product_type", templateSet.ModeCustomerTemplate.Svctypename);//业务类型
            lodop.SET_PRINT_STYLEA("ali_waybill_short_address", "CONTENT", printdata.mdd_dzmd);//大头笔
            lodop.SET_PRINT_STYLEA("ali_waybill_ext_send_date", "CONTENT", printdata.f_date);//发件时间
            if (cp_code == "ZJS" && that.getjbd(printdata.gx_mdd, cp_code) == "")//宅急送集散地默认省市区
                lodop.SET_PRINT_STYLEA("ali_waybill_package_center_name", "CONTENT", printdata.s_p + " " + printdata.s_city + " " + printdata.s_q);//集散地名称
            else
                lodop.SET_PRINT_STYLEA("ali_waybill_package_center_name", "CONTENT", that.getjbd(printdata.gx_mdd, cp_code));//集散地名称
            if (cp_code == "POSTB") {
                lodop.SET_PRINT_STYLEA("ali_waybill_shipping_address_city", "CONTENT", printdata.s_city);//发件城市
            }
            lodop.SET_PRINT_STYLEA("ali_waybill_package_center_code", "CONTENT", that.getjbd(printdata.gx_jbm, cp_code));//集散地编码
            lodop.SET_PRINT_STYLEA("ali_waybill_waybill_code", "CONTENT", printdata.fhdexnumber);//运单号

            lodop.SET_PRINT_STYLEA("ali_waybill_send_name", "CONTENT", printdata.f_name);//寄件人
            lodop.SET_PRINT_STYLEA("ali_waybill_send_phone", "CONTENT", printdata.f_tel);//寄件人电话
            lodop.SET_PRINT_STYLEA("ali_waybill_shipping_address", "CONTENT", printdata.f_addr);//寄件人地址


            lodop.SET_PRINT_STYLEA("ali_waybill_consignee_name", "CONTENT", printdata.s_name);//收件人
            lodop.SET_PRINT_STYLEA("ali_waybill_consignee_phone", "CONTENT", printdata.s_phone + " " + printdata.s_tel);//收件人电话
            lodop.SET_PRINT_STYLEA("ali_waybill_consignee_address", "CONTENT", printdata.s_addrall);//收件人地址



            if (templateSet.ModeTempPrintcfg.Qslogo == 1) {
                lodop.SET_PRINT_STYLEA("ali_waybill_cp_logo_up", "PreviewOnly", 0); //签收联快递LOGO
            }
            if (templateSet.ModeTempPrintcfg.Lclogo == 1) {
                lodop.SET_PRINT_STYLEA("ali_waybill_cp_logo_down", "PreviewOnly", 0); //留存联快递LOGO
            }

            lodop.SET_PRINT_STYLEA("ali_waybill_shipping_branch_name", "CONTENT", printdata.sfwd);//发件网点名称
            lodop.SET_PRINT_STYLEA("ali_waybill_shipping_branch_code", "CONTENT", printdata.dzmd_sf);//发件网点代码

            ////服务值
            var cnservice = this.createServceListHTML(templateSet.ModeServiceItems, printdata, true);


            /*********服务传值******/
            lodop.SET_PRINT_CONTENT("ali_waybill_service", cnservice);
            /*********服务传值******/
            /*********商家自定义区域赋值******/
            if (templateSet.ModeInputs != null) {
                $.each(templateSet.ModeInputs, function (k, data) {
                    var dataContent = data.Str_q;
                    $.each(data.proArray, function (j, item) {
                        if (item.Dataname === "fjm") {
                            dataContent += that.getjbd(printdata[item.Dataname], cp_code);
                        }
                        else if (item.Dataname === "dsjecny") {
                            dataContent += (numToCny(printdata["dsje"]) + "(￥：" + printdata["dsje"] + "元)");
                        }
                        else if (item.Dataname == "logo" || item.Dataname == "pic" || item.Dataname == "bgm" || item.Dataname == "img" || item.Dataname == "ewm" || item.Dataname == "ewm_str") { //pic的数据源在前文字
                            if (item.Dataname == "logo" || item.Dataname == "pic" || item.Dataname == "img" || item.Dataname == "bgm") {        //mm-modify
                                dataContent = "<img src='" + data.Str_q + "'/>";
                            }
                        }
                        else {
                            dataContent += that.getValueByTypeName_lodop(printdata, item.Dataname);
                        }
                    });

                    dataContent += data.Str_h;
                    lodop.SET_PRINT_CONTENT(data.DataKey, dataContent);
                });
            }
            /*********商家自定义区域赋值******/
        }
        else {//普通模板
            var inputArr = templateSet.ModeInputs; //模板文本框数组
            for (var i = 0; i < inputArr.length; i++) {
                var printTask = this.getPrintTaskByInput(inputArr[i], printDataItem, templateSet, isCompatible);
                if (printTask != null) {
                    if ((isCompatible && !isShowTempImg) || (dataObj.isSinglePrint && isCompatible && showType != "DESIGN")) { //非快递单业务处理
                        if (!printTask.oldH) {
                            printTask.oldH = printTask.itemH;
                        }
                        printTask.oldY = printTask.itemY;
                        var nowY = that.getPrintItemXY(dataObj.printTaskArr_lodop, printTask);
                        printTask.itemY = nowY;
                        if (modeH < nowY + printTask.itemH) {
                            modeH = nowY + printTask.itemH;
                        }
                        dataObj.printTaskArr_lodop.push(printTask);
                    }
                    //当是快递单或者是测试打印则立即绘制
                    if (!isCompatible || isShowTempImg) {
                        that.drawItem(lodop, printTask, starTop,showType); //发送打印任务
                        dataObj.linkedIndex = 0;
                    }
                }
            }
            if (!isCompatible || isShowTempImg) {
                dataObj.printTaskArr_lodop = [];
            }
        }

        if (isShowTempImg || (isCainiao && dataObj.modeType == 1)) {
            //云打印预览
            if (isCainiao && dataObj.modeType == 1) {
                if (hasView) {
                    lodop.SET_SHOW_MODE("BKIMG_PRINT", 1); //打印时是否包含背景图
                    templateSet.ModeListShow.ImgSrc = "/img/comp/cloudImg/P_" + templateSet.ModeList.Excode + ".png";
                }
                else if (showType == "setup") {
                    templateSet.ModeListShow.ImgSrc = "/img/comp/cloudImg/W_" + templateSet.ModeList.Excode + ".png";
                }
                else {//编辑灰背景
                    templateSet.ModeListShow.ImgSrc = "/img/comp/cloudImg/B_" + templateSet.ModeList.Excode + ".png";
                }
            }
            if (templateSet.ModeListShow.ImgSrc != "") {
                lodop.ADD_PRINT_SETUP_BKIMG("<img border='0' src='" + templateSet.ModeListShow.ImgSrc + "'>"); //指定背景图
                lodop.SET_SHOW_MODE("BKIMG_IN_PREVIEW", 1); //打印预览时是否包含背景图
                lodop.SET_SHOW_MODE("BKIMG_LEFT", 0); //设置背景图位置X值
                lodop.SET_SHOW_MODE("BKIMG_TOP", 0); //设置背景图位置Y值
                lodop.SET_SHOW_MODE("BKIMG_WIDTH", templateSet.ModeList.WidthImg); //设置背景图宽度
                lodop.SET_SHOW_MODE("BKIMG_HEIGHT", templateSet.ModeList.HeightImg); //设置背景图高度
            }
        }
        if (isShowTempImg) {
            lodop.SET_SHOW_MODE("HIDE_GROUND_LOCK", 1);  //隐藏纸钉按钮
            lodop.SET_SHOW_MODE("HIDE_TOOLS_DESIGN", 1); //隐藏整个工具栏
            if (showType == "setup") {
                if (!dataObj.isSinglePrint) {
                    lodop.SET_PRINT_MODE("POS_BASEON_PAPER", false);
                    lodop.SET_PREVIEW_WINDOW(1, 3, 0, 400, 680, "预览查看.打印前预览");
                    lodop.SET_SHOW_MODE("PREVIEW_IN_BROWSE", 1);
                    var divLodop = $(lodop).parent("div");
                    if (dataObj.templateSet.ModeTempPrintcfg.DefaultPrinter !== "" && this.checkPrinter(dataObj.templateSet.ModeTempPrintcfg.DefaultPrinter)) {
                        lodop.SET_PRINTER_INDEX(dataObj.templateSet.ModeTempPrintcfg.DefaultPrinter);
                    }
                    lodop.PREVIEW();
                    divLodop.height(divLodop.height() - 1);
                    divLodop.height(divLodop.height() + 1);
                } else {
                    lodop.SET_SHOW_MODE("SETUP_ENABLESS", "00000000000000");
                    lodop.SET_SHOW_MODE("SETUP_IN_BROWSE", 1);
                    lodop.PRINT_SETUP();
                }
            } else {
                lodop.SET_SHOW_MODE("DESIGN_IN_BROWSE", 1);
                lodop.PRINT_DESIGN();
            }

        }

        return modeH;
    };

    //检验是否存在该打印机
    lodopObj.checkPrinter = function (printerName) {
        var ret = false;
        var printers = this.getPrinters();
        for (var i = 0; i < printers.length; i++) {
            if (printers[i] === printerName) {
                ret = true;
                break;
            }
        }
        return ret;
    }

    /*根据input对象返回打印任务
    inputJson：intput对象
 printDataItem：打印数据
 templateSet：模板设置
 */
    lodopObj.getPrintTaskByInput = function (inputJson, printDataItem, templateSet, isCompatible) {
        var dataObj = comp.Print.Data;
        var that = new comp.Print.FN();
        var cp_code = templateSet.ModeList.Excode;
        var isMsEx = false;//是否是字母件
        var isMother = false;//字母件 是否是母件
        if (printDataItem.zmj_sum && printDataItem.zmj_sum != "" && printDataItem.fhdexnumber != "8011110001") {
            isMsEx = true;
            if (printDataItem.zmj_mnum=="") {
                isMother = true;
            }
        }
        var printTask = {};
        var dataType = ""; //如果是店标的话是图片
        printTask.content = inputJson.Str_q; //文本框前文字
        printTask.itemKey = inputJson.InputID;//Key
        //兼容菜鸟
        if (inputJson.DataKey) {
            printTask.itemKey = inputJson.DataKey;
        }
        printTask.dataType = "txt";
        printTask.itemY = inputJson.Y_;//Y
        printTask.itemX = inputJson.X_;//X
        printTask.itemW = inputJson.W_;//W
        printTask.itemH = inputJson.H_;//H
        printTask.fontSize = inputJson.Fontsize==0?dataObj.tempDefaultFontSize: that.getFontSize_lodop(inputJson.Fontsize);//字体大小
        printTask.fontName = inputJson.Fontname;//字体
        printTask.readOnly = inputJson.Isedit == 1 ? 0 : 1;//纯文本内容在打印维护时，是否禁止修改
        printTask.bold = inputJson.Isb_n;//是否加粗
        printTask.lineSpacing = that.getLineSpacing_lodop(printTask.fontSize, inputJson.Hjj);//行间距
        printTask.letterSpacing = inputJson.Zjj;//字间距
        printTask.isEdit = 1;//是否允许控件被修改删除
        printTask.isMove = 1;//是否允许控件被移动
        printTask.direction = 0;//打印方向 默认横向
        var citemArr = inputJson.proArray; //单个文本框中数据项数组
        for (var j = 0; j < citemArr.length; j++) {
            if (j > 0) {
                printTask.content += " ";
            }
            var citem = citemArr[j];
            dataType = citem.Dataname;
            printTask.content += citem.Str_q; //数据项前文字

            if (citem.ProValue) {
                var pStr = citem.ProValue;
                if (pStr.indexOf("direction=1") >= 0) {
                    printTask.direction = 1;
                }
            }
            if (dataType == "logo" || dataType == "pic" || dataType == "bgm" || dataType == "img") { //pic的数据源在前文字
                printTask.dataType = "img";
                printTask.content = "";
                printTask.stretch = 2; //图片缩放
                if (dataType == "logo" ) {
                    printTask.content = this.getValueByTypeName_lodop(printDataItem, dataType);
                } else if (dataType === "img" || dataType === "pic" || dataType == "bgm") {
                    printTask.content = inputJson.Str_q;
                }
                break;
            }
            else if (dataType == "txm_tid" || dataType == "txm_number" || dataType == "txm_jbm") { //条形码
                printTask.dataType = "barcode";
                printTask.content = "";
                printTask.txmType = citem.Str_q == "" ? "128Auto" : ("128" + citem.Str_q.toUpperCase());
                var val = this.getValueByTypeName_lodop(printDataItem, dataType);
                if (dataType == "txm_jbm") {
                    val = that.getjbd(val, cp_code);
                }
                printTask.content += val; //数据项内容
                break;
            } else if (dataType == "gx_jbm") { //集包码
                printTask.dataType = "txt";
                printTask.content += that.getjbd(this.getValueByTypeName_lodop(printDataItem, dataType), cp_code); //数据项内容
                break;
            }
            else if (dataType == "fjm") { //分拣码
                printTask.dataType = "txt";
                printTask.content += that.getjbd(this.getValueByTypeName_lodop(printDataItem, dataType), cp_code); //数据项内容
                break;
            }
            else if (dataType == "ewm" || dataType == "ewm_str") { //二维码
                printTask.dataType = "barcode";
                printTask.txmType = "QRCode";
                printTask.content = "";
                //if (dataType == "ewm") {
                //    printTask.content += this.getValueByTypeName_lodop(printDataItem, dataType); //数据项内容
                //}
                //else {
                printTask.content += inputJson.Str_q; //ewm_str数据在前文字里
                //}
                break;
            }
            else if (dataType == "line1" || dataType == "line2" || dataType == "line3" || dataType == "line") { //线
                printTask.dataType = "line";
                if (!printTask.lineStyle) {
                    printTask.lineStyle = 0;
                }
                if (inputJson.Str_q) {
                    printTask.lineStyle = +inputJson.Str_q;
                }
                break;
            }
            else if (dataType == "f_ww") { //如果是发件旺旺的话
                printTask.dataType = "txt";
                var val = this.getValueByTypeName_lodop(printDataItem, dataType);
                if (citem.ProValue) {
                    var pStr = citem.ProValue; //单个数据项组成部分数组  _show=1|_fx=1";
                    if (pStr.indexOf("fx=1") > -1) { //显示

                    } else {
                        if (pStr.indexOf("show=1") > -1) { //主旺旺部分
                            val = val.substring(0, val.indexOf(':'));
                        } else if (pStr.indexOf("show=2") > -1) { //子旺旺部分
                            val = val.indexOf(':') > -1 ? val.substring(val.indexOf(':') + 1) : val;
                        }
                        printTask.content += val; //数据项内容
                    }
                }
                else {
                    printTask.content += val;
                }
                printTask.content += citem.Str_h; //数据项后文字
            }
            else if (dataType == "f_date") { //如果是发件日期的话
                printTask.dataType = "txt";
                var date = new Date();
                var isshowH = false;
                if (citem.ProValue) {
                    var pStr = citem.ProValue; //单个数据项组成部分数组  _date=1|_time=1";
                    if (pStr.indexOf("date=1") > -1) { //明天
                        date.setDate(date.getDate() + 1);
                    }
                    if (pStr.indexOf("time=1") > -1) { //显示时分秒
                        isshowH = true;
                    }
                }
                var val = date.format("yyyy-MM-dd");
                if (isshowH) {
                    val = date.format("yyyy-MM-dd hh:mm:ss");
                }
                printTask.content += val; //数据项内容
                printTask.content += citem.Str_h; //数据项后文字
            }
            else if (dataType == "mdd") { //如果是目的地的话
                printTask.dataType = "txt";
                if (citem.ProValue) {
                    var pStr = citem.ProValue; //单个数据项组成部分数组  _p=1|_c1=1|_c2=1|_c3=1";
                    if (pStr.indexOf("p=1") > -1) {
                        var s_p = printDataItem["s_p"];
                        if (s_p.indexOf("北京") > -1 || s_p.indexOf("上海") > -1 || s_p.indexOf("天津") > -1 || s_p.indexOf("上海") > -1) {
                        } else {
                            printTask.content += s_p;
                        }
                    }
                }
                printTask.content += printDataItem["s_city"] + " " + printDataItem["s_q"];
            }
            else if (dataType == "gx_mdd" || dataType == "mdd_dzmd") { //集包目的地or大头笔
                printTask.dataType = "txt";
                if (that.getjbd(this.getValueByTypeName_lodop(printDataItem, dataType), cp_code) == "") {
                    if (dataType == "gx_mdd") {
                        printTask.content += printDataItem["s_p"] + " " + printDataItem["s_city"] + " " + printDataItem["s_q"];
                    }
                    else {
                        printTask.content = this.getValueByTypeName_lodop(printDataItem, dataType);
                    }
                }
                else {
                    printTask.content += that.getjbd(this.getValueByTypeName_lodop(printDataItem, dataType), cp_code);
                }
                printTask.content += citem.Str_h; //数据项后文字
            }
            else if (dataType == "s_phone" || dataType == "s_tel") { //单打不需要解密
                printTask.dataType = "txt";
                printTask.content += this.getValueByTypeName_lodop(printDataItem, dataType);
                printTask.content += citem.Str_h; //数据项后文字
            }
            else if (dataType == "servicetype") {
                printTask.dataType = "txt";
                printTask.content += templateSet.ModeCustomerTemplate.Svctypename;
                printTask.content += citem.Str_h; //数据项后文字
            }
            else if (dataType == "servicename") {
                printTask.dataType = "txt";
                printTask.content += templateSet.ModeCustomerTemplate.ServiceValue;
                printTask.content += citem.Str_h; //数据项后文字
            }
            else if (dataType == "servicelist") {//服务选项
                dataObj.serviceListWidth = printTask.itemW;
                printTask.dataType = "htm";
                printTask.itemKey = "servicelist";
                printTask.content = this.createServceListHTML(templateSet.ModeServiceItems, printDataItem, false, printTask.itemW);
            }
            else if (dataType.indexOf("table") > -1 || dataType.indexOf("hj") > -1 || dataType.indexOf("tb") > -1) {//表格属性
                if (dataType == "table") {
                    printTask.dataType = "table";
                    printTask.content = printDataItem.tableHtml;
                    printTask.oldH = printTask.itemH;
                    if (!dataObj.isLinkedItem) {
                        printTask.itemH = printDataItem.tableHeight;
                    }
                } else {
                    return null;
                }
            }

            else {
                printTask.dataType = "txt";
                var val = this.getValueByTypeName_lodop(printDataItem, dataType);
                var isSet = true;
                if (citem.ProValue) {
                    var pStr = citem.ProValue;
                    if (pStr.indexOf("nu=0") >= 0 || pStr.indexOf("nu＝0") >= 0) { //nu=0表示无内容时不显示内容框 默认为显示  只有contentType=="mjs"|"s_message"|"f_memo"|"o_info"时才有可能有这个值
                        if ($.trim(val) == "") {
                            printTask.content = "";
                            printTask.oldH = printTask.itemH;
                            printTask.itemH = 0;
                            isSet = false;
                        }
                    }
                }
                if (isMsEx){
                    if (isMother){
                        if (dataType == "zmj_mnum") {
                            printTask.content = "";
                            printTask.oldH = printTask.itemH;
                            printTask.itemH = 0;
                            isSet = false;
                        }
                        if (dataType == "fhdexnumber") {
                            printTask.content = "母单号："
                        }
                    }
                    else {
                        if (dataType == "fhdexnumber") {
                            printTask.content = "子单号："
                        }
                    }
                }

                if (isSet) {
                    printTask.content += val; //数据项内容
                    printTask.content += citem.Str_h; //数据项后文字
                }
            }

            //content.push(citem.Str_h);//数据项后文字
        } //单个文本框中数据项数组循环结束

        if (printTask.dataType == "txt") {
            printTask.content += inputJson.Str_h; //文本框后文字
            if ((isCompatible || dataType == "f_info") && printTask.content.length > 0) {
                if (dataType == "f_memo" || dataType == "s_message" || dataType == "o_info" || dataType == "f_info") {
                    //重新计算文本框行高以及换行要求
                    var nowCs = that.getInputHeight_lodop(printTask.itemW, printTask.itemH, inputJson.Zjj, inputJson.Hjj, printTask.fontSize, inputJson.Isb_n, printTask.content, true, true);
                    printTask.oldH = printTask.itemH;
                    printTask.itemH = nowCs.nowH;
                    printTask.content = nowCs.content;
                    //快递单发件详情高度不能超过纸张
                    if (!isCompatible && dataType == "f_info") {
                        var ph = parseInt(dataObj.pageHMmLodop * dataObj.mmToPxUnit);
                        if (printTask.itemY + printTask.itemH > ph) {
                            printTask.itemH = ph - (printTask.itemY + 5);
                        }

                    }
                }
            }
        }

        printTask.content = printTask.content.toString().replace("＆", "&").replace("＝", "=");//内容
        return printTask;
    };

    /*************************
    *创建服务选项的html
    *serviceItems  服务选项实体
    *printData 打印数据实体
    *printData 是否是菜鸟官方模板
    ************************/
    lodopObj.createServceListHTML = function (serviceItems, printData, isCainiao, rootWidth) {
        var dataObj = comp.Print.Data;

        var content = "";
        var that = this;
        if (serviceItems != null) {
            if (isCainiao) {
                $.each(serviceItems, function (j, item) {
                    if (item.Issetval == 1) {
                        content += item.Itemcode + "=" + that.getValueByTypeName_lodop(printData, item.Dataname) + item.Unit + ";";
                    } else {
                        content += item.Itemcode + "=" + item.Defaultval + ";";
                    }
                });
            } else {
                var dataObj = comp.Print.Data;
                if (dataObj.exuid == 50570) {
                    content = "<div style='font-size:10px' " + (dataObj.globalSettingKdd.ModeSet.Fontname != "" ? "font-family:" + dataObj.globalSettingKdd.ModeSet.Fontname + ";" : "") + "font-weight: bold;'><ul style='list-style:none;margin:0;padding:0;width:" + rootWidth + "px;'>";
                    $.each(serviceItems, function (j, item) {
                        content += "<li style=\"width:150px;float:left;margin:0;padding：0\">";
                        if (item.Issetval == 1) {
                            content += item.Itemname + ":" + that.getValueByTypeName_lodop(printData, item.Dataname) + item.Unit;
                        } else {
                            content += item.Itemname + ":" + item.Defaultval;
                        }
                        content += "</li>";
                    });
                }
                else {
                    content = "<div style='font-size:10px' " + (dataObj.globalSettingKdd.ModeSet.Fontname != "" ? "font-family:" + dataObj.globalSettingKdd.ModeSet.Fontname + ";" : "") + "font-weight: bold;'><ul style='list-style:none;margin:0;padding:0;width:" + rootWidth + "px;'>";
                    $.each(serviceItems, function (j, item) {
                        content += "<li style=\"width:110px;float:left;margin:0;padding：0\">";
                        if (item.Issetval == 1) {
                            content += item.Itemname + ":" + that.getValueByTypeName_lodop(printData, item.Dataname) + item.Unit;
                        } else {
                            content += item.Itemname + ":" + item.Defaultval;
                        }
                        content += "</li>";
                    });
                }

                content += "</ul></div>";
            }
        }
        return content;
    };

    /*************************
    *通过字段类型名从用户传过来的打印数据中获取字段值
    *dataJson  M_printdata实例对象
    *typename 属性名
    ************************/
    lodopObj.getValueByTypeName_lodop = function (data, dataName) {
        if (data[dataName]) {
            return data[dataName];
        } else {
            if (dataName.indexOf("tb_hh1") > -1) {
                return "(nrt)";
            }
            else if (dataName == "tb_xh") {
                return "0";
            }
            return "";
        }
    };

    /*打印当前任务数组中的任务
    lodop:控件对象
    printTask:打印任务对象
    startTop:起始高度
    */
    lodopObj.drawItem = function (lodop, printTask, startTop, showType) {
        var dataObj = comp.Print.Data;
        var ph = parseInt(dataObj.pageHMmLodop * dataObj.mmToPxUnit);
        printTask.itemY += startTop;
        if (lodop.GET_VALUE("ItemExist", printTask.itemKey) && showType!="print") { //存在删除重新画
            lodop.SET_PRINT_STYLEA(printTask.itemKey, 'Deleted', true);
        }
        //关联项重新计算Y轴
        if (dataObj.isLinkedItem && dataObj.linkedIndex > 0) {
            if (dataObj.isFirstLinkedItem ) {
                dataObj.isFirstLinkedItem = false;
                dataObj.linkedSize = printTask.itemY - 5;
                printTask.itemY = 5;
            }
            else {
                printTask.itemY = printTask.itemY - dataObj.linkedSize;
            }
        }
        //图片
        if (printTask.dataType == "img") {
            if( printTask.content.indexOf('res/eximg/exlogo') > -1 ){             //mm-modify 添加了图片路径替换 TODO：后端初始模板数据的时候，此项是否可修改一下路劲 目前只发现申通模板有这个问题
                printTask.content = printTask.content.replace('res/eximg/exlogo','resources/img/print/ExImg/exlogo');
            }
            if (printTask.itemW === 0 && printTask.itemH === 0) {
                printTask.itemW = 80;
                printTask.itemH = 80;
            }
            lodop.ADD_PRINT_IMAGE(printTask.itemY, printTask.itemX, printTask.itemW, printTask.itemH, "<img src='" + printTask.content + "'/>");
            lodop.SET_PRINT_STYLEA(0, "Stretch", printTask.stretch); //图片缩放
            lodop.SET_PRINT_STYLEA(0, "ItemName", printTask.itemKey); //图片Key
        }
            //文本
        else if (printTask.dataType == "txt") {
            if (printTask.itemW === 0 && printTask.itemH === 0) {
                printTask.itemW = 100;
                printTask.itemH = 21;
            }
            lodop.ADD_PRINT_TEXTA(printTask.itemKey, printTask.itemY, printTask.itemX, printTask.itemW, printTask.itemH, printTask.content);
            if (printTask.fontName != "") {
                lodop.SET_PRINT_STYLEA(0, "FontName", printTask.fontName); //设定纯文本打印项的字体名称
            }
            if (printTask.fontSize != 0) {
                lodop.SET_PRINT_STYLEA(0, "FontSize", printTask.fontSize); //设定纯文本打印项的字体大小
            }
            if (printTask.bold != -1) {
                lodop.SET_PRINT_STYLEA(0, "Bold", printTask.bold); //设定纯文本打印项是否粗体
            }
            if (printTask.Alignment) {
                lodop.SET_PRINT_STYLEA(0, "Alignment", printTask.Alignment); //对齐方式
            }
            if (printTask.lineSpacing != -1) {
                lodop.SET_PRINT_STYLEA(0, "LineSpacing", printTask.lineSpacing); //纯文本的行间距
            }
            if (printTask.letterSpacing != -1) {
                lodop.SET_PRINT_STYLEA(0, "LetterSpacing", printTask.letterSpacing); //纯文本的字间距 `
            }
            if (printTask.readOnly) {
                lodop.SET_PRINT_STYLEA(0, "ReadOnly", printTask.readOnly); //纯文本内容在打印维护时，是否禁止修改
            }
            //var Top2Offset = beginY % ph - 10;
            //lodop.SET_PRINT_STYLEA(0, "ItemType", 4);
            //lodop.SET_PRINT_STYLEA(0, "Top2Offset", -Top2Offset);
        }
            //线
        else if (printTask.dataType == "line") {
            if (printTask.itemW === 0 && printTask.itemH === 0) {
                printTask.itemW = 100;
                printTask.itemH = 1;
            }
            var lineWidth = 1;
            if (printTask.lineStyle == 0) {
                lineWidth = 1;
                if (!(printTask.fontColor && printTask.fontColor != "" && printTask.fontColor.indexOf("000000") > -1)) {
                    lineWidth = 1;
                    printTask.lineStyle = 0;
                }
            } else {
                if ((showType == "setup" || showType == "design") && !dataObj.isSinglePrint) {
                    lineWidth = 1;
                }
                else {
                    lineWidth = 0;
                }
            }

            var tempbY = printTask.itemY; //线Y
            var tempLH = printTask.itemH; //线高
            var tempZ = 1; //第几张纸
            if (tempLH > 1 && ph > 0) { //竖线
                if (showType == "print") {
                    lodop.ADD_PRINT_LINE(printTask.itemY, printTask.itemX, (printTask.itemY+printTask.itemH), (printTask.itemX + (printTask.itemW == 0 ? 1 : printTask.itemW)), printTask.lineStyle, lineWidth);
                }
                else {
                    tempZ = Math.ceil(tempbY / ph);
                    while (tempLH > 0) {
                        var tempZH = ph * tempZ; //总体高度
                        var lsH = tempZH - tempbY >= tempLH ? tempLH : tempZH - tempbY;
                        lodop.ADD_PRINT_LINE(tempbY, printTask.itemX, tempbY + lsH, (printTask.itemX + (printTask.itemW == 0 ? 1 : printTask.itemW)), printTask.lineStyle, lineWidth);
                        tempbY += (lsH + 15);
                        tempLH -= (lsH + 15);
                        tempZ++;
                    }
                }
               
            } else {
                lodop.ADD_PRINT_LINE(tempbY, printTask.itemX, (printTask.itemY + (printTask.itemH == 0 ? 1 : printTask.itemH)), (printTask.itemX + (printTask.itemW == 0 ? 1 : printTask.itemW)), printTask.lineStyle, lineWidth);
            }
            lodop.SET_PRINT_STYLEA(0, "ItemName", printTask.itemKey);
        }
            //矩形
        else if (printTask.dataType == "rect") {
            var lineWidth = 0;
            if (printTask.lineStyle == 0) {
                lineWidth = 1;
            } else {
                lineWidth = 0;
            }
            lodop.ADD_PRINT_RECT(printTask.itemY, printTask.itemX, printTask.itemW, printTask.itemH, printTask.lineStyle, lineWidth);
            if (printTask.FontColor && printTask.FontColor != "") {
                lodop.SET_PRINT_STYLEA(0, "FontColor", printTask.FontColor);
            }
            lodop.SET_PRINT_STYLEA(0, "ItemName", printTask.itemKey);
        }
            //二维码/条形码
        else if (printTask.dataType == "barcode") { //条形码或者二维码
            if (printTask.itemW === 0 && printTask.itemH === 0) {
                printTask.itemW = 80;
                printTask.itemH = 80;
            }
            lodop.ADD_PRINT_BARCODE(printTask.itemY, printTask.itemX, printTask.itemW, printTask.itemH, printTask.txmType, printTask.content);
            //lodop.SET_PRINT_STYLEA(0, "Angle", 90);
            lodop.SET_PRINT_STYLEA(0, "ShowBarText", 0);
            lodop.SET_PRINT_STYLEA(0, "ItemName", printTask.itemKey);
        }
            //htm
        else if (printTask.dataType == "htm") { //  HTM
            lodop.ADD_PRINT_HTM(printTask.itemY, printTask.itemX, printTask.itemW, printTask.itemH, printTask.content);
            lodop.SET_PRINT_STYLEA(0, "ItemName", printTask.itemKey);
        }
            //table
        else if (printTask.dataType == "table") { //  表格
            lodop.ADD_PRINT_HTM(printTask.itemY, printTask.itemX, printTask.itemW, dataObj.isLinkedItem?"100%":printTask.itemH+50, printTask.content);
            lodop.SET_PRINT_STYLEA(0, "ItemName", printTask.itemKey);
            lodop.SET_PRINT_STYLEA(0, "Offset2Top", (0 - printTask.itemY));
        }

        if (printTask.isMove == 0) {
            lodop.SET_PRINT_STYLEA(0, "HOrient", 3);
            lodop.SET_PRINT_STYLEA(0, "VOrient", 3);
        }
        if (printTask.isEdit == 0) {

        }
        if (printTask.direction==1) {
            lodop.SET_PRINT_STYLEA(0, "Angle", 90);
        }
        lodop.SET_PRINT_STYLEA(0, "ReadOnly", 0);

        if (dataObj.isLinkedItem && dataObj.linkedIndex > 0) {
            lodop.SET_PRINT_STYLEA(0, "LinkedItem", dataObj.linkedIndex)
        }
    };

    //计算打印的数据需要分成几个批次打印
    lodopObj.getPrintModelsPLByCount = function (prints, pageCount) {
        var plPrintdatas = [];
        if (prints.length > 0) {
            if (prints.length <= pageCount) {
                plPrintdatas.push(prints);
            } else {
                for (var i = 0; i < prints.length; i++) {
                    if (i < pageCount) {
                        if ((typeof (plPrintdatas[0]) == "undefined")) {
                            plPrintdatas[0] = Array();
                        }
                        plPrintdatas[0].push(prints[i]);
                    }
                    else {
                        var a = Math.floor(i / pageCount);
                        if ((typeof (plPrintdatas[a]) == "undefined")) {
                            plPrintdatas[a] = Array();
                        }
                        plPrintdatas[a].push(prints[i]);
                    }
                }
            }
        }
        return plPrintdatas;
    };

    lodopObj.sortInputArr_lodop = function (inputArr) {
        for (var i = 0; i < inputArr.length - 1; i++) {
            for (var j = i + 1; j < inputArr.length; j++) {
                if (inputArr[i].Y_ > inputArr[j].Y_) {
                    var inputTemp = inputArr[i];
                    inputArr[i] = inputArr[j];
                    inputArr[j] = inputTemp;
                }
            }
        }
        return inputArr;
    };

    //菜鸟批打分组
    lodopObj.getPrintModelsPL = function (prints, pageCount,flag) {
        var plPrintdatas = [];
        if (flag) {
            plPrintdatas.push(prints);
        }
        else {
            if (prints.length > 0) {
                if (prints.length <= pageCount) {
                    plPrintdatas.push(prints);
                }
                else {
                    for (var i = 0; i < prints.length; i++) {
                        if (i < pageCount) {
                            if ((typeof (plPrintdatas[0]) == "undefined"))
                                plPrintdatas[0] = Array();
                            plPrintdatas[0].push(prints[i]);
                        }
                        else {
                            var a = Math.floor(i / pageCount); //+1;
                            if ((typeof (plPrintdatas[a]) == "undefined"))
                                plPrintdatas[a] = Array();
                            plPrintdatas[a].push(prints[i]);
                        }
                    }
                }
            }
        }
        return plPrintdatas;
    };


    //打印数据
    lodopObj.printData = function (lodop, hasView, printBoxIsShow, selectedPrinter, clearFunc, printOkFunc) {
        var dataObj = comp.Print.Data;

        if (selectedPrinter !== "" && this.checkPrinter(selectedPrinter)) {
            lodop.SET_PRINT_MODE("WINDOW_DEFPRINTER", selectedPrinter);
            lodop.SET_PRINTER_INDEX(selectedPrinter || -1 );
        }
        if (hasView) {
            lodop.SET_PRINT_MODE("RESELECT_ORIENT", true);
            lodop.SET_PRINT_MODE("RESELECT_PAGESIZE", true);
            lodop.SET_PRINT_MODE("RESELECT_COPIES", true);
            lodop.SET_PRINT_MODE("RESELECT_PRINTER", true);
            lodop.PREVIEW();
        } else {
            if (dataObj.printCopies || dataObj.printCopies > 1) {
                lodop.SET_PRINT_COPIES(dataObj.printCopies);
            }
            if (printBoxIsShow == 1) { //打印时是否显示系统打印机选择框判断
                if (dataObj.isFirstPrint) {
                    if (lodop.SELECT_PRINTER() >= 0) {
                        lodop.SET_SHOW_MODE("NP_NO_RESULT", false);
                        lodop.PRINT();
                        dataObj.isFirstPrint = false;
                        if (typeof printOkFunc == 'function') {
                            printOkFunc(); //回调
                            dataObj.isFirstPrint = true;
                        }
                    } else {
                        if (typeof clearFunc == 'function') clearFunc();
                        return false;
                    }
                } else {

                    lodop.SET_SHOW_MODE("NP_NO_RESULT", false);
                    lodop.PRINT();
                    if (typeof printOkFunc == 'function') printOkFunc(); //回调
                }
            } else {
                lodop.SET_SHOW_MODE("NP_NO_RESULT", false);
                lodop.PRINT();
                if (typeof printOkFunc == 'function') printOkFunc(); //回调
            }
        }
    };


    //计算文本框的应有的高度   针对发货单
    lodopObj.getInputHeight_lodop = function (width, height, letterspace, lineh, fontsize, isbn, str, isDefault, isjx, fhdFhdishb) {
        var that = new comp.Print.FN();
        var horCList = {};
        horCList.nowH = height;
        horCList.content = str;
        fontsize = that.getFontSizeByFlashSize_lodop(fontsize);//转换到打印出来实际的像素
        if (height > 0) {
            letterspace = letterspace == -1 ? 0 : letterspace;
            lineh = Math.round(fontsize * 0.25) + (lineh == -1 ? 1 : lineh); //getLineSpacing_lodop(fontsize,lineh);//实际的行间距
            if (isbn == -1) {
                var fhdIsb = fhdFhdishb;
                if (fhdIsb == true || fhdIsb == "1" || fhdIsb == 1) {
                    isbn = true;
                } else {
                    isbn = false;
                }
            }

            var nowWidth = 0;
            var pheight = fontsize + lineh;
            if (isjx) {
                for (var i = 0; i < horCList.content.length; i++) {
                    var strCode = horCList.content.charCodeAt(i);
                    var fz = 0;
                    if ((strCode > 255)) {
                        fz = fontsize;
                    } else {
                        fz = Math.round(fontsize / 2);
                    }
                    nowWidth = nowWidth + fz + letterspace;
                }
                var rowCount = Math.ceil(nowWidth / width);
                var hs = (horCList.content.toString().split("(nrt)")).length - 1; //判断多少个换行符
                if (horCList.content.toString().indexOf("(nrt)") > -1) {
                    horCList.content = horCList.content.toString().replace(/\(nrt\)/g, "\r\n");
                }
                horCList.nowH = pheight * (rowCount + hs);
            } else {
                if (horCList.content != "") {
                    var cenindex = 0;
                    var rowcount = Math.floor(horCList.nowH / pheight) == 0 ? 1 : Math.floor(horCList.nowH / pheight); //当前高度能打几行
                    var gdw = width * rowcount; //当前打印总像素
                    for (var i = 0; i < horCList.content.length; i++) {
                        var strCode = horCList.content.charCodeAt(i);
                        var fz = 0;
                        if ((strCode > 255)) {
                            fz = fontsize;
                        } else {
                            fz = Math.round(fontsize / 2);
                        }
                        nowWidth = nowWidth + fz + letterspace + 1;
                        if (gdw - 5 > nowWidth) { //如果当前像素小于总像素 累加
                            cenindex = i + 1;
                        } else break;
                    }
                    if (cenindex < horCList.content.length) {
                        horCList.content = horCList.content.substring(0, cenindex);
                    }
                }
            }

            if (isDefault == false) {

            }
            else {
                if (horCList.nowH < height) {
                    horCList.nowH = height;
                }
            }
        }
        return horCList;
    };
})(window);