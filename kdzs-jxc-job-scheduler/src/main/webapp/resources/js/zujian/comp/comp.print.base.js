/// <reference path="../../jquery-1.11.3.min.js" />
(function (window) {
    if (!window.console ) {
        var arr = [];
        var console = window.console = {};

        console.push = function (type, msg) {
            var obj = {}
            obj.type = type;
            obj.msg = msg;
            arr.push(obj);
        }

        console.clear = function () {
            arr.length = 0;
        }

        console.info = function (msg) {
            console.push("info", msg);
        }

        console.warn = function (msg) {
            console.push("warn", msg);
        }

        console.error = function (msg) {
            console.push("error", msg);
        }
        
        console.log = function (msg) {
            console.push("log",msg);
        }

        console.show = function () {
            return arr;
        }
    }
    window.getWebBrower = function () {
        var obj = {};
        obj.isIE = (navigator.userAgent.indexOf('MSIE') >= 0) || (navigator.userAgent.indexOf('Trident') >= 0);
        obj.is64IE = false;
        obj.isFF = false;
        if (obj.isIE && navigator.userAgent.indexOf('x64') >= 0) {
            obj.is64IE = true;
        }
        if (navigator.userAgent.indexOf('Firefox') >= 0) {
            obj.isFF = true;
        }
        return obj;
    }
    window.brower = getWebBrower();
    window.nameSpace = function (names) {
        var arr = names.split('.');
        var current = window;
        var index = 0;
        while (index < arr.length) {
            var name = arr[index];
            if (!current[name]) {
                current[name] = {};
            }
            current = current[name];
            index++;
        }
        return current;
    }
    String.prototype.startsWith = function (str) {
        var reg = new RegExp("^" + str);
        return reg.test(this);
    }
    String.prototype.format = function () {
        var args = arguments;
        return this.replace(/\{(\d+)\}/g,
        function (m, i) {
            return args[i];
        });
    }
    String.prototype.endsWith = function (str) {
        var reg = new RegExp(str + "$");
        return reg.test(this);
    }
    String.prototype.contain = function (str) {
        return this.indexOf(str)>-1;
    }
    Date.prototype.format = function (fmt) { //author: meizz   
        var o = {
            "M+": this.getMonth() + 1,
            "d+": this.getDate(),
            "h+": this.getHours(),
            "m+": this.getMinutes(),
            "s+": this.getSeconds(),
            "q+": Math.floor((this.getMonth() + 3) / 3),
            "S": this.getMilliseconds()
        };
        if (/(y+)/.test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (this.getFullYear() + "").substr(4 - RegExp.$1.length));
        }
        for (var k in o) {
            if (new RegExp("(" + k + ")").test(fmt)) {
                fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
            }
        }
        return fmt;
    }

    if (!Array.prototype.indexOf) {
        Array.prototype.indexOf = function (tar) {
            var reval = -1;
            for (var i = 0; i < this.length; i++) {
                if (this[i] == tar) {
                    return i;
                }
            }
            return reval;
        };
    }

    if (!Array.prototype.remove) {
        Array.prototype.remove = function (obj) {
            var index = this.indexOf(obj);
            if (index > -1) {
                this.splice(index, 1);
            }
        }
    }

    if (!Array.prototype.removeAt) {
        Array.prototype.removeAt = function (index) {
            if (index > -1) {
                this.splice(index, 1);
            }
        }
    }


    if (!Array.prototype.each) {
        Array.prototype.each = function (fun) {
            var len = this.length;
            for (var i = 0; i < len; i++) {
                var item = this[i];
                fun(i, item);
            }
        }
    }



    Array.prototype.findIndex = function (fun) {
        var len = this.length;
        for (var i = 0; i < len; i++) {
            var tem = this[i];
            var b = fun(tem);
            if (b) {
                return i;
            }
        }
        return -1;
    }

    window.getQueryString = function (name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) {
            return unescape(r[2]);
        }
        return null;
    }

    window.getRandomNum = function () {
        //var value = ~~10000 * Math.random();
        var charactors = "1234567890";
        var value = '', i;
        for (var j = 1; j <= 4; j++) {
            i = parseInt(10 * Math.random());
            value = value + charactors.charAt(i);
        }
        return parseInt(value);
    }

    /*
    // 得到省份简称
    */
    window._p30_getSfjcVal = function (sf) {
        if (sf.indexOf("北京") >= 0) {
            return "京";
        }
        if (sf.indexOf("上海") >= 0) {
            return "沪";
        }
        if (sf.indexOf("天津") >= 0) {
            return "津";
        }
        if (sf.indexOf("浙江") >= 0) {
            return "浙";
        }
        if (sf.indexOf("江苏") >= 0) {
            return "苏";
        }
        if (sf.indexOf("福建") >= 0) {
            return "闽";
        }
        if (sf.indexOf("广东") >= 0) {
            return "粤";
        }
        if (sf.indexOf("山东") >= 0) {
            return "鲁";
        }
        if (sf.indexOf("河南") >= 0) {
            return "豫";
        }
        if (sf.indexOf("四川") >= 0) {
            return "川";
        }
        if (sf.indexOf("湖北") >= 0) {
            return "鄂";
        }
        if (sf.indexOf("湖南") >= 0) {
            return "湘";
        }
        if (sf.indexOf("江西") >= 0) {
            return "赣";
        }
        if (sf.indexOf("重庆") >= 0) {
            return "渝";
        }
        if (sf.indexOf("河北") >= 0) {
            return "冀";
        }
        if (sf.indexOf("云南") >= 0) {
            return "云";
        }
        if (sf.indexOf("辽宁") >= 0) {
            return "辽";
        }
        if (sf.indexOf("黑龙江") >= 0) {
            return "黑";
        }
        if (sf.indexOf("安徽") >= 0) {
            return "皖";
        }
        if (sf.indexOf("山西") >= 0) {
            return "晋";
        }
        if (sf.indexOf("陕西") >= 0) {
            return "陕";
        }
        if (sf.indexOf("吉林") >= 0) {
            return "吉";
        }
        if (sf.indexOf("广西") >= 0) {
            return "桂";
        }
        if (sf.indexOf("甘肃") >= 0) {
            return "甘";
        }
        if (sf.indexOf("内蒙") >= 0) {
            return "蒙";
        }
        if (sf.indexOf("贵州") >= 0) {
            return "贵";
        }
        if (sf.indexOf("青海") >= 0) {
            return "青";
        }
        if (sf.indexOf("新疆") >= 0) {
            return "新";
        }
        if (sf.indexOf("宁夏") >= 0) {
            return "宁";
        }
        if (sf.indexOf("海南") >= 0) {
            return "琼";
        }
        if (sf.indexOf("西藏") >= 0) {
            return "藏";
        }
        if (sf.indexOf("澳门") >= 0) {
            return "澳";
        }
        if (sf.indexOf("香港") >= 0) {
            return "港";
        }
        return sf;
    }

    //人民币金额转大写程序 JavaScript版     
    //CopyRight Bardo QI     

    window.numToCny = function (money) {
        var cnNums = new Array("零", "壹", "贰", "叁", "肆", "伍", "陆", "柒", "捌", "玖"); //汉字的数字
        var cnIntRadice = new Array("", "拾", "佰", "仟"); //基本单位
        var cnIntUnits = new Array("", "万", "亿", "兆"); //对应整数部分扩展单位
        var cnDecUnits = new Array("角", "分", "毫", "厘"); //对应小数部分单位
        var cnInteger = "整"; //整数金额时后面跟的字符
        var cnIntLast = "元"; //整型完以后的单位
        var maxNum = 999999999999999.9999; //最大处理的数字

        var integerNum; //金额整数部分
        var decimalNum; //金额小数部分
        var chineseStr = ""; //输出的中文金额字符串
        var parts; //分离金额后用的数组，预定义

        if (money == "") {
            return "";
        }

        money = parseFloat(money);
        //alert(money);
        if (money >= maxNum) {
            $.alert('超出最大处理数字');
            return "";
        }
        if (money == 0) {
            chineseStr = cnNums[0] + cnIntLast + cnInteger;
            //document.getElementById("show").value=ChineseStr;
            return chineseStr;
        }
        money = money.toString(); //转换为字符串
        if (money.indexOf(".") == -1) {
            integerNum = money;
            decimalNum = '';
        } else {
            parts = money.split(".");
            integerNum = parts[0];
            decimalNum = parts[1].substr(0, 4);
        }
        if (parseInt(integerNum, 10) > 0) {//获取整型部分转换
            var zeroCount = 0;
            var intLen = integerNum.length;
            for (var i = 0; i < intLen; i++) {
                n = integerNum.substr(i, 1);
                p = intLen - i - 1;
                q = p / 4;
                m = p % 4;
                if (n == "0") {
                    zeroCount++;
                } else {
                    if (zeroCount > 0) {
                        chineseStr += cnNums[0];
                    }
                    zeroCount = 0; //归零
                    chineseStr += cnNums[parseInt(n)] + cnIntRadice[m];
                }
                if (m == 0 && zeroCount < 4) {
                    chineseStr += cnIntUnits[q];
                }
            }
            chineseStr += cnIntLast;
            //整型部分处理完毕
        }
        if (decimalNum != "") { //小数部分
            var decLen = decimalNum.length;
            for (var i = 0; i < decLen; i++) {
                n = decimalNum.substr(i, 1);
                if (n != '0') {
                    chineseStr += cnNums[Number(n)] + cnDecUnits[i];
                }
            }
        }
        if (chineseStr == '') {
            chineseStr += cnNums[0] + cnIntLast + cnInteger;
        }
        else if (decimalNum == "") {
            chineseStr += cnInteger;
        }
        return chineseStr;

    }

    /*
    * 是否存在指定函数 
    */
    window._isExitsFunction = function (funcName) {
        try {
            if (typeof (eval(funcName)) == "function") {
                return true;
            }

            //是否存在指定函数 
            function isExitsFunction(funcName) {
                if (typeof (variableName) != "string") {
                    return false;
                }
                try {
                    if (typeof (window[funcName]) == "function") {
                        return true;
                    }
                }
                catch (e) {

                }
                return false;
            }

            //是否存在指定变量 
            function isExitsVariable(variableName) {
                if (typeof (variableName) != "string") {
                    return false;
                }
                try {
                    if (typeof (window[variableName]) == "undefined") {
                        return false;
                    }
                    else {
                        return true;
                    }
                }
                catch (e) {

                }
                return false;
            }

        }
        catch (e) {

        }
        return false;
    }

    //对象拷贝
    Object.Copy = function (source) {
        var json = JSON.stringify(source);
        return JSON.parse(json);
    }
    // window.console.info("comp.print.base loaded");
})(window);
(function ($) {
    $.fn.fileUpload = function (url, fileName, beforeFun, successFun, errFun) {

        function createFrame() {
            var domid = "frame_" + (+new Date);
            var body = $("body");
            var frame = $('<iframe id="' + domid + '" name="' + domid + '" src="" />').appendTo(body);
            frame.css({ position: 'absolute', top: '-1000px', left: '-1000px', width: '100px', height: '100px' });
            return frame;
        }
        
        function setFormData(data, form) {
            form.find("input[type='hidden']").remove();
            for (var i in data) {
                $('<input  type="hidden" name="' + i + '" value="' + data[i] + '" />').appendTo(form);
            }
        }

        var dom = $(this);
        if (dom.length == 0) {
            alert("绑定上传控件为Null");
            return;
        }
        var form = dom.find("form");
        if (form.attr("upload")) {
            return;
        }
        var frame = createFrame();
        form.attr("upload","readyok");
        form.attr("action", url).attr("method", "post");
        form.attr("target", frame.attr("id")).attr("enctype", "multipart/form-data");
        var file = form.find("input[type='file']");
        file.attr("name", fileName);
        file.change(fileChange);

        function fileChange() {
            var target = $(this);
            var selectFile = target.val();
            if (!selectFile) {
                return false;
            }
            var fileType = selectFile.substr(selectFile.length - 3, 3);
            fileType = fileType.toLowerCase();
            if (fileType == "jpg" || fileType == "png" || fileType == "gif") {
                var data = {};
                beforeFun(data);
                setFormData(data,form);
                frame.load(frameCallBack);
                form.submit();
            } else {
                alert("仅支持png,jpg,gif格式的图片");
                return false;
            }
        }
            
        function frameCallBack() {
            file.unbind().val("");
            try {
                var dom = frame[0];
                var content = dom.contentWindow || dom.contentDocument;
                var text = content.document.body.innerHTML || null;
                // var json = $(text).text();
                var json = content.document.body.innerHTML || "";

                if (!json) {
                    throw new Error("服务端响应的JSON为NULL");
                }
                var dataObj = $.parseJSON(json);
                successFun(dataObj);
            }
            catch (err) {
                errFun(err);
            }
            finally {
                frame.unbind();
                file.change(fileChange);
            }
        }

    }
   // window.console.info("expand Jquery plugin fileUpload loaded");
})(jQuery);
(function ($) {
    $.fn.QlrrSilder = function () {
        var that = $(this);
        var arg = Array.prototype.slice.call(arguments);
        function init(obj) {
            if (!that.is("div")) {
                alert("必须依赖于Div创建");
                return that;
            }
            that.attr("class", "clearfix jdt_box");
            that.empty();
            var html = '<i name="name" class="float_l">横向缩放</i>\
					<span class="slideControlContainer float_l">\
						<span class="slide_control_bg">\
						<span class="slideControlFill" style="width: 100%;">\
							<span class="slideControlHandle"></span>\
						</span>\
						</span>\
					</span>\
					<i name="value" class="float_l">100%</i>';
            that.html(html);

            var dragSpan = that.find(".slideControlHandle");
            var dragParent = that.find(".slideControlFill");
            var iValue = that.find("i[name='value']");
            var iName = that.find("i[name='name']");

            var startX = 0;
            var dragging = false;
            var widthDom = that.find(".slide_control_bg");
            iName.html(obj.name);
            iValue.html(obj.value + "%").attr("maxValue", obj.value);
            dragParent.css("width", "100%");
            //鼠标按下
            dragSpan.mousedown(function (e) {
                dragging = true;
                startX = e.clientX - this.offsetLeft;
                this.setCapture && this.setCapture();
                return false;
            });

            //鼠标移动
            $(document).mousemove(function (e) {
                if (dragging) {
                    var totalWidth = widthDom.width();
                    var e = e || window.event;
                    var oX = e.clientX - startX;
                    var b = ~~((oX / totalWidth) * 100)
                    if (b < 0) {
                        b = 0;
                    }
                    if (b > 100) {
                        b = 100;
                    }
                    var value = b + "%";
                    dragParent.css("width", value);
                    var maxValue = +iValue.attr("maxValue");
                    value = ~~((oX / totalWidth) * maxValue);
                    if (value < 0) {
                        value = 0;
                    }
                    if (value > maxValue) {
                        value = maxValue;
                    }
                    iValue.html(value + "%");
                    return false;
                }
            });

            //鼠标弹起
            dragSpan.mouseup(function (e) {
                if (dragging) {
                    dragging = false;
                    this.releaseCapture && this.releaseCapture();
                    e.cancelBubble = true;
                    return false;
                }
            });

            //鼠标弹起
            $(document).mouseup(function (e) {
                if (dragging) {
                    dragging = false;
                    this.releaseCapture && this.releaseCapture();
                    e.cancelBubble = true;
                    return false;
                }
            });
            return that;
        }

        function setValue(arg) {
            var iValue = that.find("i[name='value']");
            var maxValue = +iValue.attr("maxValue");
            var b = (+arg) / maxValue;
            var ptem = ~~(b * 100);
            var dragParent = that.find(".slideControlFill");
            dragParent.css("width", ptem + "%");
            return that.find("i[name='value']").html(arg + "%");
        }

        function getValue() {
            var iValue = that.find("i[name='value']");
            return +iValue.html().replace("%", "");
        }

        function setName(arg) {
            var iName = that.find("i[name='name']");
            return iName.html(arg);
        }

        function getName(arg) {
            var iName = that.find("i[name='name']");
            return iName.html();
        }

        if (arg.length == 1) {
            var first = arg[0];
            if (typeof first == "object") {
                return init(first);
            }
            else if (first == "getValue") {
                return getValue();
            }
            else if (first == "getName") {
                return getName();
            }
        }

        if (arg.length == 2) {
            var first = arg[0];
            if (first == "setValue") {
                return setValue(arg[1]);
            }
            else if (first == "setName") {
                return setName(arg[1]);
            }
        }

        return that;
    }
    window.console.info("expand Jquery plugin QlrrSilder loaded");
})(jQuery);
(function ($) {

    $.fn.myHide = function () {
        return $(this).css("display", "none");
    }

    $.fn.myShow = function () {
        return $(this).css("display", "block");
    }

})(jQuery);
(function ($) {
    $.fn.shake = function () {
        return this.each(function (ind, inv) {
            var ele = $(inv);
            ele.addClass("btn_white_middle_js");
            shake(ele, "red", 6);
        });
    }

    function shake(ele, cls, times) {
        var count = 0;
        cls = cls || "red";
        times = times || 2;
        var t = setInterval(function () {
            count++;
            if (count % 2) {
                ele.addClass(cls);
            } else {
                ele.removeClass(cls);
            }
            if (count == times) {
                ele.removeClass(cls);
                clearInterval(t);
                ele.removeClass("btn_white_middle_js");
            }
        }, 200)
    };

})(jQuery);
(function ($) {
    $.fn.compNumberBox = function () {
        return this.each(function (ind, inv) {
            var tarDom = $(inv);
            var eventsData = $.data(inv, 'events') || $._data(inv, 'events');
            var isBind = eventsData && eventsData.compnumberbox;
            //判断是否绑定过该事件
            if (isBind) {
                return;
            }
            tarDom.attr("compnumberbox", "compnumberbox");
            tarDom.bind("keypress", function (e) {
                var current = tarDom.val();
                if (e.which == 45) {
                    if (current.indexOf("-") == -1) {
                        return true;
                    }
                    else {
                        return false;
                    }
                } else {
                    if ((e.which >= 48 && e.which <= 57 && e.ctrlKey == false && e.shiftKey == false) || e.which == 0 || e.which == 8) {
                        return true;
                    }
                    else {
                        if (e.ctrlKey == true && (e.which == 99 || e.which == 118)) {
                            return true;
                        }
                        else {
                            return false;
                        }
                    }
                }
            }).bind("blur", function () {
                var current = tarDom.val();
                current = +current;
                if (isNaN(current)) {
                    var oldVal = tarDom.attr("oldnumberval");
                    if (oldVal) {
                        tarDom.val(oldVal).change();
                    }
                    else {
                        tarDom.val("0");
                    }
                }
                else {
                    current = Math.floor(current);
                    if (current != tarDom.val()) {
                        tarDom.val(current).change();
                    }
                }
            }).bind("focus", function () {
                var current = tarDom.val();
                current = +current;
                if (isNaN(current)) {
                    var oldVal = tarDom.attr("oldnumberval");
                    if (oldVal) {
                        tarDom.val(oldVal);
                    }
                    else {
                        tarDom.val("0");
                    }
                }
                else {
                    current = Math.floor(current);
                    tarDom.val(current).attr("oldnumberval", current);
                }
            }).bind("compnumberbox", function () {

            })
        });
    }
})(jQuery);
(function ($) {
    $.fn.oneBind = function (bindMark,bindName,fun) {
        return this.each(function (ind, inv) {
            var eventsData = $.data(inv, 'events') || $._data(inv, 'events');
            var tarDom = $(inv);
            var isBind = eventsData && eventsData[bindMark];
            if (isBind) {
                return;
            }
            tarDom.bind(bindMark, function () { }).bind(bindName,fun);
        });
    }

    $.fn.oneChange = function (bindMark,fun) {
        return this.oneBind(bindMark, "change", fun);
    }

    $.fn.oneClick = function (bindMark, fun) {
        return this.oneBind(bindMark, "click", fun);
    }

})(jQuery);
(function ($) {
    var reg = /webkit/;
    $.isWebkit = function () {
        var userAgent = navigator.userAgent.toLowerCase();
        var b = reg.test(userAgent);
        return b;
    };
})(jQuery);
(function (window) {
    var comp = window.comp = window.comp || {};
    var base = comp.base = {};
    //弹出层
    base.showDialog = function (domid, isCloseEvent) {
        var dom = $("#" + domid);
        if (dom.length > 0) {
            var zhezhaoid = dom.attr("zhezhaoid");
            if (!zhezhaoid) {
                zhezhaoid = "zhezhao_" + (+new Date);
                var div = $("<div name='zhezhao'/>").appendTo($("body")).hide();
                div.addClass("pup_fullscreen").css("z-index", "1000").attr("id", zhezhaoid);
                div.attr("targetid", domid);
                dom.attr("zhezhaoid", zhezhaoid);
                if (isCloseEvent) {
                    div.click(function () {
                        var tarid = $(this).attr("targetid");
                        comp.base.closeDialog(tarid);
                    });
                }
            }
            var width = dom.width();
            var height = dom.height();
            var marginLeft = -(~~(width / 2));
            var marginTop = -(~~(height / 2));
            dom.css("position", "fixed").css("z-index", 1001).css("left", "50%").css("top", "50%").css("margin-left", marginLeft + "px").css("margin-top", marginTop + "px");
            $("#" + zhezhaoid).css("display", "block");
            dom.css("display", "block");
            var body = $(document.body);
            if (!body.data("overflowX")) {
                body.data("overflowX", body.css("overflow-x")).data("overflowY", body.css("overflow-y")).css("overflow", "hidden");
            }
        }
    }

    //关闭层
    base.closeDialog = function (domid) {
        var dom = $("#" + domid);
        if (dom.length > 0) {

            var body = $(document.body);
            if (body.data("overflowX")) {
                body.css("overflow-x", body.data("overflowX")).css("overflow-y", body.data("overflowY"));
                body.data("overflowX", null).data("overflowY", null);
            }

            var zhezhaoid = dom.attr("zhezhaoid");
            $("#" + zhezhaoid).css("display", "none");
            dom.css("display", "none");
        }
    }

    //重置弹窗位置
    base.showDialogLayout = function (domid) {
        var dom = $("#" + domid);
        if (dom.length > 0) {
            var width = dom.width();
            var height = dom.height();
            var marginLeft = -(~~(width / 2));
            var marginTop = -(~~(height / 2));
            dom.css("margin-left", marginLeft + "px").css("margin-top", marginTop + "px");
        }
    }

    //滚动条
    base.niceScroll = function (dom) {
        var obj = {
            cursorcolor: "#ccc", //#CC0071 光标颜色
            cursoropacitymax: 1, //改变不透明度非常光标处于活动状态（scrollabar“可见”状态），范围从1到0
            touchbehavior: false, //使光标拖动滚动像在台式电脑触摸设备
            cursorwidth: "5px", //像素光标的宽度
            cursorborder: "0", // 	游标边框css定义
            cursorborderradius: "5px", //以像素为光标边界半径
            autohidemode: false //是否隐藏滚动条
        };
        if ($.fn.niceScroll) {
            dom.niceScroll(obj);
        }
    };

    //获取屏幕类型
    base.getScreenType = function () {
        var sw = window.screen.width;
        var sh = window.screen.height;
        if (sw == 2736 && sh == 1824) {
            return 2;
        }
        if (sw == 1368 && sh == 912) {
            return 2;
        }
        if (sw == 3840 && sh == 2160) {
            return 4;
        }
        return 1;
    };

    //获取当前浏览器的缩放
    base.getBrowerZoom = function () {
        var ratio = 0,
        screen = window.screen,
        ua = navigator.userAgent.toLowerCase();

        if (window.devicePixelRatio !== undefined) {
            ratio = window.devicePixelRatio;
        }
        else if (~ua.indexOf('msie')) {
            if (screen.deviceXDPI && screen.logicalXDPI) {
                ratio = screen.deviceXDPI / screen.logicalXDPI;
            }
        }
        else if (window.outerWidth !== undefined && window.innerWidth !== undefined) {
            ratio = window.outerWidth / window.innerWidth;
        }
        if (ratio) {
            ratio = Math.round(ratio * 100);
        }
        return ratio;
    };

    //获取集包地 集包条形码
    base.getjbd = function (gxmdd, code) {
        var jbd = "";
        var jbds = gxmdd.split(";");
        for (var i = 0; i < jbds.length; i++) {
            if (jbds[i].indexOf(code + ",") > -1 || jbds[i].indexOf("AUTO,") > -1) {
                jbd = jbds[i].split(",")[1];
                break;
            }
        }
        return jbd;
    };

    //创建lodop实例
    base.createLodopDom=function(parent, id, width, height) {
        if (parent == null) {
            parent = $(document.body);
        }
        var ieHtml = "<object id=\"{0}\" classid=\"clsid:09896DB8-1189-44B5-BADC-D6DB5286AC57\" width=\"{1}\" height=\"{2}\"><param name='Color' value='#f0f0f0' /> <param name='Caption' value='快递助手' /></object> ";
        var noIEHtml = "<embed id=\"{0}\" type=\"application/x-cainiaoprint\" width=\"{1}\" height=\"{2}\" />";
        var html = brower.isIE ? ieHtml : noIEHtml;
        html = html.format(id, width, height);
        parent.append(html);
        var dom = $("#" + id)[0];
        if ((dom == null) || (typeof (dom.VERSION) == "undefined")) {
           // console.error("create lodop is error");
            return null;
        }
        //登陆appkey、seller_id 验证
        dom.SET_PRINT_IDENTITY("AppKey=" + comp.Print.Data.appkey + "&Seller_ID=" + comp.Print.Data.sellerid);
        dom.SET_LICENSES("北京格玩科技有限公司", "653587569718688748719056235623", "kuaidizs.com", "2F71232D415F674063F2FD9EFD7C6BBE75");
        return dom;
    }

    var browserObj = null;
    //获取当前浏览器
    base.getBrower = function () {
        var userAgent = navigator.userAgent.toLowerCase();
        var uaMatch;
        if (browserObj) {
            return browserObj;
        }
        browserObj = {};
        /**
         * 判断是否为ie
         */
        function isIE() {
            return ("ActiveXObject" in window);
        }
        /**
         * 判断是否为谷歌浏览器
         */
        if (!uaMatch) {
            uaMatch = userAgent.match(/chrome\/([\d.]+)/);
            if (uaMatch != null) {
                browserObj['name'] = 'chrome';
                browserObj['version'] = uaMatch[1];
            }
        }
        /**
         * 判断是否为火狐浏览器
         */
        if (!uaMatch) {
            uaMatch = userAgent.match(/firefox\/([\d.]+)/);
            if (uaMatch != null) {
                browserObj['name'] = 'firefox';
                browserObj['version'] = uaMatch[1];
            }
        }
        /**
         * 判断是否为opera浏览器
         */
        if (!uaMatch) {
            uaMatch = userAgent.match(/opera.([\d.]+)/);
            if (uaMatch != null) {
                browserObj['name'] = 'opera';
                browserObj['version'] = uaMatch[1];
            }
        }
        /**
         * 判断是否为Safari浏览器
         */
        if (!uaMatch) {
            uaMatch = userAgent.match(/safari\/([\d.]+)/);
            if (uaMatch != null) {
                browserObj['name'] = 'safari';
                browserObj['version'] = uaMatch[1];
            }
        }
        /**
         * 最后判断是否为IE
         */
        if (!uaMatch) {
            if (userAgent.match(/msie ([\d.]+)/) != null) {
                uaMatch = userAgent.match(/msie ([\d.]+)/);
                browserObj['name'] = 'ie';
                browserObj['version'] = uaMatch[1];
            } else {
                /**
                 * IE10
                 */
                if (isIE() && !!document.attachEvent && (function () { "use strict"; return !this; }())) {
                    browserObj['name'] = 'ie';
                    browserObj['version'] = '10';
                }
                /**
                 * IE11
                 */
                if (isIE() && !document.attachEvent) {
                    browserObj['name'] = 'ie';
                    browserObj['version'] = '11';
                }
            }
        }
        return browserObj;
    }
})(window);


 




