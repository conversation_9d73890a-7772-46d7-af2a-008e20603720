﻿(function(window) {
    var socket;
    var version = "v1";
    var cloudObj = nameSpace("comp.print.cloudprint");

    cloudObj.init = function(callback) {
        cloudObj.isInit = true;
      //  console.info("comp.print.cloudprint.init");
        cloudObj.doConnect();
        callback();
    }

    //-------------- CaiNiao 底层组件方法（供内部调用）------start-------

    //回调函数队列
    cloudObj.callbackMap = {};
    //debug模式,打印请求参数及响应参数
    cloudObj.debug = false;
    //开启错误提示模式,alert请求错误结果
    cloudObj.showFailMsg = false;

    //建立连接
    cloudObj.doConnect = function(success, error) {
        //需要进行异常捕获处理，收集一些莫名其妙的BUG
        try {
            //创建socket
            cloudObj.createWebSocket(success);
        } catch (e) {
           // console.log('doConnect CaiNiao: ' + e);
            error && error(e);
        }
    }

    /**
     * [createWebSocket 建立请求,绑定事件]
     * @param  {[type]} success [成功回调]
     */
    cloudObj.createWebSocket = function(success) {
            var that = this;
            //在尝试重连socket的时候，如果socket已存在，则无需再建立连接。
            //重建连接，是为了防止用户未开启菜鸟打印组件就直接打印的情况。
            if (this.checkedSocketExit()) {
                return;
            }
            //如果连接不存在，则尝试建立连接。
            var callbackMap = this.callbackMap;
            socket = new WebSocket("ws://localhost:13528");
            socket.onopen = function(event) {
              //  console.info('Client notified socket has opend');
                success && success(event);
                //监听消息
                socket.onmessage = function(event) {
                    var data = JSON.parse(event.data);
                    //执行预先定义的回调
                    callbackMap[data.cmd] && callbackMap[data.cmd](data);
                    //debug模式
                    if (that.debug) console.log(data.cmd + '命令回调:', data);
                    //错误提示模式
                    if (that.showFailMsg && event.status == 'failed') {
                        alert(event.message || '');
                    }
                }
            };
            socket.onclose = function(event) {
              //  console.log('Client notified socket has closed', event);
            }
            socket.onerror = function(event) {
              //  console.log('Client notified socket has error', event);
            }
        }
        /**
         * [sendSocketJSON 发送socket数据]
         * @param  {[type]} json [description]
         * @return {[type]}      [description]
         */
    cloudObj.sendSocketJSON = function(request) {
        if (this.checkedSocketExit()) { //连接存在则发送数据
            socket.send(request);
        } else {
           // console.log('socket连接不存在,检查是否已开启菜鸟打印组件，且刷新页面后重试打印操作');
            //alert('亲,请先开启菜鸟打印组件客户端,刷新页面后,再尝试打印~\n如未下载,请点击连接下载: http://cloudprint-software.oss-cn-shanghai.aliyuncs.com/CaiNiao%E6%89%93%E5%8D%B0%E7%BB%84%E4%BB%B6%E5%AE%89%E8%A3%85.exe');
        }
    };
    /**
     * [checkedSocketExit 判断socket连接是否存在]
     * @return {[type]} [description]
     */
    cloudObj.checkedSocketExit = function() {
        if (socket && socket.readyState == 1) { //连接存在则发送数据
            return true;
        }
        return false;
    };

    /**
     * 关闭socket连接
     */
    cloudObj.clearSocket = function() {
        socket.close && socket.close();
        socket = null;
    };

    //-请求编码-当前时间戳
    cloudObj.getRequestID = function() {
        var timestamp = (new Date()).valueOf();
        return timestamp;
    };
    /***
     *
     * 获取请求的UUID，指定长度和进制,如
     * getUUID(8, 2)   //"01001010" 8 character (base=2)
     * getUUID(8, 10) // "47473046" 8 character ID (base=10)
     * getUUID(8, 16) // "098F4D35"。 8 character ID (base=16)
     *
     */
    cloudObj.getUUID = function(len, radix) {
        var chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
        var uuid = [],
            i;
        radix = radix || chars.length;
        if (len) {
            for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix];
        } else {
            var r;
            uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
            uuid[14] = '4';
            for (i = 0; i < 36; i++) {
                if (!uuid[i]) {
                    r = 0 | Math.random() * 16;
                    uuid[i] = chars[(i == 19) ? (r & 0x3) | 0x8 : r];
                }
            }
        }
        return uuid.join('');
    };
    /***
     * 构造request对象
     */
    cloudObj.getRequestObject = function(cmd) {
        var request = {};
        request.requestID = this.getUUID(8, 16);
        request.version = "1.0";
        request.cmd = cmd;
        return request;
    };

    /**
     * [getCMD 指令构建，发送请求的入口]
     * @param  {[string]}   cmd [需要发送的指令名称]
     * @param  {[type]}   ext [指令参数扩展]
     * @param  {Function} fn  [回调函数]
     */
    cloudObj.getCMD = function(cmd, ext, fn) {
        this.callbackMap[cmd] = fn;
        var request = this.getRequestObject(cmd);
        $.extend(true, request, ext);
        if (this.debug) {
          //  console.log('执行打印机命令:', request);
        }
        this.sendSocketJSON(JSON.stringify(request));
    };

    //------------- CaiNiao 底层组件方法（供内部调用）------end------

    //--------------CaiNiao 底层组件方法（供外部调用）------start----

    //请求打印机列表
    cloudObj.getPrinters = function(callback) {
        this.getCMD("getPrinters", {}, callback);
    };

    /**
     * [printerConfig 获取打印机协议](呼起打印组件)
     * @param  {Function} callback [description]
     * @return {[type]}            [description]
     */
    cloudObj.printerConfig = function(callback) {
        this.getCMD("printerConfig", {}, callback);
    };

    /**
     * [setPrinterConfig 配置打印机协议]
     * @param {[object]}   params   [需要配置的字段]
     {
         name:  "Microsoft XPS Document Writer", //必传(打印机名字)
         needTopLogo : false,  //是否需要模板上联的快递logo
         needBottomLogo : false, //是否需要模板下联的快递logo
     }
     * @param {Function} callback [description]
     */
    cloudObj.setPrinterConfig = function(params, callback) {
        this.getCMD("setPrinterConfig", {
            printer: params || {}
        }, callback);
    };

    /**
     * [print 发送/预览打印数据协议]
     * @param  {[type]}   task   [电子面单数据]
     * @param  {Function} callback [description]
     * @return {[type]}            [description]
     */
    cloudObj.print = function(task, callback) {
        this.getCMD("print", { task: task }, callback);
    };

    /**
     * 请求打印任务状态协议
     * @param  {[Array]}   taskIDArray  [description]
     *  (传的是数组！数组！数组！！重要的事情说三遍)
        eg:['1']
     * @param  {Function} callback [description]
     * @return {[type]}            [description]
     */
    cloudObj.getPrintTaskStatus = function(taskIDArray, callback) {
        this.getCMD("getTaskStatus", { taskID: taskIDArray }, callback);
    };

    /**
     * [getWayBillPrintStatus 根据面单号查询打印任务]
     * @param  {[Array]} documentIDsArray [面单号]
        (传的是数组！数组！数组！！)
        eg:['9890000076011']
     * @param  {Function} callback    [description]
     * @return {[type]}               [description]
     */
    cloudObj.getWayBillPrintStatus = function(documentIDsArray, callback) {
        this.getCMD("getDocumentStatus", { documentIDs: documentIDsArray }, callback);
    };

    //--------------CaiNiao 底层组件方法（供外部调用）------end-----

    //-----------------------以下是业务组件方法--------------------
    /**
     * [isSlectPrinterInCainiaoPrinters 检查用户选择的打印机是否在菜鸟打印机列表里]
     * @param  {[type]}  caiNiaoPinters [菜鸟打印机列表]
     * @param  {[type]}  selectPrinter  [用户选择的打印机]
     * @return {Boolean}                [true,是;false,不是]
     */
    cloudObj.isSlectPrinterInCainiaoPrinters = function(caiNiaoPinters, selectPrinter) {
        var item;
        for(var i = 0, len = caiNiaoPinters.length;i < len; i ++) {
            item = caiNiaoPinters[i];
            if(selectPrinter == item.name) return true;
        }
        return false;
    }
    /**
     * [startPrint 开始打印]
     * @param  {[type]}  printdatas      [要打印的数据]
     * @param  {[type]}  templateSet     [模板内容]
     * @param  {[type]}  ptype           [快递单还是发货单]
     * @param  {Boolean} hasView         [是否预览]
     * @param  {[type]}  printBoxIsShow  [是否显示系统打印机选择框]
     * @param  {[type]}  selectedPrinter [已选择的打印机]
     * @param  {[type]}  printSynFunc    [打印异步执行方法，进度条等等]
     * @param  {[type]}  clearFunc       [选择打印机取消事件]
     * @param  {[type]}  printOkFunc     [打印成功后调用]
     */
    cloudObj.startPrint = function(printdatas, templateSet, ptype, hasView, printBoxIsShow, selectedPrinter, printSynFunc, clearFunc, printOkFunc) {
        var that = this, bInPrintList;
        var contx = new comp.Print.FN();
        //不使用外接传入的打印机.
        selectedPrinter = templateSet['ModeTempPrintcfg'].DefaultPrinter || '';
        that.debug && console.log('默认打印机:', selectedPrinter);
        //菜鸟获取打印机列表
        contx.getCloudDefaultPrinterList(function(data) {
            //如果用户设置打印机的不在菜鸟打印机列表里，则使用菜鸟的默认打印机
            bInPrintList = that.isSlectPrinterInCainiaoPrinters(data.printers, selectedPrinter);
            if(!bInPrintList) {
                selectedPrinter = data.defaultPrinter;
                that.debug && console.log('默认打印机不在菜鸟打印机里,使用菜鸟默认:', selectedPrinter);
            }
            that.debug && console.log('使用打印机:', selectedPrinter);
            print();
        });

        function print() {
            var data = cloudObj.formatDataForPrint({
                preview: false, //直接打印，不预览
                printer: selectedPrinter || contx.getDefaultPrinter(),
                printDatas: printdatas,
                templateSet: templateSet
            });

            that.debug && console.log('打印数据data:', data);

            cloudObj.print(data, function(event) {
                if ("success" == event.status) {
                  //  console.log('打印成功:');
                   // console.info(event);
                    if ($.isFunction(printOkFunc)) {
                        printOkFunc(event, data);

                    }
                } else {
                  //  console.log('打印失败:');
                   // console.info(event);
                }
            });
        }

    };

    /**
     * [formatTaskHeaderDataForPrint 构建打印任务信息]
     * @param  {[type]} params [description]
     * @return {[type]}        [description]
     */
    cloudObj.formatDataForPrint = function(params) {
        var printTaskId = parseInt(1000 * Math.random());
        var data = {
            taskID: '' + printTaskId,
            preview: params.preview || false,
            printer: params.printer,
            documents: cloudObj.formatDocumentsDataForPrint(params)
        }

        return data;
    };

    /**
     * [formatDataForPrint 格式化文档数据]
     * @return {[type]} [description]
     */
    cloudObj.formatDocumentsDataForPrint = function(params) {
        var that = this;
        var pData, tData,
            documents = [],
            dataItem,
            eleSurfaceArea, customArea;
        var contx = new comp.Print.FN()

        pData = params.printDatas;
        tData = params['templateSet'].ModeListShow;
        var excode = params['templateSet'].ModeList.Excode;
        $.each(pData, function(key, item) {
            //电子面单部分
            eleSurfaceArea = item.content;
            //自定义区部分
            if (item.custom.gx_mdd) {//目的地
                item.custom.gx_mdd = contx.getjbd(item.custom.gx_mdd, excode);
            }
            if (item.custom.gx_jbm) {//集包码
                item.custom.gx_jbm = contx.getjbd(item.custom.gx_jbm, excode);
            }
            if (item.custom.fjm) {//分拣码
                item.custom.fjm = contx.getjbd(item.custom.fjm, excode);
            }
            customArea = {
                templateURL: that.getCustomAreaTemplateURL(tData.Exuserid, tData.Mode_ListShowId),
                data: item.custom
            };
            //单个完整的document
            dataItem = {
                "documentID": eleSurfaceArea.data.waybillCode,
                "contents": [
                    eleSurfaceArea, customArea
                ]
            };
            documents.push(dataItem);
        });
        return documents;
    }

    /**
     * [getCustomAreaTemplateURL 获取电子面单自定义区部分的模板地址]
     * @param  {[type]} exuserId       [description]
     * @param  {[type]} modeListShowId [description]
     * @return {[type]}                [description]
     */
    cloudObj.getCustomAreaTemplateURL = function(exuserId, modeListShowId) {

        var origin, path, search, tURL;

        origin = window.location.origin;

        path = '/ashx/ZuJian/printnew/CloudPrintHandler.ashx';

        search = ['?',
            'action=GetCustomTemplateFile',
            'exuserId=' + exuserId,
            'modeListShowId=' + modeListShowId
        ].join('&');

        tURL = origin + path + search;

        return tURL;
    }
})(window);
