﻿/// <reference path="../../jquery-1.10.2.min.js" />
/// <reference path="comp.print.base.js" />
/// <reference path="comp.print.data.js" />
/// <reference path="comp.print.desc.js" />
/// <reference path="comp.print.lodop.js" />
/// <reference path="comp.print.resources.js" />
(function () {
    var comp = window.comp = window.comp || {};
    var instancePrint = null;
    var instancePrintFN = null;
    var readyFN = [];
    var isReadyWork = false;

    //核心构造函数
    comp.Print = function () {
        if (instancePrint) {
            return instancePrint;
        }
        return this;
    }

    //全局事件注册对象
    comp.Print.eventObj = {};

    //全局参数默认对象
    comp.Print.Data = {
        templateSet: {
            inputTextArray: [],
            moveAllY: null
        },
        exCompanys: [],
        pageHMmLodop: 0,
        pageWMmLodop: 0,
        A4Height: 1122,//A4纸的高度
        BatchPages: 3,//A4拼接时按纸张数传打印任务的纸张值
        LdSetOpen: true,
        pTop: 0,
        overH: 0,
        currentPage: 1,
        exuid: 0,
        exsubuid: 0,
        uk: null,
        ptype: null,
        appkey: null,
        isFirstPrint: true,
        exConfig_fhd: null,
        marginTop: 12,
        marginLeft: 13,
        sellerid: 0,
        mmToPxUnit: 3.78,//毫米像素转换比例
        printTaskArr_lodop: [],
        kddTemplates: {
            ModeListShowId: null,
            ModeListShows: []
        },
        pageType:"batch",//页面类型 默认为批打页面
        kddTempInfos: [], //模版详细信息
        fhdTempInfos:[],//发货单模版详细信息
        printExampleData: null,
        kddGroupCount: 10, //快递单每组的数量
        fhdGroupCount: 10, //发货单单每组的数量
        globalSettingKdd: {}, //快递单全局设置
        globalSettingFhd: {}, //发货单全局设置
        templateDefaultItem: [], //编辑模版时的默认选项
        systemFonts: [], //系统字体
        yunzhanUserSetting: null, //用户云栈设置
        yunzhanSetting: null, //云栈设置信息
        netPointSetting: null, //网点设置信息,
        defaultLayoutTemplate: [],
        platform:"normal",
        serviceListWidth: 0,//服务选项宽度
        isSinglePrint: false,//是否是单打
        isLinkedItem: true,//发货单打印是否开启关联表格
        linkedIndex: 0,//发货单打印关联项索引
        isFirstLinkedItem: false,//发货单是否是一个关联项
        linkedSize: 0,//关联项高度
        tempDefaultFontSize: 0,//模板默认字号
        tempDefaultFontName: "",//模板默认字体
        printCopies: 1,//打印份数
        jhdSet:null,
        shopLogo: null,
        registerMethods: {},
        modeType: 0, //快递单打印模式，默认0，lodop打印；1，菜鸟打印。
        checkLodopDom: 0,
        jsVersion:+new Date
    };

    //全局参数初始化
    //exuid：数据库用户id  exsubuid：用户子帐号id  uk：sessionId安全验证；  ptype：1 ；appkey:'', sellerid：taobaoId；modeType : 快递单打印模式，默认0，lodop打印；1，菜鸟打印。  
    comp.Print.init = function (exuid, exsubuid, uk, ptype, appkey, sellerid,platform,modeType,jsVersion) {
        exuid = +exuid;
        exsubuid = +exsubuid;
        comp.Print.isReadyWork = isReadyWork;
        comp.Print.Data = $.extend(comp.Print.Data, {
            exuid: exuid,
            exsubuid: exsubuid,
            uk: uk,
            ptype: ptype,
            appkey: appkey,
            sellerid: sellerid,
            platform:platform,
            modeType: modeType || 0,
            jsVersion:jsVersion
        });
    }

    //检验是否安装菜鸟控件
    comp.Print.checkCainiao = function () {
       // console.info("comp.Print.checkCainiao");
        var body = $(document.body);
        if (body.length==0) {
            alert("不允许在dom尚未装载时进行校验");
            return null;
        }
        if (comp.Print.Data.checkLodopDom == 0) {
            
            var fn = new comp.Print.FN();
            var id = "sysCheckCainiao_" + (+new Date);
            comp.Print.Data.checkLodopDom = fn.createLodopDom(null, id, 0, 0);
            return comp.Print.Data.checkLodopDom;
        }
        return comp.Print.Data.checkLodopDom;
    }

    //校验闪打
    comp.Print.checkCloudPrint = function (callback) {
        var fn = new comp.Print.FN();
        return fn.creatCaiNiaoWebSocket(callback);
    }

    //组件准备就绪,注册应用函数
    comp.Print.ready = function (fn) {
        if(comp.Print.isReadyWork){
            fn.call(instance);
        }else{
            var index = readyFN.indexOf(fn);
            if (index == -1) {
                readyFN.push(fn);
            }
        }
    }

    //========MM改动=====================/
    //校验是否支持Lodop
    comp.Print.prototype.checkLodop = function () {
        return new comp.Print.FN().checkLodop();
    }
    //获取用户设置的云栈旺店信息
    comp.Print.prototype.getYunZhanUserSetting = function(exuserId, subUserId, modeListShowId, exid, callback){
        return new comp.Print.FN().getYunZhanUserSetting(exuserId, subUserId, modeListShowId, exid, callback);
    }
    //===================================/

    //注册方法
    comp.Print.registerMethod = function (methodName, method) {
        return new comp.Print.FN().bindEvent(methodName, method);
    }

    //设置打印份数
    comp.Print.setPrintCopies = function ( printCopies ) {
        comp.Print.Data.printCopies = printCopies || 1;
    }

    //取消方法
    comp.Print.unRegisterMethod = function (methodName) {
        var rObj = comp.Print.Data.registerMethods;
        if (rObj && rObj[methodName]) {
            rObj[methodName] = {};
            rObj[methodName].methods = [];
        }
    }

    //开始执行
    comp.Print.doWork = function () {
      //  console.info("comp.Print is do");
        initPrint();
    }

    //获取浏览器缩放率
    comp.Print.prototype.getBrowerZoom = function () {
        return new comp.Print.FN().getBrowerZoom();
    }

    //设置是否单打
    comp.Print.prototype.setIsSinglePrint = function (isSinglePrint) {
        comp.Print.Data.isSinglePrint = !!isSinglePrint;
    }

    //设置默认打印机
    comp.Print.prototype.setDefaultPrinter = function (ptype, printerName, mkddid, callback) {
        return new comp.Print.FN().setDefaultPrinter(ptype, printerName, mkddid, callback);
    }

    //绑定快递单模版改变事件
    comp.Print.prototype.KddTemplateListChanged = function (fun) {
        if ($.isFunction(fun)) {
            return new comp.Print.FN().bindEvent("KddTemplateListChanged",fun);
        }
        else {
            console.error("外界绑定的快递单模版改变事件传入的参数不是Function");
        }
    }

    ////新增快递单
    comp.Print.prototype.addKdd = function (callback) {
        return new comp.Print.FN().addKdd(callback);
    }

    //删除快递单
    comp.Print.prototype.delKdd = function () {
        return new comp.Print.FN().setGolbalSet();
    }

    ////获取快递单列表
    comp.Print.prototype.getKddList = function () {
        var list = new comp.Print.FN().getKddList();
        return list;
    }

    ////获取快递单详情
    comp.Print.prototype.getTemplateInfo = function (templateType, mkddid, isAsync) {
        if (templateType == "kdd") {
            return new comp.Print.FN().getTemplateInfo(mkddid,isAsync);
        }
        else if (templateType == "fhd") {
            var fnPrint = new comp.Print.FN();
            mkddid = fnPrint.getDefaultFhdMkddid();
            return new comp.Print.FN().getFhdTemplateInfo(mkddid);
        }
        return "";
    }

    //快递单主页面
    comp.Print.prototype.showTemplateMain = function (templateType,defId,printDatas) {
        if (templateType=="kdd") {
            return new comp.Print.FN().showkddMain(defId,printDatas);
        }
        else if (templateType == "fhd") {
            return new comp.Print.FN().showFhdMain(defId,printDatas);
        }
        else if (templateType == "jhd") {
            return new comp.Print.FN().showJhdMain(false);
        }
        return "";
    }

    //关闭主界面
    comp.Print.prototype.closeTemplateInfo = function (templateType) {
        return new comp.Print.FN().closeTemplateInfo(templateType);
    }


    //获取默认发货单MKDDID
    comp.Print.prototype.getDefaultFhdMkddid = function () {
        return new comp.Print.FN().getDefaultFhdMkddid();
    }

    //获取默认快递MKDDID
    comp.Print.prototype.getDefaultKddMkddid = function () {
        return new comp.Print.FN().getDefaultKddMkddid();
    }

    //打印
    //printdatas 打印数据
    //mkddid 模版id
    //ptype 快递单或者发货单
    //hasview 是否预览
    //printBoxIsShow  是否在打印前弹出选择框
    //selectPrinter 默认打印机
    //printSynFunc 打印进度回调
    //clearFun 打印选择框选择取消时触发
    //printOkFunc 打印结束时调用
    comp.Print.prototype.printTemplate = function (printdatas, mkddid, ptype, hasView, printBoxIsShow, selectedPrinter, printSynFunc, clearFunc, printOkFunc) {
        var fnPrint=new comp.Print.FN();
        var lodop = fnPrint.getHiddenLodop();
        var templateSet = null;
        if (ptype == 'kdd') {
            templateSet = fnPrint.getTemplateInfo(mkddid);
        }
        else {
            mkddid = fnPrint.getDefaultFhdMkddid();
            templateSet = fnPrint.getFhdTemplateInfo(mkddid);
        }
        return fnPrint.printTemplate(lodop, printdatas, templateSet, ptype, hasView, printBoxIsShow, selectedPrinter, printSynFunc, clearFunc, printOkFunc);
    }

    //打印拣货单或备货单
    //printdatas 打印数据
    //ptype 拣货单或备货单
    //hasview 是否预览
    //printBoxIsShow  是否在打印前弹出选择框
    //intOrient 打印方向 默认1
    //pageSize 页数
    comp.Print.prototype.printJHDBHD = function (printdatas, ptype, intOrient, pageSize, hasView, defPrinter,skuSum) {
        return new comp.Print.FN().printJHDBHD(printdatas, ptype, intOrient, pageSize, hasView, defPrinter,skuSum);
    }

    //获取打印机列表
    comp.Print.prototype.getPrinters = function () {
        return new comp.Print.FN().getPrinters();
    }

    //获取默认打印机
    comp.Print.prototype.getDefaultPrinter = function () {
        return new comp.Print.FN().getDefaultPrinter();
    }

    //获取原始模拟数据
    comp.Print.prototype.getInitPrintData = function () {
        return new comp.Print.FN().getInitPrintData();
    }

    //获取发货单模版列表
    comp.Print.prototype.getFhdTemplateList = function() {
        return new comp.Print.FN().getFhdTemplateList();
    }

    //获取跨底单模版列表 含默认模版id
    comp.Print.prototype.getKddListAndMkddId = function () {
        return new comp.Print.FN().getKddListAndMkddId();
    }

    //打印Html
    comp.Print.prototype.printHtml = function ( html, width, height, intOrient, pageSize, hasView, modeid, defPrinter) {

        return new comp.Print.FN().printHtml(html, width, height, intOrient, pageSize, hasView, modeid, defPrinter);
    }

    //保存Html到Excel
    comp.Print.prototype.saveAsExcel = function (name, width, height, html) {
        return comp.Print.FN().saveAsExcel(name, width, height, html);
    }

    /****************************************/

    //执行方法
    comp.Print.FN = function () {
        if (instancePrintFN) {
            return instancePrintFN;
        }
        instancePrintFN = this;
        return this;
    }


    //获取当前浏览器的缩放
    comp.Print.FN.prototype.getBrowerZoom = function () {
        return comp.base.getBrowerZoom();
    }

    //获取当前浏览器
    comp.Print.FN.prototype.getBrower = function () {
        return comp.base.getBrower();
    }

    //获取屏幕类型
    comp.Print.FN.prototype.getScreenType = function () {
        return comp.base.getScreenType();
    }

    //关闭注解main
    comp.Print.FN.prototype.closeTemplateInfo = function (ptype) {
        return comp.print.resources.closeTemplateInfo(ptype);
    }

    //保存拣货单设置
    comp.Print.FN.prototype.updateJhdSet = function (jhdSet, callback, errorcallback) {
        return comp.print.data.updateJhdSet(jhdSet,callback,errorcallback);
    }

    //拣货单主界面
    comp.Print.FN.prototype.showJhdMain = function (isNoShow) {
        return comp.print.resources.showJHDMain(isNoShow);
    }

    //重置弹窗位置
    comp.Print.FN.prototype.showDialogLayout = function (domid) {
        return comp.base.showDialogLayout(domid);
    }

    //获取默认发货单MKDDID
    comp.Print.FN.prototype.getDefaultFhdMkddid = function () {
        return this.getFhdTemplateList().ModeListShowId;
    }

    //获取默认快递MKDDID
    comp.Print.FN.prototype.getDefaultKddMkddid = function () {
        return comp.Print.Data.kddTemplates.ModeListShowId;
    }

    //获取系统默认打印机
    comp.Print.FN.prototype.getDefaultPrinter = function () {
        return comp.print.lodop.getDefaultPrinter();

    }

    //设置默认打印机
    comp.Print.FN.prototype.setDefaultPrinter = function (ptype, printerName, mkddid, callback) {
        var temObj = null;
        var modeInfo = null;
        if (ptype == "fhd") {
            mkddid = this.getDefaultFhdMkddid();
            modeInfo = this.getFhdTemplateInfo(mkddid);
        }
        else {
            modeInfo = this.getTemplateInfo(mkddid);
        }

        temObj = modeInfo.ModeTempPrintcfg;
        if (temObj) {
            temObj.DefaultPrinter = printerName;
            this.saveDefaultPrinter(mkddid,temObj.Id, printerName, function () {
                if ($.isFunction(callback)) {
                    callback();
                }
            });
        }
    }

    ////获取快递单列表
    comp.Print.FN.prototype.getKddList = function () {
        return comp.Print.Data.kddTemplates.ModeListShows;
    }

    //获取快递单默认列表 含默认默认id
    comp.Print.FN.prototype.getKddListAndMkddId = function () {
        return comp.Print.Data.kddTemplates;
    }

    //注册事件
    comp.Print.FN.prototype.bindEvent = function (eventName, fun) {
        var list = comp.Print.eventObj[eventName];
        if (!list) {
            list = comp.Print.eventObj[eventName] = [];
        }
        //同样的事件避免注册多次
        var index = $.inArray(fun,list);
        index > -1 ? list.splice(index,1) : "";
        list.push(fun);
    }

    //使表格进入编辑状态
    comp.Print.FN.prototype.setTableForEdit = function (table) {
        return comp.print.resources.setTableForEdit(table);
    }

    //获取tableHtml
    comp.Print.FN.prototype.getFhdTableHtml = function (tempDatas, modeInputs,modeSet,modeInfo) {
        return comp.print.resources.getFhdTableHtml(tempDatas, modeInputs,modeSet,modeInfo);
    }

    //保存发货单默认模版样式
    comp.Print.FN.prototype.saveFhdDefaultTemplate = function (modeListShowId, callback) {
        return comp.print.data.saveFhdDefaultTemplate(modeListShowId,callback);
    }

    //获取发货单模版详细信息
    comp.Print.FN.prototype.getFhdTemplateInfo = function (modeListShowId) {
        return comp.print.data.getFhdTemplateInfo(modeListShowId);
    }

    //获取发货单模版列表
    comp.Print.FN.prototype.getFhdTemplateList = function () {
        return comp.print.data.getFhdTemplateList();
    }

    //初始化标记了initType的dom内容
    comp.Print.FN.prototype.initTypeDom = function (dom) {
        return comp.print.data.initTypeDom(dom);
    }


    //发货单编辑表格
    comp.Print.FN.prototype.editFhdTable = function (data, callback) {
        return comp.print.resources.editFhdTable(data, callback);
    }


    //发货单全局设置
    comp.Print.FN.prototype.setFhdGolbalSet = function (callback) {
        return comp.print.resources.setFhdGolbalSet(callback);
    }

    //获取隐藏的Lodop
    comp.Print.FN.prototype.getHiddenLodop = function () {
        return comp.print.lodop.getHiddenLodop();
    }

    //恢复模版布局
    comp.Print.FN.prototype.recoveryDefaultTemplate = function (exid,ptype) {
        return comp.print.data.recoveryDefaultTemplate(exid,ptype);
    }

    //系统字体
    comp.Print.FN.prototype.getSystemFonts = function () {
        return comp.print.data.getSystemFonts();
    }

    //获取快递单编辑时的选项
    comp.Print.FN.prototype.getTemplateDefaultItem = function () {
        return comp.print.data.getTemplateDefaultItem();
    }

    //保存快递单全局设置
    comp.Print.FN.prototype.saveKddGlobalSetting = function (obj, callback) {
        return comp.print.data.saveKddGlobalSetting(obj, callback);
    }

    //保存发货单全局设置
    comp.Print.FN.prototype.saveFhdGlobalSetting = function (obj, callback) {
        return comp.print.data.saveFhdGlobalSetting(obj, callback);
    }

    //获取全局设置
    comp.Print.FN.prototype.getKddGlobalSetting = function (exuserId, subUserId, type, callback) {
        return comp.print.data.getKddGlobalSetting(exuserId, subUserId, type, callback);
    }

    //保存模版地图
    comp.Print.FN.prototype.saveTemplateBgImg = function (modeListShowId, bgImgId, imgSrc, callback) {
        return comp.print.data.saveTemplateBgImg(modeListShowId, bgImgId, imgSrc, callback);
    }

    //全局设置
    comp.Print.FN.prototype.setGolbalSet = function (callback) {
        return comp.print.resources.setGolbalSet(callback);
    }

    //设置模版底图
    comp.Print.FN.prototype.setTempUnderlay = function (companyId, kddtype, styleId, modeShowId, callback) {
        return comp.print.resources.setTempUnderlay(companyId, kddtype, styleId, modeShowId, callback);
    }

    //编辑快递模版
    comp.Print.FN.prototype.editKdd = function (tempInfo, callback) {
        return comp.print.resources.editKdd(tempInfo, callback);
    }

    //获取模拟数据
    comp.Print.FN.prototype.getPrintData = function (type) {
        var printObj = comp.print.data.getPrintData(type);
        if (comp.Print.Data.shopLogo) {
            printObj.logo = comp.Print.Data.shopLogo.LogoSrc;
        }
        else {
            printObj.logo = "";
        }
        return printObj;
    }

    //获取原始模拟数据对象
    comp.Print.FN.prototype.getInitPrintData = function () {
        var initPrintObj = new comp.print.desc.PrintData();
        var setting = comp.Print.Data.globalSettingFhd.FjrSet || {};
        initPrintObj.o_shop = setting.FhdShopname;
        initPrintObj.o_title = setting.FhdTitle;
        initPrintObj.o_tel = setting.FhdTel;
        initPrintObj.o_name = setting.FhdName;
        initPrintObj.o_info = setting.FhdInfo;
        if (comp.Print.Data.shopLogo) {
            initPrintObj.logo = comp.Print.Data.shopLogo.LogoSrc;
        } else {
            initPrintObj.logo = "";
        }
        return initPrintObj;
    }

    //绘制
    comp.Print.FN.prototype.draw = function (mode, lodop, exData, type, showType) {
        if (type == 'fhd') {
            var arr = [exData];
            this.getFhdTableHtml(arr, mode.ModeInputs, mode.ModeSet, mode);
        }
        this.loadPrintItem(lodop, exData, mode, type, true, showType,0);
    }

    //创建lodop元素
    comp.Print.FN.prototype.createLodopDom = function (parent, id, width, height) {
        return comp.base.createLodopDom(parent, id, width, height);
    }

    //保存默认打印机
    comp.Print.FN.prototype.saveDefaultPrinter = function (mkddid,configId, printerName, callback) {
        return comp.print.data.saveDefaultPrinter(mkddid,configId, printerName, callback);
    }

    //获取模版的详细信息
    comp.Print.FN.prototype.getTemplateInfo = function (tempid,isAsync) {
        return comp.print.data.getTemplateInfo(tempid,isAsync);
    }

    //保存模版排序和删除
    comp.Print.FN.prototype.saveSortKdd = function (obj, callback,errorback) {
        return comp.print.data.saveSortKdd(obj, callback,errorback);
    }

    //删除上传的模版底图
    comp.Print.FN.prototype.delAddKddImg = function (bgImgId, callback) {
        return comp.print.data.delAddKddImg(bgImgId, callback);
    }

    //上传模版底图
    comp.Print.FN.prototype.uploadImgAddKdd = function (btnId, fileName, beforeFun, successFun) {
        return comp.print.data.uploadImgAddKdd(btnId, fileName, beforeFun, successFun);
    }

    //上传图片
    comp.Print.FN.prototype.uploadCustomImg = function (btnId, fileName, beforeFun, successFun) {
        return comp.print.data.uploadCustomImg(btnId, fileName, beforeFun, successFun);
    }


    //上传店标
    comp.Print.FN.prototype.uploadShopLogo = function (btnId, fileName, beforeFun, successFun) {
        return comp.print.data.uploadShopLogo(btnId, fileName, beforeFun, successFun);
    }

    //快递单主页面
    comp.Print.FN.prototype.showkddMain = function (defId,printDatas) {
        return comp.print.resources.showkddMain(defId,printDatas);
    }

    //展示发货单主界面
    comp.Print.FN.prototype.showFhdMain = function (defId, printDatas) {
        return comp.print.resources.showFhdMain(defId, printDatas);
    }


    //构造一个实例
    comp.Print.FN.prototype.addKdd = function (callback) {
        return comp.print.resources.addKdd(callback);
    }


    //保存新增的快递单
    comp.Print.FN.prototype.saveAddKdd = function (obj, callback) {
        return comp.print.data.saveAddKdd(obj, callback);
    }

    //弹出层
    comp.Print.FN.prototype.showDialog = function (domid,isBindClose) {
        return comp.base.showDialog(domid,isBindClose);
    }

    //关闭弹出层
    comp.Print.FN.prototype.closeDialog = function (domid) {
        return comp.base.closeDialog(domid);
    }

    //美化滚动条
    comp.Print.FN.prototype.niceScroll = function (dom) {
        //return comp.base.niceScroll(dom);
        dom.css("overflow","hidden").css("overflow-y","auto");
        return dom;
    }

    //获取业务类型
    comp.Print.FN.prototype.getKddWorkType = function (excode, callback) {
        return comp.print.data.getKddWorkType(excode, callback);
    }

    //获取服务类型
    comp.Print.FN.prototype.getKddServiceType = function (excode, callback) {
        return comp.print.data.getKddServiceType(excode, callback);
    }

    //获取增值服务类型
    comp.Print.FN.prototype.getExAdvancedServices = function (excode, callback) {
        return comp.print.data.getExAdvancedServices(excode, callback);
    }
    //获取五联单底图
    comp.Print.FN.prototype.getKddBgImgBy = function (companyId, kddtype, styleId, isDefault, callback) {
        return comp.print.data.getKddBgImgBy(companyId, kddtype, styleId, isDefault, callback);
    };

    //获取面单尺寸
    comp.Print.FN.prototype.getKddSize = function (companyId, kddtype, callback) {
        return comp.print.data.getKddSize(companyId, kddtype, callback);
    };

    //获取面单样式
    comp.Print.FN.prototype.getKddStyle = function (companyId, kddtype, height, callback) {
        return comp.print.data.getKddStyle(companyId, kddtype, height, callback);
    };

    //获取云栈设置信息 东方：2016.05.18
    comp.Print.FN.prototype.getYunZhanSetting = function (exuserId, subUserId, excode, modeListShowId, exid, callback) {
        return comp.print.data.getYunZhanSetting(exuserId, subUserId, excode, modeListShowId, exid, callback);
    };

    //获取用户的云栈设置信息 东方：2016.05.18
    comp.Print.FN.prototype.getYunZhanUserSetting = function (exuserId, subUserId, modeListShowId, exid, callback) {
        return comp.print.data.getYunZhanUserSetting(exuserId, subUserId, modeListShowId, exid, callback);
    };

    //保存云栈网点店铺代码信息 东方：2016.05.31
    comp.Print.FN.prototype.saveYunZhanShopCode = function (exuserId, modeListShowId, shopCode, callback) {
        return comp.print.data.saveYunZhanShopCode(exuserId, modeListShowId, shopCode, callback);
    };

    //删除云栈网点店铺代码信息 东方：2016.05.31
    comp.Print.FN.prototype.deleteYunZhanShopCode = function (exuserId, modeListShowId, callback) {
        return comp.print.data.deleteYunZhanShopCode(exuserId, modeListShowId, callback);
    };

    //删除用户的云栈设置信息 东方：2016.05.18
    comp.Print.FN.prototype.deleteYunZhanSetting = function (exuserId, subUserId, modeListShowId, exid, callback) {
        return comp.print.data.deleteYunZhanSetting(exuserId, subUserId, modeListShowId, exid, callback);
    };

    //保存用户的云栈设置信息 东方：2016.05.18
    comp.Print.FN.prototype.saveYunZhanSetting = function (obj, callback) {
        return comp.print.data.saveYunZhanSetting(obj, callback);
    };

    //获取网点电子面单的设置信息 东方：2016.05.18
    comp.Print.FN.prototype.getBranchSetting = function (exid, exuserId, subUserId, modeListShowId,exCode, callback) {
        return comp.print.data.getBranchSetting(exid, exuserId, subUserId, modeListShowId,exCode, callback);
    };

    //编辑模板保存 无忌：2016.05.31
    comp.Print.FN.prototype.saveBranchSetting = function (obj, callback) {
        return comp.print.data.saveBranchSetting(obj, callback);
    };

    //保存网点电子面单的设置信息 东方：2016.05.20
    comp.Print.FN.prototype.SaveTemplate = function (obj, callback) {
        return comp.print.data.SaveTemplate(obj, callback);
    };

    //实例方法
    comp.Print.FN.prototype.getKddTypeByExCode = function (companyId, callback) {
        return comp.print.data.getKddTypeByExCode(companyId, callback);
    }

    //校验是否支持Lodop
    comp.Print.FN.prototype.checkLodop = function () {
        return comp.print.lodop.checkLodop();
    }

    //获取所有打印机
    comp.Print.FN.prototype.getPrinters = function () {
        return comp.print.lodop.getPrinters();
    }

    //获取客户端信息
    comp.Print.FN.prototype.getLodopInfo = function (info) {
        return comp.print.lodop.getLodopInfo(info);
    }

    /////////////////////////////////////////////////////////////////////////////
    //保存Html到Excel
    comp.Print.FN.prototype.saveAsExcel = function (name, width, height, html) {
        return comp.print.lodop.saveAsExcel(name, width, height, html);
    }

    //打印Html
    comp.Print.FN.prototype.printHtml = function (html, width, height, intOrient, pageSize, hasView, modeid, defPrinter) {
        return comp.print.lodop.printHTML(this.getHiddenLodop(), html, width, height, intOrient, pageSize, hasView, modeid, defPrinter);
    }

    //获取连打设置
    comp.Print.FN.prototype.getLDSet = function (mkddid) {
        return comp.print.data.getLDSet(mkdid);
    }

    //获取快递单设置
    comp.Print.FN.prototype.getKddSet = function (mkddid) {
        return comp.print.data.getKddSet(mkddid);
    }

    //获取当前所选快递
    comp.Print.FN.prototype.getNowKddSet = function () {
        return comp.print.data.getNowKddSet();
    }

    //获取FHD设置
    comp.Print.FN.prototype.getFhdSet = function () {
        return comp.print.data.getFhdSet();
    }

    //读取菜鸟快递模板对象
    comp.Print.FN.prototype.getExTemp = function (mkddid) {
        return comp.print.data.getExTemp(mkddid);
    }

    //保存连打设置
    comp.Print.FN.prototype.saveLDSet = function (isfhd, mkddid, width, height, printName, a4Mode, a4Line, sheer, callback) {
        return comp.print.data.saveLDSet(isfhd, mkddid, widht, height, printName, a4Mode, a4Line, sheer, callback);
    }

    //连打设置开启关闭
    comp.Print.FN.prototype.updateLDStaus = function (status, callback) {
        return comp.print.data.updateLDStaus(status, callback);
    }

    //连打设置
    comp.Print.FN.prototype.printCtrl = function (type, mkddid, selnum) {
        return comp.print.lodop.printCtrl(type, mkddid, selnum);
    }

    //替换Flash控件的打印方法
    comp.Print.FN.prototype.printTemplate = function (lodop, printdatas, templateSet, ptype, hasView, printBoxIsShow, selectedPrinter, printSynFunc, clearFunc, printOkFunc) {
        if (1 == comp.Print.Data.modeType && ptype == "kdd" && templateSet.ModeList.KddType == 3 && templateSet.ModeList.StyleId == 2) {
           // console.log('使用云打印~');
            return comp.print.cloudprint.startPrint(printdatas, templateSet, ptype, hasView, printBoxIsShow, selectedPrinter, printSynFunc, clearFunc, printOkFunc);
        }else {
            if (ptype == 'fhd') {
                this.getFhdTableHtml(printdatas, templateSet.ModeInputs, templateSet.ModeSet, templateSet);
            }
            return comp.print.lodop.printTemplate(lodop, printdatas, templateSet, ptype, hasView, printBoxIsShow, selectedPrinter, printSynFunc, clearFunc, printOkFunc);
        }
    }

    //lodop打印模版
    comp.Print.FN.prototype.printLodopTemplate = function (lodop, printdatas, templateSet, ptype, hasView, printBoxIsShow, selectedPrinter, printSynFunc, clearFunc, printOkFunc) {
        return comp.print.lodop.printTemplate(lodop, printdatas, templateSet, ptype, hasView, printBoxIsShow, selectedPrinter, printSynFunc, clearFunc, printOkFunc);
    }
    //打印拣货单或备货单
    comp.Print.FN.prototype.printJHDBHD = function (printdatas, ptype, intOrient, pageSize, hasView, defPrinter,skuSum) {
        if (ptype == "jhd") {
            var jhdSet = comp.Print.Data.jhdSet;
            this.showJhdMain(true);
            this.createTableHtml(printdatas,jhdSet);
            var height = document.getElementById('div_jhd_left').clientHeight + 400;
            if (skuSum) {
                height = skuSum * 200;
            }
            var width = document.getElementById('div_jhd_left').clientWidth + 50;
            var html=document.getElementById("div_jhd_left").innerHTML;
            return this.printHtml(html, width, height, intOrient, pageSize, hasView, ptype, defPrinter);
        }
    }

    comp.Print.FN.prototype.createTableHtml = function (goodsJHDs,jhdSet) {
        return comp.print.resources.createTableHtml(goodsJHDs,jhdSet);
    }




    //重置按纸张打印时所需的全局变量
    comp.Print.FN.prototype.resetVal = function (type) {
        var printData = comp.Print.Data;
        printData._pTop = 0;
        printData._overH = 0;
        printData._currentPage = 1;
        this.setGlobal_lodop(type);
    }

    //计算打印的数据需要分成几个批次打印
    comp.Print.FN.prototype.getPrintModelsPLByCount = function (prints, pageCount) {
        return comp.print.lodop.getPrintModelsPLByCount(prints, pageCount);
    }

    //对发货单文本框数组进行排序,针对发货单
    comp.Print.FN.prototype.sortInputArr_lodop = function (inputArr) {
        return comp.print.lodop.sortInputArr_lodop(inputArr);
    }


    //菜鸟批打分组
    comp.Print.FN.prototype.getPrintModelsPL = function (printdatas, count) {
        return comp.print.lodop.getPrintModelsPL(printdatas, count);
    }

    //打印的全局设置
    comp.Print.FN.prototype.setGlobal_lodop = function (type, lodop,isDef) {
        return comp.print.lodop.setGlobal_lodop(type, lodop,isDef);
    }

    //打印当前任务数组中的任务
    comp.Print.FN.prototype.drawItem = function (lodop, printTask, startTop, showType) {
        return comp.print.lodop.drawItem(lodop, printTask, startTop, showType);
    }

    //打印数据
    comp.Print.FN.prototype.printData = function (lodop, hasView, printBoxIsShow, selectedPrinter, clearFunc, printOkFunc) {
        return comp.print.lodop.printData(lodop, hasView, printBoxIsShow, selectedPrinter, clearFunc, printOkFunc);
    }

    //通过字段类型名从用户传过来的打印数据中获取字段值
    comp.Print.FN.prototype.getValueByTypeName_lodop = function (dataJson, protoName) {
        if (dataJson[protoName]) {
            return dataJson[protoName];
        }
        else {
            if (protoName.indexOf("tb_hh") > -1) {
                return "(nrt)";
            }
            else if (protoName == "tb_xh") {
                return "0";
            }
            return "";
        }
    }

    //获取格式化后的集包地与集包码
    comp.Print.FN.prototype.getjbd = function (gxmdd, code) {
        return comp.base.getjbd(gxmdd,code);
    }

    //计算文本框的应有的高度   针对发货单
    comp.Print.FN.prototype.getInputHeight_lodop = function (width, height, letterspace, lineh, fontsize, isbn, str, isDefault, isjx, fhdFhdishb) {
        return comp.print.lodop.getInputHeight_lodop(width, height, letterspace, lineh, fontsize, isbn, str, isDefault, isjx, fhdFhdishb);
    }

    //根据用户快递模板ID，得到CODE
    comp.Print.FN.prototype.getExCode = function (mkddid) {
        var code = "";
        var kddModel = this.getKddSet(mkddid);
        if (kddModel != null) {
            code = kddModel.excode;
        }
        return code;
    }

    //把数组转换为对象 保留数组中的字段类型 针对发货单
    comp.Print.FN.prototype.arrTojson_lodop = function (arr) {
        var rJson = {};
        for (var arri = 0; arri < arr.length; arri++) {
            var arrItem = arr[arri].split('=');
            if (arrItem[1] != "" && !isNaN(arrItem[1])) {
                arrItem[1] = parseInt(arrItem[1]);
            }
            rJson[arrItem[0]] = arrItem[1];
        }
        return rJson;
    }

    //通过打印项坐标数组得到当前要打印项的坐标  返回的坐标需要加上当前页的top值
    comp.Print.FN.prototype.getPrintItemXY = function (printItemArr, printItem) {
        var rtnY = printItem.itemY;
        var tempY = printItem.itemY;
        for (var i = 0; i < printItemArr.length; i++) {
            var printi = printItemArr[printItemArr.length - (i + 1)];
            if (printItem.itemY < (printi.itemY + printi.itemH) || (printi.itemH - printi.oldH) != 0 || (printi.itemY - printi.oldY) != 0) {
                var befcsum = this.isOverlap(printItem, printItemArr, 1);
                printItem.itemY += (printi.itemH - printi.oldH) + (printi.itemY - printi.oldY);
                var aftcsum = this.isOverlap(printItem, printItemArr, 0);
                if (befcsum.length >= aftcsum.length) {
                    if (befcsum.length == aftcsum.length) {
                        if (befcsum.join(",") == aftcsum.join(",")) {
                            rtnY += (printi.itemH - printi.oldH) + (printi.itemY - printi.oldY);
                            break;
                        }
                    } else {
                        rtnY += (printi.itemH - printi.oldH) + (printi.itemY - printi.oldY);
                        break;
                    }
                }
            }
        }
        return rtnY;
    }

    //通过打印项坐标判断两个矩形是否重叠
    comp.Print.FN.prototype.isOverlap = function (rc1, printItemArr, type) {
        var ret = [];
        for (var i = 0; i < printItemArr.length; i++) {
            var rc2 = printItemArr[printItemArr.length - (i + 1)];
            if (type == 1) {
                if (rc1.itemX + rc1.itemW > rc2.itemX && rc2.itemX + rc2.itemW > rc1.itemX && rc1.oldY + rc1.itemH > rc2.oldY && rc2.oldY + rc2.oldH > rc1.oldY) {
                    ret.push(i);
                }
            } else {
                if (rc1.itemX + rc1.itemW > rc2.itemX && rc2.itemX + rc2.itemW > rc1.itemX && rc1.itemY + rc1.itemH > rc2.itemH && rc2.itemY + rc2.itemH > rc1.itemY && rc2.itemH > 0)
                { ret.push(i); }
            }
        }
        return ret;
    }

    //把表格数据传入打印任务中 针对发货单合并模式
    comp.Print.FN.prototype.printHBTableDataToTask_lodop = function (data, tbTempJson, rtnData, fhdFontsize) {
        return comp.print.lodop.printHBTableDataToTask_lodop(data, tbTempJson, rtnData, fhdFontsize);
    }

    //把表格数据传入打印任务中 针对发货单非合并模式
    comp.Print.FN.prototype.printNoHBTableDataToTask_lodop = function (data, tbTempJson, rtnData, fhdFontsize) {
        return comp.print.lodop.printNoHBTableDataToTask_lodop(data, tbTempJson, rtnData, fhdFontsize);
    }

    //通过控件字体的大小获取需要设置的行间距  一般为负数
    comp.Print.FN.prototype.getLineSpacing_lodop = function (fontsize, hjj) {
        fontsize = this.getFontSizeByFlashSize_lodop(fontsize);
        var defaultLineP = 0.2; //控件的行间距默认百分比
        var minusLineP = 0.1; //需要减去的行间距百分比 剩下的就是默认的高度百分比
        if (hjj == -1) {
            hjj = 0;
        }
        var diffsize = -Math.ceil(minusLineP * fontsize) + hjj;
        return diffsize;
    }

    //通过控件字体的大小转换为flash字体的大小（控件和flash字体有偏差）
    comp.Print.FN.prototype.getFontSizeByFlashSize_lodop = function (fontsize) {
        var diffP = 0.7; //flash和控件的字体大小相差百分比
        return Math.round(fontsize / diffP);
    }

    //通过flash字体的大小转换为控件字体的大小（控件和flash字体有偏差）
    comp.Print.FN.prototype.getFontSize_lodop = function (fontsize) {
        var diffP = 0.7;
        //flash和控件的字体大小相差百分比
        return Math.ceil(diffP * fontsize);
    }

    //通过行高和表格内容的高度获取表格内容在表格中应该下移的高度
    comp.Print.FN.prototype.getDiffHeight_lodop = function (rowH, nowH) {
        return Math.round((rowH - nowH) / 2);
    }

    //替换指定的标签
    comp.Print.FN.prototype.replaceHH = function (str) {
        var hs = (str.toString().split("(nrt)")).length - 1; //判断多少个换行符
        if (str.toString().indexOf("(nrt)") > -1) {
            str = str.toString().replace(/\(nrt\)/g, "\r\n");
        }
        return str;
    }


    //获取最新ID
    comp.Print.FN.prototype.getNewLodopKey = function (lodop ) {
        return comp.print.lodop.getNewLodopKey(lodop);
    }

    //指定lodop背景图
    comp.Print.FN.prototype.setLodopBgImg = function (lodop,imgSrc) {
        return comp.print.lodop.setLodopBgImg(lodop, imgSrc);
    }

    //绘制打印视图
    comp.Print.FN.prototype.loadPrintItem = function (lodop, printDataItem, templateSet, templateType, isShowTempImg, showType, starTop) {
        return comp.print.lodop.loadPrintItem(lodop, printDataItem, templateSet, templateType, isShowTempImg, showType, starTop);
    }


    // 根据input对象返回打印任务
    comp.Print.FN.prototype.getPrintTaskByInput = function (inputJson, printDataItem, templateSet) {
        return comp.print.lodop.getPrintTaskByInput(inputJson, printDataItem, templateSet);
    }

    // 批量绘制打印对象
    comp.Print.FN.prototype.printTasksDraw = function (topY, lodop) {
        return comp.print.lodop.printTasksDraw(topY, lodop);
    }

    /*************************
    *创建服务选项的html
    *serviceItems  服务选项实体
    *printData 打印数据实体
    *printData 是否是菜鸟官方模板
    ************************/
    comp.Print.FN.prototype.createServceListHTML = function (serviceItems, printData, isCainiao,rootWidth) {
        return comp.print.lodop.createServceListHTML(serviceItems, printData, isCainiao, rootWidth);
    }

    //获取系统默认打印机(云打印方法，异步模型)
    comp.Print.FN.prototype.getCloudDefaultPrinter = function (callback) {
        return comp.print.cloudprint.getPrinters(function(data) {
            callback(data.defaultPrinter);
        });
    }
    //获取系统默认打印机列表(云打印方法，异步模型)
    comp.Print.FN.prototype.getCloudDefaultPrinterList = function (callback) {
        return comp.print.cloudprint.getPrinters(callback);
    }

    //弹出菜鸟打印组件(云打印方法，异步模型)
    comp.Print.FN.prototype.printerConfig = function (callback) {
        return comp.print.cloudprint.printerConfig(callback);
    }

    //配置打印机协议(云打印方法，异步模型)
    comp.Print.FN.prototype.setPrinterConfig = function (params, callback) {
        return comp.print.cloudprint.setPrinterConfig(params, callback);
    }

    //检测菜鸟打印组件是否开启，异步模型，慎用。
    comp.Print.FN.prototype.creatCaiNiaoWebSocket = function (callback) {
        return creatCaiNiaoWebSocket(callback);
    }


    //==================================================================================

    //初始化js模块加载
    function initPrint() {
        var jsVersion = comp.Print.Data.jsVersion;
        loadJs("/resources/js/zujian/comp/comp.print.data.js?v=" + jsVersion, checkState);
        loadJs("/resources/js/zujian/comp/comp.print.desc.js?v=" + jsVersion, checkState);
        loadJs("/resources/js/zujian/comp/comp.print.lodop.js?v=" + jsVersion, checkState);
        if(isCaiNiaoCloudPrint()) {
            loadJs("/resources/js/zujian/comp/comp.print.cloudprint.js?v=" + jsVersion, checkState);
        }
        loadJs("/resources/js/zujian/comp/comp.print.resources.js?v=" + jsVersion, checkState);
       // console.info("load modle is over");
    }

    //校验Js是否加载完毕
    function checkState() {
        if (comp && comp.print) {
            var obj = comp.print;
            if (obj.data && (!obj.data.isCheckInitState)) {
                obj.data.isCheckInitState = true;
                obj.data.init(callBackModel);
            }

            if (obj.desc && (!obj.desc.isCheckInitState)) {
                obj.desc.isCheckInitState = true;
                obj.desc.init(callBackModel);
            }

            if (obj.lodop && (!obj.lodop.isCheckInitState)) {
                obj.lodop.isCheckInitState = true;
                obj.lodop.init(callBackModel);
            }
            if (obj.cloudprint && (!obj.cloudprint.isCheckInitState)) {
                obj.cloudprint.isCheckInitState = true;
                obj.cloudprint.init(callBackModel);
            }

            if (obj.resources && (!obj.resources.isCheckInitState)) {
                obj.resources.isCheckInitState = true;
                obj.resources.init(callBackModel);
            }

        }
    }

    //准备就绪开始页面逻辑
    function readyWork() {
        instance = new comp.Print();
        if (!isReadyWork) {
            $.each(readyFN,
            function (index, fn) {
                fn.call(instance);
            });
            isReadyWork = true;
            comp.Print.isReadyWork = isReadyWork;
        }
    }

    //模块回调方法模块初始化完成后回调
    function callBackModel() {
        var descObj = comp.print.desc;
        var resourceObj = comp.print.resources;
        var lodopObj = comp.print.lodop;
        var dataObj = comp.print.data;
        var cloudprintObj;
        if(isCaiNiaoCloudPrint()) {
            cloudprintObj = comp.print.cloudprint || {};
            cloudprintObjIsInit =  cloudprintObj.isInit || false;
        }else {
            cloudprintObj = true;
            cloudprintObjIsInit = true;
        }

        if ((!descObj) || (!dataObj) || (!lodopObj) || (!resourceObj) || (!cloudprintObj)) {
            return;
        }

        //所有模块初始化完成后调用
        if (comp.print.data.isInit && comp.print.desc.isInit && comp.print.resources.isInit && comp.print.lodop.isInit && cloudprintObjIsInit) {
           // console.info("readyWork");
            readyWork();
        }
    }


    //动态加载JS文件
    function loadJs(url, callback) {
        var done = false;
        var script = document.createElement('script');
        script.type = 'text/javascript';
        script.language = 'javascript';
        script.src = url;
        script.onload = script.onreadystatechange = function () {
            if (!done && (!script.readyState || script.readyState == 'loaded' || script.readyState == 'complete')) {
                done = true;
                if (script.onload) {
                    script.onload=null;
                }
                if (script.onreadystatechange) {
                    script.onreadystatechange = null;
                }
                if ($.isFunction(callback)) {
                    callback();
                }
            }
        }
        document.getElementsByTagName("head")[0].appendChild(script);
    }
    //判断是否是菜鸟云打印
    function isCaiNiaoCloudPrint() {
        return comp.Print.Data.modeType == '1' ? true: false;
    }

    //检测菜鸟打印组件是否打开(异步的)（可用性待验证）
    function creatCaiNiaoWebSocket(callback) {
        if(window.WebSocket) {//浏览器支持Websocket
            var socket = new WebSocket("ws://localhost:13528");
            socket.onopen = function(event) {
                socket && socket.close();
                //延迟一段时间等待socket关闭
                setTimeout(function() {
                    socket = null;
                    var arg = {
                        isSupport: true,
                        status:0
                    };
                    callback(arg);
                },300);
            };
            socket.onerror = function(event) {
               socket && socket.close();
               //延迟一段时间等待socket关闭
               setTimeout(function() {
                   socket = null;
                   var arg = {
                       isSupport: false,
                       status: 2
                   };
                   callback(arg);
               },300);
            }
        }
        else {
            var arg = {
                isSupport: false,
                status: 1
            };
            callback(arg);
        }
    };

})();