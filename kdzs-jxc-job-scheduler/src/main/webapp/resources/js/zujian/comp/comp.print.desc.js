﻿/// <reference path="comp.print.data.js" />
/// <reference path="comp.print.lodop.js" />
/// <reference path="comp.print.resources.js" />
/// <reference path="comp.print.base.js" />
/// <reference path="comp.Print.js" />
(function (window) {
    var descObj = nameSpace("comp.print.desc");
    var cacheObj = {};
    comp.print.desc.init = function (callback) {
        comp.print.desc.isInit = true;
        console.info("comp.print.desc.init");
        callback();
    }

    //单个打印对象描述
    descObj.PrintTask = function () {
        this.content = ""; //内容
        this.itemKey = "";//Key
        this.dataType = "txt";//数据项type
        this.itemY = 0;//Y
        this.itemX = 0;//X
        this.itemW = 0;//W
        this.itemH = 0;//H
        this.fontSize = 0;//字体大小
        this.fontName = "";//字体
        this.stretch = 2; //图片缩放
        this.txmType = "128Auto"//条形码类型
        this.lineStyle = 0;//线条类型
        this.readOnly = 0;//纯文本内容在打印维护时，是否禁止修改
        this.bold = -1;//是否加粗
        this.lineSpacing = -1;//行间距
        this.letterSpacing = -1;//字间距
        this.isEdit = 1;//是否允许控件被修改删除
        this.isMove = 1;//是否允许控件被移动
        this.direction = 0;//打印方向 默认横向
    }

    //打印数据的构造函数
    descObj.PrintData = function () {
        // 发件人
        this.f_name = "";

        // 发件人电话
        this.f_tel = "";

        // 发件地址
        this.f_addr = "";

        // 发货信息
        this.f_info = "";

        // 发件日期  //  属性：(单选)当天 后一天，(多选)是否显示时分秒
        this.f_date = "";

        // 发件旺旺  //  属性：(单选)[当前登陆为子旺旺时]只显示主旺旺 显示子旺旺 只显示:后边的部分
        this.f_ww = "";

        // 卖家备注
        this.f_memo = "";

        // 发件人签名
        this.f_qm = "";

        // 发件邮编
        this.f_zip = "";

        // 收件人
        this.s_name = "";

        // 收件手机
        this.s_phone = "";

        // 收件固话
        this.s_tel = "";

        // 收件省
        this.s_p = "";

        // 收件市 // 一级市
        this.s_city = "";

        // 收件区 // 县或区
        this.s_q = "";

        // 收件地址 // 用户输入的部分，与省市区重复的部分在CS阶段过滤掉
        this.s_addr = "";

        // 收件街道
        this.s_addrall = "";

        // 买家旺旺
        this.s_ww = "";

        // 订单编号 
        this.s_tid = "";

        // 收件邮编
        this.s_zip = "";

        // 买家留言
        this.s_message = "";

        // 数量
        this.count = "";

        // 目的地 // 属性：(单选)[是否显示省]显示/不显示 (单选)[一级市时显示]市/市+区  (单选)[二级市时显示]二级市/一级市+二级市  (单选)[县时显示]县/一级市+县 
        this.mdd = "";

        // 代收金额 // 属性：(单选)[是否显示"元"字] 显示/不显示
        this.dsje = "";

        // LC编号
        this.lc = "";

        // 实付金额 // 属性：(单选)[是否显示"元"字] 显示/不显示
        this.ddje = "";

        // 发货单号
        //this.fhdnum = "";


        // 店铺名称
        this.o_shop = "";

        // 发货单标题
        this.o_title = "";

        // 卖家姓名
        this.o_name = "";

        // 联系电话
        this.o_tel = "";

        // 友情提示
        this.o_info = "";

        // 下单时间
        this.xdate = "";

        // 付款时间
        this.fdate = "";

        // 店标
        this.logo = "";

        // 店标w
        this.logow = 0;

        // 店标h
        this.logoh = 0;

        // 满就送
        this.mjs = "";


        // 当前的地址组ID（非批量中有用，用来打印完成调用打印标识设置时使）
        this.addrGroupID = 0;


        // 是否是分销
        this.isFx = "0";

        // 是否是货到付款
        this.isCod = "0";


        // 是否是试用订单
        this.isSy = "0";


        // 发货单表格
        this.table = "";


        // 发货单表格_标题
        this.table_title = "";


        // 发货单表格_合计
        this.table_hj = "";

        // 合计_数量
        this.hj_sl = "";
        this.hj_yf = "";
        this.hj_yh = "";
        this.hj_sf = "";


        // 发货单表格_列 最多15列
        this.table_r_0 = "";
        this.table_r_1 = "";
        this.table_r_2 = "";
        this.table_r_3 = "";
        this.table_r_4 = "";
        this.table_r_5 = "";
        this.table_r_6 = "";
        this.table_r_7 = "";
        this.table_r_8 = "";
        this.table_r_9 = "";
        this.table_r_10 = "";
        this.table_r_11 = "";
        this.table_r_12 = "";
        this.table_r_13 = "";
        this.table_r_14 = "";

        // 产品ID
        this.tb_pid = "";
        // 发货单表格_序号
        this.tb_xh = "";

        // 发货单表格_图片
        this.tb_img = "";

        // 发货单表格_宝贝标题
        this.tb_tit = "";

        // 发货单表格_简称
        this.tb_jc = "";

        // 发货单表格_商家编码(宝贝)
        this.tb_sjbm_b = "";

        // 换行
        this.tb_hh1 = "";
        this.tb_hh2 = "";
        this.tb_hh3 = "";

        // 发货单表格_商家编码(款号)
        this.tb_sjbm_kh = "";

        // 发货单表格_产品规格
        this.tb_cpgg = "";

        // 发货单表格_产品规格(全，未替换过过滤字)
        this.tb_cpggAll = "";

        // 发货单表格_数量
        this.tb_count = "";

        // 发货单表格_单价
        this.tb_dj = "";

        // 发货单表格_优惠
        this.tb_yh = "";

        // 发货单表格_实付
        this.tb_sf = "";


        // 发货单表格_分销商实付
        this.tb_fx_sf = "";


        // 重量(单个宝贝的重量)
        this.tb_zhongl = "0";


        // 如果一个宝贝拍了N个型号，则对应的传N条数据
        this.tableDataArray = null;

        // 分销的时候记录采购单上的 分销商总价
        this.fx_cgd_pay = "0.00";


        // 快递单号(打在发货单上)
        this.fhdexnumber = "";

        // 货款总计(买家付的)(单项产品实付+运费)(实付金额中有可能用积金宝等积份，所以货款总计有时会比实付总额高)    
        this.hkzj = "0.00";


        // 子订单ID
        this.tb_oid = "";


        // 发票抬头
        this.invoice = "";
        // 图片(一个框如果出现这个选项，则不会显示其它的选项，图片地址存在前文字中而不在此处，他是一个模板上固定的，和订单无关)
        // 如果是从FLASH本身的库中取图片，则同时把要取的标签写在后文字处 [EMS][ZJS]
        this.pic = "";

        // 线条1像素，2像素，3像素(一个框如果出现这个选项，则不会显示其它的选项，框的XY是起始点，如果画横线则w有值h为0，画坚线则h有值w为0)
        this.line1 = "";
        this.line2 = "";
        this.line3 = "";

        // 订单条形码(一个框如果出现这个选项，则不会显示其它的选项，本身是数据源) 框的前文字处标记条形码的规格：A\B\C\EAN128
        this.txm_tid = "0123456789";

        // 运单号条形码(一个框如果出现这个选项，则不会显示其它的选项，本身是数据源) 框的前文字处标记条形码的规格：A\B\C\EAN128
        this.txm_number = "1234567890";

        // 二维码(一个框如果出现这个选项，则不会显示其它的选项，本身是数据源)
        this.ewm = "http://www.taobao.com";


        // 电子面单目的地
        this.mdd_dzmd = "目的地";


        // 总重量
        this.zhliang = "";

        // 付款方式
        this.fkfs = "";

        // 保价金额
        this.bjje = "";

        // 保价费用
        this.bjfy = "";

        // 签单返回
        this.qdfh = "";

        // 始发地 - 电子面单中用
        this.dzmd_sf = "";

        // 到达地 - 电子面单中用和目的地不同
        this.dzmd_dd = "";


        //有一些快递的电子面单内容项没有明确的说明，以下几个代码区码不同的快递间有不同的意义
        this.dzmd_dm01 = "";
        this.dzmd_dm02 = "";
        this.dzmd_dm03 = "";
        this.dzmd_dm04 = "";
        this.dzmd_dm05 = "";

        // 收件单位(没有此项时用收件人姓名数据)
        this.sjdw = "";

        // 始发网点
        this.sfwd = "";

        // 到达网点
        this.ddwd = "";

        // 用户自定义二维码(内容在前文字处)
        this.ewm_str = "";

        // 体积
        this.tiji = "";

        // 省份简称
        this.sfjc = "";

        // 包裹码
        this.bgm = ""; //"res/ExImg/user/logo/aaaaaa.png"; //"/img/print/mst.jpg";

        // 顺丰月结账号\卡号
        this.cutid = "";

        // 固定字符串
        this.str_1 = ""; //每个快递模板只能赋值一次

        this.s_ky = "";
        // 个性目的地  (快递间用英文的;分隔，快递CODE与值间用英文的,分隔) TTKD,值;YTO,值
        this.gx_mdd = "个性目的地";

        // 个性集包码   (快递间用英文的;分隔，快递CODE与值间用英文的,分隔) TTKD,值;YTO,值
        this.gx_jbm = "个性集包码";

        // 条形码_集包码  (快递间用英文的;分隔，快递CODE与值间用英文的,分隔) TTKD,值;YTO,值
        this.txm_jbm = "123456";

        // 打印序号条形码
        this.txm_printid = "1";

        // 打印序号
        this.printid = "1";
        // 包裹总数
        this.baoguoCount = "1";
        // 包裹编号
        this.baoguoID = "1";
        //@@@@@@@dm 分拣码
        // 分拣码
        this.fjm = "1";
        // 发货单时设置发货单信息
        this.o_shop = "店铺名称";
        this.o_title = "快递单标题";
        this.o_tel = "联系电话";
        this.o_name = "经办人";
        this.o_info = "友情提示";
        // 快递单时设置 发货信息
        this.f_info = "发货信息";
        //字母件数量
        this.zmj_sum = "";
        //字母件子单号
        this.zmj_mnum = "";
        // 菜鸟面单字段值
        this.printconfig = "";
    }
})(window);