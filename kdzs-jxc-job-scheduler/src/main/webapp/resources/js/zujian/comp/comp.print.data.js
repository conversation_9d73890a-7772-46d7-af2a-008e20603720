/// <reference path="../../jquery-1.10.2.min.js" />
/// <reference path="ajaxfileupload.js" />
/// <reference path="comp.Print.js" />
/// <reference path="comp.print.base.js" />
(function (window) {

    var dataObj = nameSpace("comp.print.data");
    var ajaxInitExCompany = false;
    var ajaxInitTemplates = false;
    var ajaxGlobalSettingKdd = false;
    var ajaxGlobalSettingFhd = false;
    var ajaxInitJhdSet = false;
    var ajaxInitShopLogo = false;

    function changeData(data){
        return {
            Data:data.data,
            IsError: (data.result!=100),
            Msg: (data.message||""),
        }
    }
    function checkState() {
        //TODO 暂时去掉 测试快递单
        if (ajaxInitExCompany && ajaxInitTemplates && ajaxGlobalSettingKdd && ajaxGlobalSettingFhd &&ajaxInitJhdSet&&ajaxInitShopLogo && (!dataObj.isInit)) {
       // if (ajaxInitExCompany && ajaxInitTemplates && ajaxGlobalSettingKdd && ajaxGlobalSettingFhd  && ajaxInitJhdSet && (!dataObj.isInit)) {
            dataObj.isInit = true;
            console.info("comp.print.data.init");
            dataObj.initCallBack();
        }
    }

    //扩展快递单打印类型
    function extendPrintModeType(params) {
        var result = $.extend({}, params, {
            modeType: comp.Print.Data.modeType || 0
        });
        return result;
    }
    //模块初始化方法
    dataObj.init = function (callback) {
        dataObj.initCallBack = callback;
        if (!ajaxInitExCompany) {
           dataObj.initExCompany(checkState);
        }

        if (!ajaxInitTemplates) {
           dataObj.initTemplates(checkState);
        }

        if (!ajaxGlobalSettingKdd) {
           dataObj.getKddGlobalSetting(comp.Print.Data.exuid, comp.Print.Data.exsubuid, checkState);
        }

        if (!ajaxGlobalSettingFhd) {
           dataObj.getFhdGlobalSetting(comp.Print.Data.exuid, comp.Print.Data.exsubuid, checkState);
        }

        if (!ajaxInitJhdSet) {
           dataObj.getJhdSet(checkState);
        }

        if (!ajaxInitShopLogo) {
           dataObj.getShopLogo(checkState);
        }

        // dataObj.initPrintData(function () {
        //     dataObj.isInit = true;
        //   //  console.info("comp.print.data.init success");
        //     dataObj.initCallBack();
        // });
    }

    //初始化数据
    // dataObj.initPrintData = function (callback) {
    //     var printData = comp.Print.Data;
    //     var parameter = {};
    //     parameter.action = "GetPrintInitData";
    //     parameter.exuserId = 8;
    //     parameter.subUserId = 0;
    //     $.ajax({
    //         type: "post",
    //         dataType: "json",
    //         url: "/ashx/ZuJian/printnew/HandlerTemplate.ashx",
    //         data: extendPrintModeType(parameter),
    //         success: function (data) {
    //             if (!data.IsError) {
    //                 var resObj = data.Data;
    //                 printData.exCompanys = resObj.ExCompanys;
    //                 printData.kddTemplates = resObj.KddTemplateList;
    //                 printData.fhdTemplateList = resObj.FhdTemplateList;
    //                 var eventObj = comp.Print.eventObj;
    //                 var list = eventObj.KddTemplateListChanged;
    //                 if (list) {
    //                     list.each(function (index, item) {
    //                         item(printData.kddTemplates);
    //                     });
    //                 }
    //                 printData.globalSettingKdd = resObj.KddGlobalSetting;
    //                 printData.systemFonts = printData.globalSettingKdd.SystemFonts;
    //                 printData.globalSettingFhd = resObj.FhdGlobalSetting;
    //                 printData.shopLogo = resObj.ShopLogo;
    //                 printData.jhdSet = resObj.JhdSet;
    //                 var defaultKddTemplateInfo = resObj.DefaultKddTemplateInfo;
    //                 var defaultFhdTemplateInfo = resObj.DefaultFhdTemplateInfo;
    //                 comp.Print.Data.fhdTempInfos.push(defaultFhdTemplateInfo);
    //                 comp.Print.Data.kddTempInfos.push(defaultKddTemplateInfo);
    //                 callback(data);
    //             }
    //             else {
    //                 console.error("comp.Print.data.initPrintData data msg:" + data.Msg);
    //             }
    //         },
    //         error: function (request, textStatus, errorThrown) {
    //             console.error("comp.Print.data.initPrintData is error！" + (textStatus || errorThrown));
    //         }
    //     });
    // }

    //保存发货单默认模版样式
    dataObj.saveFhdDefaultTemplate = function (modeListShowId, callback,errback) {
        var userid = comp.Print.Data.exuid;
        var printData = comp.Print.Data;
        var parameter = { action: "SaveFhdDefaultTemplate", exuserId: userid, modeListShowId: modeListShowId };
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeListshow/saveFhdDefaultTemplate",
            data: parameter,
            async: false,
            success: function (data) {
                var data = changeData(data);
                if (!data.IsError) {
                    for (var i = 0; i < printData.fhdTemplateList.ModeListShows.length; i++) {
                        var temObj = printData.fhdTemplateList.ModeListShows[i];
                        temObj.IsDef = 0;
                        if (temObj.Mode_ListShowId == modeListShowId) {
                            temObj.IsDef = 1;
                        }
                    }
                    printData.fhdTemplateList.ModeListShowId = modeListShowId;
                    callback(data);
                }
                else {
                    console.error("comp.Print.data.saveFhdDefaultTemplate data msg:" + data.Msg);
                    if ($.isFunction(errback)) {
                        errback(data.Msg);
                    }
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.print.data.saveFhdDefaultTemplate is error:" + (textStatus || errorThrown));
                if ($.isFunction(errback)) {
                    errback((textStatus || errorThrown));
                }
            }
        });
    }

    //获取发货单模版详细信息
    dataObj.getFhdTemplateInfo = function (modeListShowId, isAsync) {
        var arr = comp.Print.Data.fhdTempInfos;
        for (var i = 0; i < arr.length; i++) {
            var item = arr[i];
            if (item.ModeListShow.Mode_ListShowId == modeListShowId) {
                var json = JSON.stringify(item);
                return item;
            }
        }
        isAsync = !!isAsync;
        var ret = null;
        var userid = comp.Print.Data.exuid;
        var parameter = { action: "GetFhdTemplateInfo", exuserId: userid, modeListShowId: modeListShowId };
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeListshow/getFhdTemplateInfo",
            data: parameter,
            async: isAsync,
            success: function (data) {
                var data = changeData(data);
                if (!data.IsError) {
                    arr.push(data.Data);
                    ret = data.Data;
                }
                else {
                    console.error("comp.Print.data.getFhdTemplateInfo data msg:" + data.Msg);
                    alert("获取发货单模版详细信息出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.print.data.getFhdTemplateInfo is error:" + (textStatus || errorThrown));
                alert("获取发货单模版详细信息出错");
            }
        });
       
         return ret;
    }

    //获取模版详细信息
    dataObj.getTemplateInfo = function (tempid, isAsync) {
        var arr = comp.Print.Data.kddTempInfos;
        for (var i = 0; i < arr.length; i++) {
            var item = arr[i];
            if (item.ModeListShow.Mode_ListShowId == tempid) {
                return item;
            }
        }
        isAsync = !!isAsync;
        var ret = null;
        var userid = comp.Print.Data.exuid;
        var parameter = { action: "GetTemplateInfoByShowId", exuserId: userid, modeListShowId: tempid };
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeListshow/getTemplateInfoByShowId",
            data: parameter,
            async: isAsync,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    arr.push(data.Data);
                    ret = data.Data;
                }
                else {
                    console.error("comp.Print.data.getTemplateInfo data msg:" + data.Msg);
                    alert("获取模版详细信息出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.print.data.getTemplateInfo is error:" + (textStatus || errorThrown));
                alert("获取模版详细信息出错");
            }
        });
        return ret;
    }

    //获取发货单模版列表
    dataObj.getFhdTemplateList = function (isAsync) {
        var printData = comp.Print.Data;
        if (printData.fhdTemplateList) {
           return printData.fhdTemplateList;
        }
        isAsync = !!isAsync;
        var ret = null;
        var parameter = {};
        parameter.action = "GetFhdTemplateList";
        parameter.exUserId = printData.exuid;
        parameter.subUserId = printData.exsubuid;
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeListshow/getFhdTemplateList",
            data: parameter,
            async: false,
            success: function (data) {
                var data = changeData(data);
                if (!data.IsError) {
                    printData.fhdTemplateList = data.Data;
                    ret = data.Data;
                }
                else {
                    console.error("comp.Print.data.getFhdTemplateList data msg" + data.Msg);
                    alert("获取发货单模版列表出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.getFhdTemplateList is error！" + (textStatus || errorThrown));
                alert("获取发货单模版列表出错");
            }
        });
        return ret;
    }

    //恢复默认布局
    dataObj.recoveryDefaultTemplate = function (exid, ptype) {
        if (ptype == "kdd") {
            return dataObj.recoveryDefaultKddTemplate(exid);
        } else {
            return dataObj.recoveryDefaultFhdTemplate(exid);
        }
    }

    //获取快递模版默认布局
    dataObj.recoveryDefaultKddTemplate = function (exid) {
        var ret = null;
        var printData = comp.Print.Data;
        if (printData.defaultLayoutTemplate && printData.defaultLayoutTemplate.length > 0) {
            var len = printData.defaultLayoutTemplate.length;
            for (var i = 0; i < len; i++) {
                var temObj = printData.defaultLayoutTemplate[i];
                if (temObj.exid == exid) {
                    return Object.Copy(temObj.data);
                }
            }
        }
        var parameter = {};
        parameter.action = "RecoveryDefaultTemplate";
        parameter.exid = exid; 
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeListshow/recoveryDefaultTemplate",
            data: parameter,
            async: false,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    ret = Object.Copy(data.Data);
                    var temObj = {};
                    temObj.exid = exid;
                    temObj.data = data.Data;
                    printData.defaultLayoutTemplate.push(temObj);
                }
                else {
                    console.error("comp.Print.data.recoveryDefaultKddTemplate data msg" + data.Msg);
                    alert("获取快递模版默认布局出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.recoveryDefaultKddTemplate is error！" + (textStatus || errorThrown));
                alert("获取快递模版默认布局出错");
            }
        });
        return ret;
    }

    //获取发货单模版默认布局
    dataObj.recoveryDefaultFhdTemplate = function (exid) {
        var ret = null;
        var printData = comp.Print.Data;
        if (printData.defaultLayoutFhdTemplate && printData.defaultLayoutFhdTemplate.length > 0) {
            var len = printData.defaultLayoutTemplate.length;
            for (var i = 0; i < len; i++) {
                var temObj = printData.defaultLayoutFhdTemplate[i];
                if (temObj.exid == exid) {
                    return Object.Copy(temObj.data);
                }
            }
        }
        var parameter = {};
        parameter.action = "RecoveryDefaultFhdTemplate";
        parameter.exid = exid;
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeListshow/recoveryDefaultFhdTemplate",
            data: parameter,
            async: false,
            success: function (data) {
                var data = changeData(data);
                if (!data.IsError) {
                    ret = Object.Copy(data.Data);
                    var temObj = {};
                    temObj.exid = exid;
                    temObj.data = data.Data;
                    printData.defaultLayoutTemplate.push(temObj);
                }
                else {
                    console.error("comp.Print.data.recoveryDefaultFhdTemplate data msg" + data.Msg);
                    alert("获取发货单模版默认布局出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.recoveryDefaultFhdTemplate is error！" + (textStatus || errorThrown));
                alert("获取发货单模版默认布局出错");
            }
        });
        return ret;
    }

    var initTypeObject = null;

    //初始化某一个元素中带有initType的标记
    dataObj.initTypeDom = function (dom) {
        if (!initTypeObject) {
            var that = new comp.Print.FN();
            initTypeObject = {};
            var fontfamilyHtml = "<option value=''>默认</option>";
            var fonts = that.getSystemFonts();
            $.each(fonts, function (ind, inv) {
                fontfamilyHtml += "<option value='" + inv + "'>" + inv + "</option>";
            });
            initTypeObject.fontfamily = function (dom) {
                dom.html(fontfamilyHtml);
            }

            var fontnumHtml = "<option value='0'>默认</option>";
            for (var i = 2; i <= 96; i++) {
                fontnumHtml += "<option value='" + i + "'>" + i + "</option>";
            }
            initTypeObject.fontnum = function (dom) {
                dom.html(fontnumHtml);
            }

            var fontboldHtml = '<option value="-1">默认</option> <option value="1">加粗</option><option value="0">不加粗</option>';
            initTypeObject.fontbold = function (dom) {
                dom.html(fontboldHtml);
            }

            var textalignHtml = '<option value="-1">默认</option> <option value="1">左</option><option value="2">中</option><option value="3">右</option>';
            initTypeObject.textalign = function (dom) {
                dom.html(textalignHtml);
            }

            var linestyleHtml = '<option value="0">实线</option><option value="2">虚线</option>';
            initTypeObject.linestyle = function (dom) {
                dom.html(linestyleHtml);
            }

            var lineboldHtml = '<option value="1">不加粗</option><option value="2">加粗</option>';
            initTypeObject.linebold = function (dom) {
                dom.html(lineboldHtml);
            }

            var fontspaceHtml = "<option value='-1'>默认</option>";
            var linespaceHtml = "<option value='-1'>默认</option>";

            for (var i = 1; i <= 10; i++) {
                fontspaceHtml += "<option value='" + i + "'>" + i + "</option>";
                linespaceHtml += "<option value='" + i + "'>" + i + "</option>";
            }

            initTypeObject.fontspace = function (dom) {
                dom.html(fontspaceHtml);
            }

            initTypeObject.linespace = function (dom) {
                dom.html(linespaceHtml);
            }

            var printdirectionHtml = '<option value="0">默认</option> <option value="1">纵向</option><option value="2">横向</option>';
            initTypeObject.printdirection = function (dom) {
                dom.html(printdirectionHtml);
            }

            var linedirectionHtml = '<option value="1">竖</option><option value="2">横</option>';
            initTypeObject.linedirection = function (dom) {
                dom.html(linedirectionHtml);
            }
        }

        var doms = dom.find("select[inittype]");
        doms.each(function (ind, inv) {
            var tarDom = $(inv);
            var attrName = tarDom.attr("inittype").toLowerCase();
            var fun = initTypeObject[attrName];
            if (fun) {
                fun(tarDom);
            }
        });
    }

    //获取系统字体
    dataObj.getSystemFonts = function () {
        var printData = comp.Print.Data;
        if (printData.systemFonts && printData.systemFonts.length > 0) {
            return printData.systemFonts;
        }
        var parameter = {};
        parameter.action = "GetSystemFonts";
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeSet/getSystemFonts",
            data: parameter,
            async: false,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    printData.systemFonts = data.Data;
                }
                else {
                    console.error("comp.Print.data.getSystemFonts data msg" + data.Msg);
                    alert("获取系统字体出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.getSystemFonts is error！" + (textStatus || errorThrown));
                alert("获取系统字体出错");
            }
        });
        return printData.systemFonts;
    }

    //获取用户店标
    dataObj.getShopLogo = function (callback) {
        var printData = comp.Print.Data;
        var parameter = {};
        parameter.action = "GetShopLogo";
        parameter.exuserId = printData.exuid;
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeLogo/getShopLogo",
            data: parameter,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    printData.shopLogo = data.Data;
                    ajaxInitShopLogo = true;
                    if (typeof callback == "function") {
                        callback();
                    }
                }
                else {
                    console.error("comp.Print.data.getShopLogo data msg" + data.Msg);
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.getShopLogo is error！" + (textStatus || errorThrown));
            }
        });
    }

    //初始化当前登录用户的快递模版
    dataObj.initTemplates = function (callback) {
        var printData = comp.Print.Data;
        var parameter = {};
        parameter.action = "GetTemplateList";
        parameter.exuserId = printData.exuid;
        parameter.subUserId = printData.exsubuid;
        parameter.modeId = "kdd";
        parameter.pageIndex = 0;
        parameter.pageSize = 0;
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeListshow/getTemplateList",
            data: parameter,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    printData.kddTemplates = data.Data;
                    ajaxInitTemplates = true;
                    if (typeof callback == "function") {
                        callback();
                    }
                    var eventObj = comp.Print.eventObj;
                    var list = eventObj.KddTemplateListChanged;
                    if (list) {
                        list.each(function (index,item) {
                            item(data.Data);
                        });
                    }
                }
                else {
                    console.error("comp.Print.data.initTemplates data msg" + data.Msg);
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.initTemplates is error！" + (textStatus || errorThrown));
            }
        });
    }

    //保存拣货单设置
    dataObj.updateJhdSet = function (jhdSet, callback,errorcallback) {
        var printData = comp.Print.Data;
        var parameter = {};
        parameter["action"] = "updatejhdset";
        var printData = comp.Print.Data;
        parameter["ptype"] = printData.ptype;
        parameter["exuid"] = printData.exuid;
        parameter["uk"] = printData.uk;
        parameter["jhdset"] = JSON.stringify(jhdSet);
        $.ajax({
            type: "post",
            dataType: "json", //返回json格式的数据
            url: "/jhdSet/updateJhdSet",
            data: parameter,
            success: function (data) {
                var data = changeData(data);
                if (!data.IsError) {
                    comp.Print.Data.jhdSet = jhdSet;
                    callback(jhdSet);
                }
                else {
                    console.error("comp.Print.data.updateJhdSet data msg" + data.mess);
                    errorcallback(data.mess);
                }
            },
            error: function (request, textStatus, errorThrown) {
                var mess = (textStatus || errorThrown);
                errorcallback(mess);
                console.error("comp.Print.data.updateJhdSet is error！" + (textStatus || errorThrown));
            }
        });
    }

    //获取拣货单设置
    dataObj.getJhdSet = function (callback) {
        var parameter = {};
        parameter["action"] = "getjhdset";
        var printData = comp.Print.Data;
        parameter["ptype"] = printData.ptype;
        parameter["exuid"] = printData.exuid;
        parameter["uk"] = printData.uk;
        $.ajax({
            type: "post",
            dataType: "json", //返回json格式的数据
            url: "/jhdSet/getJhdSet",
            data: parameter,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    printData.jhdSet = data.Data;
                    ajaxInitJhdSet = true;
                    if ($.isFunction(callback)) {
                        callback(data.Data);
                    }
                }
                else {
                    console.error("comp.Print.data.getJhdSet data msg" + data.Msg);
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.initTemplates is error！" + (textStatus || errorThrown));
            }
        });
    }

    //保存排序和删除的快递模版
    dataObj.saveSortKdd = function (obj, callback,errorback) {
        var parameter = {};
        parameter.action = "SaveSrotAndDelete";
        parameter.jsonParam = JSON.stringify(obj);
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeListshow/saveSortAndDelete",
            data: parameter,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    dataObj.initTemplates(function () {
                        callback(data.Data);
                    });
                }
                else {
                    console.error("comp.Print.data.saveSortKdd data msg" + data.Msg);
                    if ($.isFunction(errorback)) {
                        errorback(data.Msg);
                    }
                }
            },
            error: function (request, textStatus, errorThrown) {
                var msg = (textStatus || errorThrown);
                console.error("comp.Print.data.saveSortKdd is error！" + msg);
                if ($.isFunction(errorback)) {
                    errorback(msg);
                }
            }
        });
    }

    //获取快递单全局设置
    dataObj.getKddGlobalSetting = function (exuserId, subUserId, callback) {
        var parameter = {};
        parameter.exuserId = exuserId;
        parameter.subUserId = subUserId;
        parameter.modeid = "kdd";
        parameter.action = "GetKddGlobalSetting";
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeSet/getKddGlobalSetting",
            data: parameter,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    if (!ajaxGlobalSettingKdd) {
                        ajaxGlobalSettingKdd = true;
                    }
                    comp.Print.Data.globalSettingKdd = data.Data;
                    callback(data.Data);
                }
                else {
                    console.error("comp.Print.data.getKddGlobalSetting data msg" + data.Msg);
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.getKddGlobalSetting is error！" + (textStatus || errorThrown));
            }
        });
    }

    //获取发货单全局设置
    dataObj.getFhdGlobalSetting = function (exuserId, subUserId, callback) {
        var parameter = {};
        parameter.exuserId = exuserId;
        parameter.subUserId = subUserId;
        parameter.modeid = "fhd";
        parameter.action = "GetFhdGlobalSetting";
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeSet/getFhdGlobalSetting",
            data: parameter,
            success: function (data) {
                var data = changeData(data);
                if (!data.IsError) {
                    if (!ajaxGlobalSettingFhd) {
                        ajaxGlobalSettingFhd = true;
                    }
                    comp.Print.Data.globalSettingFhd = data.Data;
                    callback(data.Data);
                }
                else {
                    console.error("comp.Print.data.getKddGlobalSetting data msg" + data.Msg);
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.getKddGlobalSetting is error！" + (textStatus || errorThrown));
            }
        });
    }

    //保存快递单全局设置
    dataObj.saveKddGlobalSetting = function (obj, callback) {
        var printData = comp.Print.Data;
        var parameter = {};
        parameter.jsonParam = JSON.stringify(obj);
        parameter.action = "SaveKddGlobalSetting";
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeSet/saveKddGlobalSetting",
            data: parameter,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    dataObj.getKddGlobalSetting(printData.exuid, printData.exsubuid, function () {
                        dataObj.initTemplates(function () {
                            callback(data.Data);
                        });
                    });
                }
                else {
                    console.error("comp.Print.data.saveKddGlobalSetting data msg" + data.Msg);
                    alert("保存快递单全局设置出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.saveKddGlobalSetting is error！" + (textStatus || errorThrown));
                alert("保存快递单全局设置出错");
            }
        });
    }

    //保存发货单全局设置
    dataObj.saveFhdGlobalSetting = function (obj, callback) {
        var printData = comp.Print.Data;
        var parameter = {};
        parameter.jsonParam = JSON.stringify(obj);
        parameter.action = "SaveFhdGlobalSetting";
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeSet/saveFhdGlobalSetting",
            data: parameter,
            success: function (data) {
                var data = changeData(data)
                if (!data.IsError) {
                    comp.Print.Data.globalSettingFhd = obj;
                    callback(data.Data);
                }
                else {
                    console.error("comp.Print.data.saveKddGlobalSetting data msg" + data.Msg);
                    alert("保存发货单全局设置出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.saveKddGlobalSetting is error！" + (textStatus || errorThrown));
                alert("保存发货单全局设置出错");
            }
        });
    }

    //保存新增的快递模版
    dataObj.saveAddKdd = function (obj, callback) {
        var parameter = {};
        parameter.action = "AddTemplate";
        parameter.jsonParam = JSON.stringify(obj);
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeListshow/addTemplate",
            data: parameter,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    dataObj.initTemplates(function () { callback(data.Data); });
                }
                else {
                    console.error("comp.Print.data.saveAddKdd data msg" + data.Msg);
                    alert("新增模版失败");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.saveAddKdd is error！" + (textStatus || errorThrown));
                alert("新增模版失败");
            }
        });
    }

    //新增模版时上传图片 
    dataObj.uploadImgAddKdd = function (btnId, fileName, beforeFun, successFun) {
        $("#" + btnId).fileUpload("/modeListimg/uploadCustomBgImg", fileName, function (data) {
            data.action = "UploadCustomBgImg";
            beforeFun(data);
        }, function (data) {
            var data = changeData(data);
            if (data.IsError) {
                alert("模版上传图片失败");
                console.error("comp.Print.data.uploadImgAddKdd is error :" + data.Msg);
                return;
            }
            successFun(data.Data);
        }, function (err) {
            alert("模版上传图片失败");
            console.error("comp.Print.data.uploadImgAddKdd is error :" + err);
        })
    }
    
    //上传店标 
    dataObj.uploadShopLogo = function (btnId, fileName, beforeFun, successFun) {
        $("#" + btnId).fileUpload("/modeLogo/uploadShopLogo", fileName, function (data) {
            data.action = "UploadShopLogo";
            beforeFun(data);
        }, function (data) {
            var data = changeData(data);
            if (data.IsError) {
                alert("上传店标失败");
                console.error("comp.Print.data.uploadShopLogo is error :" + data.Msg);
                return;
            }
            successFun(data.Data);
        }, function (err) {
            alert("上传店标失败");
            console.error("comp.Print.data.uploadShopLogo is error :" + err);
        })
    }

    //上传图片 
    dataObj.uploadCustomImg = function (btnId, fileName, beforeFun, successFun) {
        $("#" + btnId).fileUpload("/modeListimg/uploadCustomImg", fileName, function (data) {
            data.action = "UploadCustomImg";
            beforeFun(data);
        }, function (data) {
            var data = changeData(data);
            if (data.IsError) {
                alert("模版中图片上传失败");
                console.error("comp.Print.data.uploadShopLogo is error :" + data.Msg);
                return;
            }
            successFun(data.Data);
        }, function (err) {
            alert("模版中图片上传失败");
            console.error("comp.Print.data.uploadShopLogo is error :" + err);
        })
    }

    //保存模版底图
    dataObj.saveTemplateBgImg = function (modeListShowId, bgImgId, imgSrc, callback) {
        var parameter = {};
        parameter.action = "SaveTemplateBgImg";
        parameter.modeListShowId = modeListShowId;
        parameter.bgImgId = bgImgId;
        parameter.imgSrc = imgSrc;
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeListshow/saveTemplateBgImg",
            data: parameter,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    callback(data.Data);
                }
                else {
                    console.error("comp.Print.data.saveTemplateBgImg data msg" + data.Msg);
                    alert("保存模版底图失败");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.saveTemplateBgImg is error！" + (textStatus || errorThrown));
                alert("保存模版底图失败");
            }
        });
    }

    //删除上传的图片    
    dataObj.delAddKddImg = function (bgImgId, callback) {
        var parameter = {};
        parameter.action = "DeleteCustomBgImg";
        parameter.bgImgId = bgImgId;
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeListimg/deleteCustomBgImg",
            data: parameter,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    callback(data.Data);
                }
                else {
                    console.error("comp.Print.data.delAddKddImg data msg" + data.Msg);
                    alert("删除上传的图片失败");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.delAddKddImg is error！" + (textStatus || errorThrown));
                alert("删除上传的图片失败");
            }
        });
    }

    //初始化快递
    dataObj.initExCompany = function (callback) {
        var printData = comp.Print.Data;
        // var parameter = { action: "InitExCompany" };
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/exCompany/initExCompany",
            data: {},
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    printData.exCompanys = data.Data;
                    ajaxInitExCompany = true;
                    callback();
                }
                else {
                    console.error("comp.Print.data.initexcompany data msg:" + data.Msg);
                    alert("初始化快递失败");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.print.data.initexcompany is error:" + (textStatus || errorThrown));
            }
        });
    }


    //设置模版默认打印机 
    dataObj.saveDefaultPrinter = function (mkddid, configId, printerName, callback) {
        var parameter = { action: "SaveDefaultPrinter", configId: configId, printerName: printerName };
        parameter.modeListShowId = mkddid;
        parameter.exuserId = comp.Print.Data.exuid;
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeTempPrintCfg/saveDefaultPrinter",
            data: parameter,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    callback(data.Data);
                }
                else {
                    console.error("comp.Print.data.saveDefaultPrinter data msg:" + data.Msg);
                    alert("设置模版默认打印机出错!");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.print.data.saveDefaultPrinter is error:" + (textStatus || errorThrown));
                alert("设置模版默认打印机出错!");
            }
        });
    }

    //获取面单底图
    //var parameter = { action: "GetKddBgImgBy", companyId: companyId, kddtype: kddtype, styleid: styleId, exuserid: exuserid };
    dataObj.getKddBgImgBy = function (companyId, kddtype, styleId, isDefault, callback) {
        var parameter = { action: "GetKddBgImgBy", companyId: companyId, kddtype: kddtype, styleid: styleId};
        parameter.isDefault = isDefault;
        parameter.exuserid = comp.Print.Data.exuid;
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeListimg/getKddBgImgBy",
            data: parameter,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    callback(data.Data);
                }
                else {
                    console.error("comp.Print.data.getKddBgImgBy is error" + data.Msg);
                    alert("获取面单底图出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.getKddBgImgBy is error！" + (textStatus || errorThrown));
                alert("获取面单底图出错");
            }
        });
    }

    //模版右侧标签
    dataObj.getTemplateDefaultItem = function (isAsync) {
        var parameter = { action: "GetTemplateDefaultItem" };
        if (comp.Print.Data.templateDefaultItem && comp.Print.Data.templateDefaultItem.length > 0) {
            return comp.Print.Data.templateDefaultItem;
        }
        isAsync = !!isAsync;
        var ret = null;
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeTemplateTag/getTemplateDefaultItem",
            data: parameter,
            async: isAsync,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    ret = data.Data;
                    comp.Print.Data.templateDefaultItem = ret;
                }
                else {
                    console.error("comp.Print.data.getKddBgImgBy is error" + data.Msg);
                    alert("获取模版右侧标签出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.getKddBgImgBy is error！" + (textStatus || errorThrown));
                alert("获取模版右侧标签出错");
            }
        });
        return ret;
    }

    //获取面单类型
    dataObj.getKddTypeByExCode = function (companyId, callback) {
        var parameter = { action: "GetKddTypeByExCode", companyId: companyId };
        parameter.exuserId = comp.Print.Data.exuid;
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeList/getKddTypeByExCode",
            data: parameter,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    callback(data.Data);
                }
                else {
                    console.error("comp.Print.data.getKddTypeByExCode is error" + data.Msg);
                    alert("获取面单类型出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.getKddTypeByExCode is error！" + (textStatus || errorThrown));
                alert("获取面单类型出错");
            }
        });
    }

    //获取业务类型
    dataObj.getKddWorkType = function (excode, callback) {
        var parameter = { action: "GetKddWorkType", excode: excode };
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeList/getKddServiceType",
            data: parameter,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    callback(data.Data);
                }
                else {
                    console.error("comp.Print.data.getKddWorkType is error" + data.Msg);
                    alert("获取业务类型出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.getKddWorkType is error！" + (textStatus || errorThrown));
                alert("获取业务类型出错");
            }
        });
    }

    //获取服务类型
    dataObj.getKddServiceType = function (excode, callback) {
        var parameter = { action: "GetKddServiceType", excode: excode };
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeList/getKddServiceItem",
            data: parameter,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    callback(data.Data);
                }
                else {
                    console.error("comp.Print.data.getKddServiceType is error" + data.Msg);
                    alert("获取服务类型出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.getKddServiceType is error！" + (textStatus || errorThrown));
                alert("获取服务类型出错");
            }
        });
    }

    //获取增值服务类型
    dataObj.getExAdvancedServices = function (excode, callback) {
        var parameter = { action: "GetExAdvancedServices", excode: excode };
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeList/getExAdvancedServices",
            data: parameter,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    callback(data.Data);
                }
                else {
                    console.error("comp.Print.data.getExAdvancedServices is error" + data.Msg);
                    alert("获取增值服务类型出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.getExAdvancedServices is error！" + (textStatus || errorThrown));
                alert("获取增值服务类型出错");
            }
        });
    }

    //获取面单样式
    dataObj.getKddStyle = function (companyId, kddtype, height, callback) {
        var parameter = { action: "GetKddStyle", companyId: companyId, kddtype: kddtype, height: height };
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeKddStyle/getKddStyle",
            data: parameter,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    callback(data.Data);
                } else {
                    console.error("comp.Print.data.getKddStyle is error" + data.Msg);
                    alert("获取面单样式出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.getKddStyle is error！" + (textStatus || errorThrown));
                alert("获取面单样式出错");
            }
        });
    };

    //获取面单尺寸
    dataObj.getKddSize = function (companyId, kddtype, callback) {
        var parameter = { action: "GetKddSize", companyId: companyId, kddtype: kddtype };
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeList/getKddSize",
            data: parameter,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    callback(data.Data);
                } else {
                    console.error("comp.Print.data.getKddSize is error" + data.Msg);
                    alert("获取面单尺寸出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.getKddSize is error！" + (textStatus || errorThrown));
                alert("获取面单尺寸出错");
            }
        });
    };

    //获取云栈设置信息 东方：2016.05.18
    // dataObj.getYunZhanSetting = function (exuserId, subUserId, excode, modeListShowId, exid, callback) {
    //     var printData = comp.Print.Data;
    //     var parameter = { modeListShowId: modeListShowId, excode: excode };
    //     $.ajax({
    //         type: "get",
    //         dataType: "json",
    //         url: "/dzmdYzDefaultAddress/getYunZhanSetting",
    //         data: parameter,
    //         success: function (data) {
    //             printData.yunzhanSetting = data.Data;
    //             if ($.isFunction(callback)) {
    //                 callback(data, exuserId, subUserId, modeListShowId, exid);
    //             }
    //             //if (!data.IsError) {
    //             //    callback(data.Data);
    //             //} else {
    //             //    console.error("comp.Print.data.getYunZhanSetting is error" + data.Msg);
    //             //    alert("获取云栈设置信息出错");
    //             //}
    //         },
    //         error: function (request, textStatus, errorThrown) {
    //             console.error("comp.Print.data.getYunZhanSetting is error！" + (textStatus || errorThrown));
    //             //alert("获取云栈设置信息出错");
    //         }
    //     });
    // };

    // //获取用户的云栈设置信息 东方：2016.05.18
    // dataObj.getYunZhanUserSetting = function (exuserId, subUserId, modeListShowId, exid, callback) {
    //     var printData = comp.Print.Data;
    //     var parameter = { modeListShowId: modeListShowId, exId: exid };
    //     $.ajax({
    //         type: "get",
    //         dataType: "json",
    //         url: "/dzmdYzDefaultAddress/getYunZhanUserSetting",
    //         data: parameter,
    //         success: function (data) {
    //             if (!data.IsError) {
    //                 printData.yunzhanUserSetting = data.Data;
    //                 if ($.isFunction(callback)) {
    //                     callback();
    //                 }
    //             } else {
    //                 console.error("comp.Print.data.getYunZhanUserSetting is error" + data.Msg);
    //                 alert("获取云栈设置信息出错");
    //             }
    //         },
    //         error: function (request, textStatus, errorThrown) {
    //             console.error("comp.Print.data.getYunZhanUserSetting is error！" + (textStatus || errorThrown));
    //             alert("获取云栈设置信息出错");
    //         }
    //     });
    // };

    // //保存云栈网点店铺代码信息
    // dataObj.saveYunZhanShopCode = function (exuserId, modeListShowId, shopCode, callback) {
    //     var parameter = { modeListShowId: modeListShowId, shopCode: shopCode };
    //     $.ajax({
    //         type: "post",
    //         dataType: "json",
    //         url: "/dzmdYzLinkShop/saveYunZhanShopCode",
    //         data: parameter,
    //         success: function (data) {
    //             if (!data.IsError) {
    //                 if (data.Code == "200") {
    //                     if ($.isFunction(callback)) {
    //                         callback(data);
    //                     }
    //                 } else {
    //                     alert("请输入正确的非本店铺代码");
    //                     return;
    //                 }
    //             } else {
    //                 console.error("comp.Print.data.saveYunZhanShopCode is error" + data.Msg);
    //                 alert("保存云栈网点店铺代码信息出错,请刷新重试");
    //                 callback(data);
    //             }
    //         },
    //         error: function (request, textStatus, errorThrown) {
    //             console.error("comp.Print.data.saveYunZhanShopCode is error！" + (textStatus || errorThrown));
    //             alert("保存云栈网点店铺代码信息出错,请刷新重试");
    //         }
    //     });
    // };

    // //删除云栈店铺代码
    // dataObj.deleteYunZhanShopCode = function (exuserId, modeListShowId, callback) {
    //     var parameter = { exId: exuserId, modeListShowId: modeListShowId };
    //     $.ajax({
    //         type: "post",
    //         dataType: "json",
    //         url: "/dzmdYzLinkShop/deleteYunZhanShopCode",
    //         data: parameter,
    //         success: function (data) {
    //             if (!data.IsError) {
    //                 if (data.Code == "200") {
    //                     if ($.isFunction(callback)) {
    //                         callback(data);
    //                     }
    //                 }
    //             } else {
    //                 console.error("comp.Print.data.deleteYunZhanShopCode is error" + data.Msg);
    //                 alert("删除云栈网点店铺代码信息出错,请刷新重试");
    //                 callback(data);
    //             }
    //         },
    //         error: function (request, textStatus, errorThrown) {
    //             console.error("comp.Print.data.deleteYunZhanShopCode is error！" + (textStatus || errorThrown));
    //             alert("删除云栈网点店铺代码信息出错,请刷新重试");
    //         }
    //     });
    // };

    // //删除用户的云栈设置信息 东方：2016.05.18
    // dataObj.deleteYunZhanSetting = function (exuserId, subUserId, modeListShowId, exid, callback) {
    //     var parameter = { modeListShowId: modeListShowId, exId: exid };
    //     $.ajax({
    //         type: "post",
    //         dataType: "json",
    //         url: "/dzmdYzDefaultAddress/deleteYunZhanSetting",
    //         data: parameter,
    //         success: function (data) {
    //             if (!data.IsError) {
    //                 if ($.isFunction(callback)) {
    //                     callback(data);
    //                 }
    //             } else {
    //                 console.error("comp.Print.data.deleteYunZhanSetting is error" + data.Msg);
    //                 alert("删除用户的云栈设置信息出错,请刷新重试");
    //                 callback(data);
    //             }
    //         },
    //         error: function (request, textStatus, errorThrown) {
    //             console.error("comp.Print.data.deleteYunZhanSetting is error！" + (textStatus || errorThrown));
    //             alert("删除用户的云栈设置信息出错,请刷新重试");
    //         }
    //     });
    // };

    // //保存用户的云栈设置信息 东方：2016.05.18
    // dataObj.saveYunZhanSetting = function (obj, callback) {
    //     var printData = comp.Print.Data;
    //     var parameter = { dzmdYzDefaultAddressJSON: JSON.stringify(obj) };
    //     $.ajax({
    //         type: "post",
    //         dataType: "json",
    //         url: "/dzmdYzDefaultAddress/saveYunZhanSetting",
    //         data: parameter,
    //         success: function (data) {
    //             if (!data.IsError) {
    //                 printData.yunzhanUserSetting = data.Data;
    //                 if ($.isFunction(callback)) {
    //                     callback(data);
    //                 }
    //             } else {
    //                 console.error("comp.Print.data.saveYunZhanSetting is error" + data.Msg);
    //                 alert("保存用户的云栈设置信息出错,请刷新重试");
    //                 callback(data);
    //             }
    //         },
    //         error: function (request, textStatus, errorThrown) {
    //             console.error("comp.Print.data.saveYunZhanSetting is error！" + (textStatus || errorThrown));
    //             alert("保存用户的云栈设置信息出错,请刷新重试");
    //         }
    //     });
    // };

    //---mm-modify----//
    //获取云栈设置信息 东方：2016.05.18 mm-modify
    dataObj.getYunZhanSetting = function (exuserId, subUserId, excode, modeListShowId, exid, callback) {
        var printData = comp.Print.Data;
        var parameter = { modeListShowId: modeListShowId, excode: excode };
        $.ajax({
            type: "get",
            dataType: "json",
            url: "/dzmdYzDefaultAddress/getYunZhanSetting",
            data: parameter,
            success: function (data) {
                // var changeData = {
                //     IsError : (data.result != 100),
                //     Data:data.data
                // };
                var data = changeData(data);
                printData.yunzhanSetting  = data.Data;
                // printData.yunzhanSetting = data.Data;
                if ($.isFunction(callback)) {
                    callback(data, exuserId, subUserId, modeListShowId, exid);
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.getYunZhanSetting is error！" + (textStatus || errorThrown));
                //alert("获取云栈设置信息出错");
            }
        });
    };

    //获取用户的云栈设置信息 东方：2016.05.18 mm-modify
    dataObj.getYunZhanUserSetting = function (exuserId, subUserId, modeListShowId, exid, callback) {
        var printData = comp.Print.Data;
        var parameter = {modeListShowId: modeListShowId, exId: exid };
        $.ajax({
            type: "get",
            dataType: "json",
            url: "/dzmdYzDefaultAddress/getYunZhanUserSetting",
            data: parameter,
            success: function (data) {
                if (data.result == 100) {
                    printData.yunzhanUserSetting = data.data;
                    if ($.isFunction(callback)) {
                        callback();
                    }
                } else {
                    console.error("comp.Print.data.getYunZhanUserSetting is error" + data.message);
                    alert("获取云栈设置信息出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.getYunZhanUserSetting is error！" + (textStatus || errorThrown));
                alert("获取云栈设置信息出错");
            }
        });
    };

    //保存云栈网点店铺代码信息  mm-modify
    dataObj.saveYunZhanShopCode = function (exuserId, modeListShowId, shopCode, callback) {
        var parameter = {  modeListShowId: modeListShowId, linkCode: shopCode };
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/dzmdYzLinkShop/saveYunZhanShopCode",
            data: parameter,
            success: function (data) {
                if (data.result == 100 ) {
                    if ($.isFunction(callback)) {
                        callback(data);
                    }
                } else {
                    console.error("comp.Print.data.saveYunZhanShopCode is error" + data.message);
                    alert("保存云栈网点店铺代码信息出错,请刷新重试");
                    callback(data);
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.saveYunZhanShopCode is error！" + (textStatus || errorThrown));
                alert("保存云栈网点店铺代码信息出错,请刷新重试");
            }
        });
    };

    //删除云栈店铺代码      mm-modify
    dataObj.deleteYunZhanShopCode = function (exuserId, modeListShowId, callback) {
        var parameter = { modeListShowId: modeListShowId };
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/dzmdYzLinkShop/deleteYunZhanShopCode",
            data: parameter,
            success: function (data) {
                if (data.result==100) {
                    if ($.isFunction(callback)) {
                        callback(data);
                    }
                } else {
                    console.error("comp.Print.data.deleteYunZhanShopCode is error" + data.Msg);
                    alert("删除云栈网点店铺代码信息出错,请刷新重试");
                    callback(data);
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.deleteYunZhanShopCode is error！" + (textStatus || errorThrown));
                alert("删除云栈网点店铺代码信息出错,请刷新重试");
            }
        });
    };

    //删除用户的云栈设置信息 东方：2016.05.18   mm-modify
    dataObj.deleteYunZhanSetting = function (exuserId, subUserId, modeListShowId, exid, callback) {
        var parameter = { modeListShowId: modeListShowId, exId: exid };
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/dzmdYzDefaultAddress/deleteYunZhanSetting",
            data: parameter,
            success: function (data) {
                var ndata = changeData(data);
                if (!ndata.IsError) {
                    if ($.isFunction(callback)) {
                        callback(ndata);
                    }
                } else {
                    console.error("comp.Print.data.deleteYunZhanSetting is error" + ndata.Msg);
                    alert("删除用户的云栈设置信息出错,请刷新重试");
                    callback(ndata);
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.deleteYunZhanSetting is error！" + (textStatus || errorThrown));
                alert("删除用户的云栈设置信息出错,请刷新重试");
            }
        });
    };

    //保存用户的云栈设置信息 东方：2016.05.18  mm-modify
    dataObj.saveYunZhanSetting = function (obj, callback) {
        var printData = comp.Print.Data;
        var parameter = { jsonParam: JSON.stringify(obj) };
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/dzmdYzDefaultAddress/saveYunZhanSetting",
            data: parameter,
            success: function (data) {
                if (data.result==100) {
                    printData.yunzhanUserSetting = obj;
                    if ($.isFunction(callback)) {
                        callback(data);
                    }
                } else {
                    console.error("comp.Print.data.saveYunZhanSetting is error" + data.message);
                    alert("保存用户的云栈设置信息出错,请刷新重试");
                    callback(data);
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.saveYunZhanSetting is error！" + (textStatus || errorThrown));
                alert("保存用户的云栈设置信息出错,请刷新重试");
            }
        });
    };
    //---mm-modify----//



    //获取网点电子面单的设置信息 东方：2016.05.18
    dataObj.getBranchSetting = function (exid, exuserId, subUserId, modeListShowId, exCode, callback) {
        var printData = comp.Print.Data;
        var parameter = { action: "GetBranchSetting", exid: exid, exuserId: exuserId, subUserId: subUserId, modeListShowId: modeListShowId };
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/ashx/ZuJian/printnew/HandlerTemplate.ashx",
            data: parameter,
            success: function (data) {
                if (!data.IsError) {
                    printData.netPointSetting = data.Data;
                    if ($.isFunction(callback)) {
                        callback(data, exCode);
                    }
                } else {
                    console.error("comp.Print.data.getBranchSetting is error" + data.Msg);
                    if (data.Msg.indexOf('Invalid session') > -1) {
                        alert("获取电子面单设置信息出错，授权已过期");
                    } else {
                        alert("获取电子面单设置信息出错");
                    }
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.getBranchSetting is error！" + (textStatus || errorThrown));
                alert("获取网点电子面单的设置信息出错");
            }
        });
    };

    //保存网点电子面单的设置信息 东方：2016.05.20
    dataObj.saveBranchSetting = function (obj, callback) {
        var printData = comp.Print.Data;
        var parameter = { action: "SaveBranchSetting", jsonParam: JSON.stringify(obj) };
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/ashx/ZuJian/printnew/HandlerTemplate.ashx",
            data: parameter,
            success: function (data) {
                if (!data.IsError) {
                    printData.netPointSetting = data.Data;
                    if ($.isFunction(callback)) {
                        callback();
                    }
                } else {
                    console.error("comp.Print.data.saveBranchSetting is error" + data.Msg);
                    alert("保存网点电子面单的设置信息出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.saveBranchSetting is error！" + (textStatus || errorThrown));
                alert("保存网点电子面单的设置信息出错");
            }
        });
    };

    //编辑模板保存 无忌：2016.05.31
    dataObj.SaveTemplate = function (obj, callback) {
        if (obj.ModeListShow.Modeid == "kdd") {
            dataObj.SaveKddTemplate(obj,callback);
        }
        else {
            dataObj.SaveFhdTemplate(obj, callback);
        }
    };

    //编辑模板保存 无忌：2016.05.31
    dataObj.SaveKddTemplate = function (obj, callback) {
        var printData = comp.Print.Data;
        var that = new comp.Print.FN();
        var parameter = { action: "SaveTemplate", jsonParam: JSON.stringify(obj) };
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeListshow/saveTemplate",
            data: parameter,
            success: function (data) {
                data = changeData(data);
                if (!data.IsError) {
                    var index = comp.Print.Data.kddTempInfos.findIndex(function (item) {
                        if (item.ModeListShow.Mode_ListShowId == obj.ModeListShow.Mode_ListShowId) {
                            return true;
                        }
                        return false;
                    });
                    if (index >= 0) {
                        var arr = comp.Print.Data.kddTempInfos;
                        var oldObj = arr[index];
                        dataObj.addLogForEditKddTemplate(oldObj, obj);
                        arr.splice(index, 1, obj);
                        var modeName = obj.ModeListShow.ExcodeName;
                        var modeId = obj.ModeListShow.Mode_ListShowId;

                        var list = that.getKddList();
                        var index = list.findIndex(function (item) {
                            return item.Mode_ListShowId == modeId;
                        });
                        var lItem = list[index];
                        if (lItem.ExcodeName != modeName) {
                            lItem.ExcodeName = modeName;
                            var eventObj = comp.Print.eventObj;
                            var evs = eventObj.KddTemplateListChanged;
                            if (evs) {
                                evs.each(function (index, item) {
                                    item(list);
                                });
                            }
                        }
                    }
                    else {
                        console.error("application is error ,because of it fined ,but not find");
                    }
                    if ($.isFunction(callback)) {
                        callback();
                    }
                } else {
                    console.error("comp.Print.data.saveEditModel is error" + data.Msg);
                    alert("保存编辑模版出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.saveEditModel is error！" + (textStatus || errorThrown));
                alert("保存编辑模版出错");
            }
        });
    };

    dataObj.addLogForEditKddTemplate = function (oldObj, newObj) {
        var that = new comp.Print.FN();
        var oldjson = JSON.stringify(oldObj.ModeInputs);
        var newjson = JSON.stringify(newObj.ModeInputs);
        if (oldjson != newjson) {
            var otherInfo = {};
            otherInfo.logAction = comp.Print.Data.logAction;
            oldjson = JSON.stringify(oldObj);
            newjson = JSON.stringify(newObj);
            if (newObj.ModeList.KddType != 3) {
                return;
            }
            otherInfo.brower = that.getBrower();
            var jsonParam = {
                ExUserId: comp.Print.Data.exuid,
                ExSubId: comp.Print.Data.exsubuid,
                RequestBody: newjson,
                ResponseBody: oldjson,
                LogType: "SaveKddTemplate",
                RedunFieldText: JSON.stringify(otherInfo),
                RedunFieldString: newObj.ModeListShow.Mode_ListShowId
            };
            var parameter = {};
            parameter.action = "AddModeLog";
            parameter.jsonParam = JSON.stringify(jsonParam);
            parameter.exuserId = comp.Print.Data.exuid;
            $.ajax({
                type: "post",
                dataType: "json",
                url: "/modeLog/saveModeLog",
                data: extendPrintModeType(parameter),
                success: function (data) {
                    data = changeData(data);
                    if (data.IsError) {
                        console.error("comp.Print.data.addLogForEditKddTemplate is error！" + data.Msg);
                    }
                },
                error: function (request, textStatus, errorThrown) {
                    console.error("comp.Print.data.saveEditModel is error！" + (textStatus || errorThrown));
                }
            });
        }
    }

    //发货单保存
    dataObj.SaveFhdTemplate = function (obj, callback) {
        var printData = comp.Print.Data;
        var parameter = { action: "SaveFhdTemplate", jsonParam: JSON.stringify(obj) };
        $.ajax({
            type: "post",
            dataType: "json",
            url: "/modeListshow/saveFhdTemplate",
            data: parameter,
            success: function (data) {
                var data = changeData(data);
                if (!data.IsError) {
                    var index = comp.Print.Data.fhdTempInfos.findIndex(function (item) {
                        if (item.ModeListShow.Mode_ListShowId == obj.ModeListShow.Mode_ListShowId) {
                            return true;
                        }
                        return false;
                    });
                    if (index >= 0) {
                        var arr = comp.Print.Data.fhdTempInfos;
                        arr.splice(index,1,obj);
                        comp.Print.Data.globalSettingFhd.ModeSet = obj.ModeSet;
                    }
                    else {
                        console.error("application is error ,because of it fined ,but not find");
                    }
                    if ($.isFunction(callback)) {
                        callback();
                    }
                } else {
                    console.error("comp.Print.data.SaveFhdTemplate is error" + data.Msg);
                    alert("保存发货单详细信息出错");
                }
            },
            error: function (request, textStatus, errorThrown) {
                console.error("comp.Print.data.SaveFhdTemplate is error！" + (textStatus || errorThrown));
                alert("保存发货单详细信息出错");
            }
        });
    };

    //获取模拟数据
    dataObj.getPrintData = function (modeid) {
        var that = new comp.Print.FN();
        var obj = that.getInitPrintData();
        // 设置发件人信息
        obj.f_name = "发件人名称";
        obj.f_tel = "发件人电话";
        obj.f_addr = "发件人地址";
        obj.f_zip = "发件人邮编";
        obj.f_qm = "发件人签名";

        obj.f_ww = "卖家旺旺";
        //obj.logo = "卖家旺旺LOGO";
        obj.logow = "卖家旺旺LOGO宽度";
        obj.logoh = "卖家旺旺LOGO高度";
        var date = new Date();

        var yy = date.getFullYear();
        var mm = date.getMonth() + 1;
        var dd = date.getDate();
        mm = mm < 9 ? ("0" + mm) : mm;
        dd = dd < 9 ? ("0" + dd) : dd;
        obj.f_date = yy + "-" + mm + "-" + dd;
        // 发货单时设置发货单信息
        if (modeid == 'fhd') {
            obj.o_shop = "店铺名称";
            obj.o_title = "发货单标题";
            obj.o_tel = "联系电话";
            obj.o_name = "经办人";
            obj.o_info = "友情提示";
        }
        else {
            // 快递单时设置 发货信息
            obj.f_info = "发货信息";
        }

        // 收件人部分从页面上提取，用户可能做了编辑
        obj.s_name = "收件人姓名";
        obj.s_phone = "13388888888";
        obj.s_tel = "010-12345678";
        obj.s_p = "北京";
        obj.s_city = "北京市";
        obj.s_q = "测试区";
        obj.s_addr = "测试地址测试地址测试地址测试地址测试地址测试地址测试地址";
        obj.s_addrall = "北京市 测试区 测试地址测试地址测试地址测试地址测试地址测试地址测试地址";
        obj.s_zip = "123456";
        obj.s_ww = "买家旺旺";
        obj.xdate = "2000-10-10 12:00:00";
        obj.fdate = "2000-10-10 13:00:05";
        obj.mjs = "满就送内容";

        // 合计_数量
        obj.hj_sl = "10";
        obj.hj_yf = "0.00";
        obj.hj_yh = "65";
        obj.hj_sf = "1200.00";

        obj.txm_number = "8011110001";

        obj.fhdexnumber = "8011110001";

        // 临时变量，存储当前地址所有订单中对应的产品数据
        var tempPidAry = new Array();

        // 记录当前所有订单中对应的产品ID，有重复的只记录一个，用来把相同的产品放到一起
        var pidAry = new Array();

        /////将当前地址组中的多个订单中的以下数据项合并，合并前先把数据清除
        obj.count = "0";
        obj.s_tid = "";
        obj.lc = "";
        obj.dsje = "0";
        obj.ddje = "0";
        obj.hkzj = "0";
        obj.bjje = "0";
        obj.bjfy = "0";
        obj.f_memo = "";
        obj.s_message = "";

        // 个性目的地  (快递间用英文的;分隔，快递CODE与值间用英文的,分隔) TTKD,值;YTO,值
        obj.gx_mdd = "个性目的地";

        // 个性集包码   (快递间用英文的;分隔，快递CODE与值间用英文的,分隔) TTKD,值;YTO,值
        obj.gx_jbm = "个性集包码";

        // 条形码_集包码  (快递间用英文的;分隔，快递CODE与值间用英文的,分隔) TTKD,值;YTO,值
        obj.txm_jbm = "条形码_集包码";
        // 备注、留言处理
        obj.f_memo = "卖家备注的内容信息";
        obj.s_message = "买家留言的内容信息";
        // 二维码
        obj.ewm = "二维码";
        //字母件数量
        obj.zmj_sum = "字母件数量";
        //字母件子单号
        obj.zmj_mnum = "字母件子单号";
        //月结卡号
        obj.cutid = "月结单号";

        // 总数量，读取当前订单中包含的产品项，将每一项产品的数量相加(去掉退款中的数量)
        var newCount = 0;
        for (var j = 0 ; j < 4 ; j++) {
            var tb_count = 1;
            // 记录数量
            newCount += tb_count;
            // 记录产品数据
            tempPidAry[tempPidAry.length] = {};
            var temTitJ = j % 2;
            tempPidAry[tempPidAry.length - 1].tb_pid = "90000001" + temTitJ;
            tempPidAry[tempPidAry.length - 1].tb_img = "/resources/img/print/img.jpg";
            tempPidAry[tempPidAry.length - 1].tb_tit = "宝贝的标题" + temTitJ;
            tempPidAry[tempPidAry.length - 1].tb_jc = "宝贝的简称" + temTitJ;
          
            var temj = (j + 1) % 3;
            tempPidAry[tempPidAry.length - 1].tb_sjbm_b = "AX" + temj;
            tempPidAry[tempPidAry.length - 1].tb_sjbm_kh = "B0" + temj;
            tempPidAry[tempPidAry.length - 1].tb_cpgg = "红" + temj + "码";
            tempPidAry[tempPidAry.length - 1].tb_cpggAll = "颜色:红;产品规格:" + temj + "码";
            tempPidAry[tempPidAry.length - 1].tb_count = tb_count;
            tempPidAry[tempPidAry.length - 1].tb_dj = "126.50";
            tempPidAry[tempPidAry.length - 1].tb_yh = "6.50";
            tempPidAry[tempPidAry.length - 1].tb_sf = "120.00";
            tempPidAry[tempPidAry.length - 1].tb_fx_sf = "90.00";

            // 记录产品ID，如果已经记录过则不记录
            var ispid = false;
            for (var k = 0 ; k < pidAry.length ; k++) {
                if (pidAry[k] == tempPidAry[tempPidAry.length - 1].tb_pid) {
                    ispid = true;
                    break;
                }
            }
            if (!ispid) {
                pidAry[pidAry.length] = tempPidAry[tempPidAry.length - 1].tb_pid;
            }
        }

        obj.count = "数量";
        // 订单编号
        obj.s_tid = "订单编号";
        // LC编号
        obj.lc = "LC编号";
        // 代收金额
        obj.dsje = "代收金额";
        // 实付金额
        obj.ddje = "实付金额";
        // 货款总计
        obj.hkzj = "货款总计";

        // 将当前地址中的所有订单中的产品加入打印数据
        obj.tableDataArray = new Array();
        for (var k = 0; k < pidAry.length ; k++) {
            for (var i = 0 ; i < tempPidAry.length ; i++) {
                if (tempPidAry[i].tb_pid != pidAry[k]) {
                    continue;
                }
                var addi = obj.tableDataArray.length;
                obj.tableDataArray[addi] = {};
                obj.tableDataArray[addi].tb_pid = tempPidAry[i].tb_pid;
                obj.tableDataArray[addi].tb_img = tempPidAry[i].tb_img;
                obj.tableDataArray[addi].tb_tit = tempPidAry[i].tb_tit;
                obj.tableDataArray[addi].tb_jc = tempPidAry[i].tb_jc;
                obj.tableDataArray[addi].tb_sjbm_b = tempPidAry[i].tb_sjbm_b;
                obj.tableDataArray[addi].tb_sjbm_kh = tempPidAry[i].tb_sjbm_kh;
                obj.tableDataArray[addi].tb_cpgg = tempPidAry[i].tb_cpgg;
                obj.tableDataArray[addi].tb_cpggAll = tempPidAry[i].tb_cpggAll;
                obj.tableDataArray[addi].tb_count = tempPidAry[i].tb_count;
                obj.tableDataArray[addi].tb_dj = tempPidAry[i].tb_dj;
                obj.tableDataArray[addi].tb_yh = tempPidAry[i].tb_yh;
                obj.tableDataArray[addi].tb_sf = tempPidAry[i].tb_sf;
                obj.tableDataArray[addi].tb_fx_sf = tempPidAry[i].tb_fx_sf;
            }
        }
        obj.bgm = "/img/UserPackCode/packcode_" + comp.Print.Data.exuid + ".png";
        return obj;
    }

    ///////////////////////////////////////////////////////////////////////////

    //获取连打设置
    dataObj.getLDSet = function (mkdid) {
        var printData = comp.Print.Data;
        var retdata = null;
        for (var i = 0; i < printData.ldPtrintSets.length; i++) {
            if (mkddid == printData.ldPtrintSets[i].Mkddid) {
                retdata = printData.ldPtrintSets[i];
                break;
            }
        }
        return retdata;
    }

    //获取快递单设置
    dataObj.getKddSet = function (mkddid) {
        var printData = comp.Print.Data;
        var retdata = null;
        for (var i = 0; i < printData.kddArray.length; i++) {
            if (mkddid == printData.kddArray[i].mkddId) {
                retdata = printData.kddArray[i];
                break;
            }
        }
        return retdata;
    }

    //获取当前所选快递
    dataObj.getNowKddSet = function () {
        var printData = comp.Print.Data;
        var retdata = null;
        for (var i = 0; i < printData.kddArray.length; i++) {
            if (printData.now_kdd_mkddId == printData.kddArray[i].mkddId) {
                retdata = dataObj.kddArray[i];
                break;
            }
        }
        return retdata;
    }

    //获取菜鸟快递模版对象
    dataObj.getExTemp = function (mkddid) {
        var printData = comp.Print.Data;
        var extemp = null;
        if (printData.kddTempLate) {
            var length = printData.kddTempLate.length;
            for (var i = 0; i < length; i++) {
                if (printData.kddTempLate[i].Exshowid == mkddid) {
                    extemp = printData.kddTempLate[i];
                    return extemp;
                }
            }
        }

        if (extemp == null) {
            printData.kddTempLate = [];
            var parameter = {
                //action: "getextemp", ptype: printData.ptype, exuid: printData.exuid, uk: printData.uk
                exuid:printData.exuid,
                mkddId:extemp.modeListshowId
            };
            $.ajax({
                type: "Post",
                dataType: "json",
                url: "/modeListshow/getCainiaoTemplate",
                async: false,
                data: parameter,
                success: function (data) {
                    data = changeData(data);
                    if (!data.IsError) {
                        if (data.Data != null) {
                            extemp = data.Data;
                            printData.kddTempLate.push(extemp);
                        }
                        else {
                            alert("模板出错！请删除后重新添加。");
                        }
                    }
                    else {
                        alert(data.Msg);
                    }
                },
                error: function (request, textStatus, errorThrown) {
                    alert("服务器繁忙！");
                }
            });
        }
        return extemp;
    }

    //保存连打设置
    dataObj.saveLDSet = function (isfhd, mkddid, w, h, printName, a4Mode, a4Line, sheer, callback) {
        var printData = comp.Print.Data;
        var parameter = {};
        parameter["action"] = "saveldset";
        parameter["ptype"] = printData.ptype;
        parameter["exuid"] = printData.exuid;
        parameter["uk"] = printData.uk;
        parameter["isfhd"] = isfhd;
        parameter["mkddid"] = mkddid;
        parameter["w"] = w;
        parameter["h"] = h;
        parameter["printName"] = printName;
        parameter["a4mode"] = a4Mode;
        parameter["a4line"] = a4Line;
        parameter["sheer"] = sheer;
        $.ajax({
            type: "Post",
            dataType: "json",
            url: "/ashx/ZuJian/Print/LDSetCommon.ashx",
            data: parameter,
            success: function (data) {
                if (data.IsError) {
                    var ldPtrintSet = dataObj.getLDSet(mkddid);
                    ldPtrintSet.PrintName = printName.replace(/%2B/g, "+");
                    ldPtrintSet.Width = w;
                    ldPtrintSet.Height = h;
                    ldPtrintSet.Sheer = sheer;
                    if (isfhd == 1) {
                        ldPtrintSet.A4mode = a4Mode;
                        ldPtrintSet.A4line = a4Line;
                    }
                }
                if (typeof callback == 'function') {
                    callback(data);
                }
            },
            error: function () {
                alert("服务器繁忙！");
            }
        });
    }

    //连打设置开启关闭
    dataObj.updateLDStaus = function (status, callback) {
        var printData = comp.Print.Data;
        var parameter = {};
        parameter["action"] = "updatePrintSet";
        parameter["pttype"] = printData.Ptype;
        parameter["exuserid"] = printData.ExUid;
        parameter["isAutoPrint"] = staus;
        $.ajax({
            type: "Post",
            dataType: "json",
            url: "/ashx/ZuJian/orderTools/orderTools.ashx",
            data: parameter,
            success: function (data) {
                if (!data.IsError) {
                    status == 1 ? printData.LdSetOpen = true : printData.LdSetOpen = false;
                }
                if (typeof callback == 'function') {
                    callback(data);
                }
            },
            error: function () {
                alert("服务器繁忙！");
            }
        });
    };


     //模块初始化方法
    // dataObj.init = function (callback) {
    //     dataObj.initCallBack = callback;
    //     if (!ajaxInitExCompany) {
    //         dataObj.initExCompany(checkState);
    //     }

    //     if (!ajaxInitTemplates) {
    //         dataObj.initTemplates(checkState);
    //     }

    //     if (!ajaxGlobalSettingKdd) {
    //         dataObj.getKddGlobalSetting(comp.Print.Data.exuid, comp.Print.Data.exsubuid, checkState);
    //     }

    //     if (!ajaxGlobalSettingFhd) {
    //         dataObj.getFhdGlobalSetting(comp.Print.Data.exuid, comp.Print.Data.exsubuid, checkState);
    //     }

    //      if (!ajaxInitJhdSet) {
    //          dataObj.getJhdSet(checkState);
    //      }

    //     // if (!ajaxInitShopLogo) {
    //     //     dataObj.getShopLogo(checkState);
    //     // }
    // }


})(window);